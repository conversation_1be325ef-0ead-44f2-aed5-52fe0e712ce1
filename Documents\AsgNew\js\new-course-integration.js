/**
 * Integración del formulario New Course con la API ASG
 * 
 * Conecta el frontend HTML con los endpoints del backend
 */

document.addEventListener('DOMContentLoaded', function() {
    
    // Referencias a elementos del DOM
    const form = document.querySelector('#course-form');
    const saveButton = document.querySelector('#save-course');
    const saveDraftButton = document.querySelector('#save-draft');
    const imageUpload = document.querySelector('#course-image');
    const categorySelect = document.querySelector('#course-category');
    
    // Estado del formulario
    let courseData = {};
    let isDirty = false;
    
    /**
     * Inicializar la aplicación
     */
    function init() {
        console.log('🚀 Inicializando New Course...');
        
        // Cargar categorías
        loadCategories();
        
        // Configurar event listeners
        setupEventListeners();
        
        // Configurar auto-guardado
        setupAutoSave();
        
        console.log('✅ New Course inicializado');
    }
    
    /**
     * Cargar categorías desde la API
     */
    async function loadCategories() {
        try {
            const result = await courseAPI.getCategories();
            
            if (result.success && categorySelect) {
                // Limpiar opciones existentes
                categorySelect.innerHTML = '<option value="">Seleccionar categoría...</option>';
                
                // Agregar nuevas opciones
                Object.entries(result.data).forEach(([value, label]) => {
                    const option = document.createElement('option');
                    option.value = value;
                    option.textContent = label;
                    categorySelect.appendChild(option);
                });
                
                console.log('✅ Categorías cargadas');
            }
        } catch (error) {
            console.error('❌ Error cargando categorías:', error);
        }
    }
    
    /**
     * Configurar event listeners
     */
    function setupEventListeners() {
        // Botón guardar curso
        if (saveButton) {
            saveButton.addEventListener('click', handleSaveCourse);
        }
        
        // Botón guardar borrador
        if (saveDraftButton) {
            saveDraftButton.addEventListener('click', handleSaveDraft);
        }
        
        // Subida de imagen
        if (imageUpload) {
            imageUpload.addEventListener('change', handleImageUpload);
        }
        
        // Cambios en el formulario
        if (form) {
            form.addEventListener('input', handleFormChange);
            form.addEventListener('change', handleFormChange);
        }
        
        // Validación en tiempo real
        setupRealTimeValidation();
    }
    
    /**
     * Manejar guardado de curso
     */
    async function handleSaveCourse(event) {
        event.preventDefault();
        
        try {
            // Mostrar loading
            showLoading(saveButton, 'Guardando curso...');
            
            // Recopilar datos del formulario
            const formData = collectFormData();
            formData.status = 'published';
            
            // Validar datos
            const validation = await courseAPI.validateCourse(formData);
            
            if (!validation.success) {
                return;
            }
            
            // Crear curso
            const result = await courseAPI.createCourse(formData);
            
            if (result.success) {
                // Redirigir a la lista de cursos o mostrar éxito
                showSuccessAndRedirect(result.data);
            }
            
        } catch (error) {
            console.error('❌ Error guardando curso:', error);
        } finally {
            hideLoading(saveButton, 'Guardar Curso');
        }
    }
    
    /**
     * Manejar guardado de borrador
     */
    async function handleSaveDraft(event) {
        event.preventDefault();
        
        try {
            // Mostrar loading
            showLoading(saveDraftButton, 'Guardando borrador...');
            
            // Recopilar datos del formulario
            const formData = collectFormData();
            formData.status = 'draft';
            
            // Guardar borrador
            const result = await courseAPI.saveDraft(formData);
            
            if (result.success) {
                isDirty = false;
                showSuccessMessage('Borrador guardado exitosamente');
            }
            
        } catch (error) {
            console.error('❌ Error guardando borrador:', error);
        } finally {
            hideLoading(saveDraftButton, 'Guardar Borrador');
        }
    }
    
    /**
     * Manejar subida de imagen
     */
    async function handleImageUpload(event) {
        const file = event.target.files[0];
        
        if (!file) return;
        
        try {
            // Mostrar preview
            showImagePreview(file);
            
            // Subir imagen
            const result = await courseAPI.uploadImage(file, 'course');
            
            if (result.success) {
                // Guardar URL de la imagen
                courseData.featured_image_url = result.data.original;
                
                // Actualizar preview con la imagen subida
                updateImagePreview(result.data.sizes.medium);
                
                console.log('✅ Imagen subida:', result.data);
            }
            
        } catch (error) {
            console.error('❌ Error subiendo imagen:', error);
        }
    }
    
    /**
     * Recopilar datos del formulario
     */
    function collectFormData() {
        const formData = new FormData(form);
        const data = {};
        
        // Campos básicos
        data.title = formData.get('title') || '';
        data.description = formData.get('description') || '';
        data.price = parseFloat(formData.get('price')) || 0;
        data.category = formData.get('category') || 'business';
        data.level = formData.get('level') || 'beginner';
        data.duration = parseFloat(formData.get('duration')) || 0;
        data.language = formData.get('language') || 'es';
        
        // Módulos
        data.modules = collectModules();
        
        // Objetivos de aprendizaje
        data.learn_objectives = collectLearnObjectives();
        
        // Beneficios
        data.benefits = collectBenefits();
        
        // Imagen destacada
        if (courseData.featured_image_url) {
            data.featured_image_url = courseData.featured_image_url;
        }
        
        return data;
    }
    
    /**
     * Recopilar módulos del curso
     */
    function collectModules() {
        const modules = [];
        const moduleElements = document.querySelectorAll('.module-item');
        
        moduleElements.forEach((element, index) => {
            const title = element.querySelector('[name="module_title"]')?.value || '';
            const description = element.querySelector('[name="module_description"]')?.value || '';
            const duration = parseInt(element.querySelector('[name="module_duration"]')?.value) || 0;
            
            if (title.trim()) {
                modules.push({
                    title: title.trim(),
                    description: description.trim(),
                    duration: duration
                });
            }
        });
        
        return modules;
    }
    
    /**
     * Recopilar objetivos de aprendizaje
     */
    function collectLearnObjectives() {
        const objectives = [];
        const objectiveElements = document.querySelectorAll('.objective-item input');
        
        objectiveElements.forEach(element => {
            const value = element.value.trim();
            if (value) {
                objectives.push(value);
            }
        });
        
        return objectives;
    }
    
    /**
     * Recopilar beneficios
     */
    function collectBenefits() {
        const benefits = [];
        const benefitElements = document.querySelectorAll('.benefit-item input');
        
        benefitElements.forEach(element => {
            const value = element.value.trim();
            if (value) {
                benefits.push(value);
            }
        });
        
        return benefits;
    }
    
    /**
     * Configurar validación en tiempo real
     */
    function setupRealTimeValidation() {
        const requiredFields = ['title', 'description', 'price'];
        
        requiredFields.forEach(fieldName => {
            const field = document.querySelector(`[name="${fieldName}"]`);
            if (field) {
                field.addEventListener('blur', () => validateField(fieldName, field.value));
            }
        });
    }
    
    /**
     * Validar campo individual
     */
    async function validateField(fieldName, value) {
        const tempData = { [fieldName]: value };
        
        try {
            await courseAPI.validateCourse(tempData);
            clearFieldError(fieldName);
        } catch (error) {
            // Los errores se manejan automáticamente en courseAPI
        }
    }
    
    /**
     * Limpiar error de campo
     */
    function clearFieldError(fieldName) {
        const errorElement = document.querySelector(`#${fieldName}-error`);
        const inputElement = document.querySelector(`[name="${fieldName}"]`);
        
        if (errorElement) {
            errorElement.style.display = 'none';
        }
        
        if (inputElement) {
            inputElement.classList.remove('error');
        }
    }
    
    /**
     * Configurar auto-guardado
     */
    function setupAutoSave() {
        setInterval(() => {
            if (isDirty) {
                autoSaveDraft();
            }
        }, 30000); // Auto-guardar cada 30 segundos
    }
    
    /**
     * Auto-guardar borrador
     */
    async function autoSaveDraft() {
        try {
            const formData = collectFormData();
            
            if (formData.title.trim()) {
                await courseAPI.saveDraft(formData);
                isDirty = false;
                console.log('💾 Auto-guardado realizado');
            }
        } catch (error) {
            console.error('❌ Error en auto-guardado:', error);
        }
    }
    
    /**
     * Manejar cambios en el formulario
     */
    function handleFormChange() {
        isDirty = true;
        courseAPI.clearValidationErrors();
    }
    
    /**
     * Mostrar loading en botón
     */
    function showLoading(button, text) {
        button.disabled = true;
        button.innerHTML = `<i class="bi bi-hourglass-split me-2"></i>${text}`;
    }
    
    /**
     * Ocultar loading en botón
     */
    function hideLoading(button, originalText) {
        button.disabled = false;
        button.innerHTML = originalText;
    }
    
    /**
     * Mostrar preview de imagen
     */
    function showImagePreview(file) {
        const reader = new FileReader();
        reader.onload = function(e) {
            const preview = document.querySelector('#image-preview');
            if (preview) {
                preview.src = e.target.result;
                preview.style.display = 'block';
            }
        };
        reader.readAsDataURL(file);
    }
    
    /**
     * Actualizar preview con imagen subida
     */
    function updateImagePreview(imageUrl) {
        const preview = document.querySelector('#image-preview');
        if (preview) {
            preview.src = imageUrl;
        }
    }
    
    /**
     * Mostrar éxito y redirigir
     */
    function showSuccessAndRedirect(courseData) {
        showSuccessMessage(`Curso "${courseData.title}" creado exitosamente`);
        
        setTimeout(() => {
            window.location.href = 'https://abilityseminarsgroup.com/all-courses/';
        }, 2000);
    }
    
    /**
     * Mostrar mensaje de éxito
     */
    function showSuccessMessage(message) {
        // Implementar según tu sistema de notificaciones
        console.log('✅', message);
        alert(message); // Temporal - reemplazar con toast
    }
    
    // Inicializar cuando el DOM esté listo
    init();
});
