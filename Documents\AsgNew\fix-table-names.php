<?php
/**
 * Script para corregir los nombres de las tablas en asg-course-endpoints.php
 * 
 * Cambiar de prefijos asg_ a wpic_ para coincidir con la base de datos real
 */

// Mapeo de nombres de tablas
$table_mapping = [
    // Tabla principal de cursos
    "wpdb->prefix . 'asg_courses'" => "wpdb->prefix . 'courses'",
    
    // Tablas relacionadas
    "wpdb->prefix . 'asg_course_modules'" => "wpdb->prefix . 'modules'",
    "wpdb->prefix . 'asg_course_meta'" => "wpdb->prefix . 'course_meta'",
    "wpdb->prefix . 'asg_course_objectives'" => "wpdb->prefix . 'learn_list'",
    "wpdb->prefix . 'asg_course_learn_objectives'" => "wpdb->prefix . 'learn_list'",
    "wpdb->prefix . 'asg_course_benefits'" => "wpdb->prefix . 'benefit_list'",
    "wpdb->prefix . 'asg_lessons'" => "wpdb->prefix . 'lessons'",
    
    // Referencias directas con prefix
    '{$wpdb->prefix}asg_courses' => '{$wpdb->prefix}courses',
    '{$wpdb->prefix}asg_course_modules' => '{$wpdb->prefix}modules',
    '{$wpdb->prefix}asg_course_meta' => '{$wpdb->prefix}course_meta',
    '{$wpdb->prefix}asg_course_objectives' => '{$wpdb->prefix}learn_list',
    '{$wpdb->prefix}asg_course_learn_objectives' => '{$wpdb->prefix}learn_list',
    '{$wpdb->prefix}asg_course_benefits' => '{$wpdb->prefix}benefit_list',
    '{$wpdb->prefix}asg_lessons' => '{$wpdb->prefix}lessons'
];

echo "🔧 Mapeo de correcciones de tablas:\n";
foreach ($table_mapping as $old => $new) {
    echo "  ❌ $old\n";
    echo "  ✅ $new\n\n";
}

echo "📋 Instrucciones:\n";
echo "1. Aplicar estos cambios manualmente en asg-course-endpoints.php\n";
echo "2. Buscar y reemplazar cada referencia de tabla\n";
echo "3. Verificar que las tablas existen en la base de datos\n";
echo "4. Probar los endpoints después de los cambios\n";

?>
