<?php
/**
 * ========================================
 * UTILIDADES API - SISTEMA ASG v2.0
 * ========================================
 * 
 * Descripción: Funciones auxiliares y utilidades para la API
 * Incluye: Validaciones, sanitización, helpers de base de datos
 * 
 * Autor: ASG Team
 * Fecha: 2025-01-04
 * Versión: 2.0.0
 */

// Prevenir acceso directo
if (!defined('ABSPATH')) {
    exit('Acceso directo no permitido');
}

/**
 * ========================================
 * FUNCIONES DE VALIDACIÓN
 * ========================================
 */

/**
 * Validar datos de curso
 */
function asg_validate_course_data($data, $is_update = false) {
    $errors = array();
    
    // Validaciones obligatorias para creación
    if (!$is_update) {
        if (empty($data['name_course'])) {
            $errors[] = 'El título del curso es obligatorio';
        }
    }
    
    // Validaciones de formato
    if (isset($data['name_course']) && strlen($data['name_course']) > 255) {
        $errors[] = 'El título del curso no puede exceder 255 caracteres';
    }
    
    if (isset($data['price_course']) && (!is_numeric($data['price_course']) || $data['price_course'] < 0)) {
        $errors[] = 'El precio debe ser un número positivo';
    }
    
    if (isset($data['duration_course']) && (!is_numeric($data['duration_course']) || $data['duration_course'] < 0)) {
        $errors[] = 'La duración debe ser un número positivo';
    }
    
    if (isset($data['status_course']) && !in_array($data['status_course'], ['draft', 'published', 'archived'])) {
        $errors[] = 'Estado de curso inválido';
    }
    
    if (isset($data['language_course']) && !in_array($data['language_course'], ['es', 'en', 'fr', 'pt'])) {
        $errors[] = 'Idioma no soportado';
    }
    
    // Validar URL de imagen si se proporciona
    if (isset($data['cover_img']) && !empty($data['cover_img']) && !filter_var($data['cover_img'], FILTER_VALIDATE_URL)) {
        $errors[] = 'URL de imagen inválida';
    }
    
    return $errors;
}

/**
 * Validar datos de módulo
 */
function asg_validate_module_data($data, $is_update = false) {
    $errors = array();
    
    if (!$is_update && empty($data['title_module'])) {
        $errors[] = 'El título del módulo es obligatorio';
    }
    
    if (isset($data['title_module']) && strlen($data['title_module']) > 255) {
        $errors[] = 'El título del módulo no puede exceder 255 caracteres';
    }
    
    if (isset($data['duration_module']) && (!is_numeric($data['duration_module']) || $data['duration_module'] < 0)) {
        $errors[] = 'La duración del módulo debe ser un número positivo';
    }
    
    if (isset($data['order_module']) && (!is_numeric($data['order_module']) || $data['order_module'] < 0)) {
        $errors[] = 'El orden del módulo debe ser un número positivo';
    }
    
    return $errors;
}

/**
 * Validar datos de lección
 */
function asg_validate_lesson_data($data, $is_update = false) {
    $errors = array();
    
    if (!$is_update && empty($data['title_lesson'])) {
        $errors[] = 'El título de la lección es obligatorio';
    }
    
    if (isset($data['title_lesson']) && strlen($data['title_lesson']) > 255) {
        $errors[] = 'El título de la lección no puede exceder 255 caracteres';
    }
    
    if (isset($data['lesson_type']) && !in_array($data['lesson_type'], ['video', 'text', 'quiz', 'assignment'])) {
        $errors[] = 'Tipo de lección inválido';
    }
    
    if (isset($data['video_url']) && !empty($data['video_url']) && !filter_var($data['video_url'], FILTER_VALIDATE_URL)) {
        $errors[] = 'URL de video inválida';
    }
    
    return $errors;
}

/**
 * ========================================
 * FUNCIONES DE SANITIZACIÓN
 * ========================================
 */

/**
 * Sanitizar datos de curso
 */
function asg_sanitize_course_data($data) {
    $sanitized = array();
    
    // Campos de texto
    if (isset($data['name_course'])) {
        $sanitized['name_course'] = sanitize_text_field($data['name_course']);
    }
    
    if (isset($data['description_course'])) {
        $sanitized['description_course'] = wp_kses_post($data['description_course']);
    }
    
    if (isset($data['category_course'])) {
        $sanitized['category_course'] = sanitize_text_field($data['category_course']);
    }
    
    if (isset($data['language_course'])) {
        $sanitized['language_course'] = sanitize_text_field($data['language_course']);
    }
    
    if (isset($data['status_course'])) {
        $sanitized['status_course'] = sanitize_text_field($data['status_course']);
    }
    
    // URLs
    if (isset($data['cover_img'])) {
        $sanitized['cover_img'] = esc_url_raw($data['cover_img']);
    }
    
    // Números
    if (isset($data['price_course'])) {
        $sanitized['price_course'] = floatval($data['price_course']);
    }
    
    if (isset($data['duration_course'])) {
        $sanitized['duration_course'] = intval($data['duration_course']);
    }
    
    if (isset($data['featured_course'])) {
        $sanitized['featured_course'] = intval($data['featured_course']);
    }
    
    // Campos SEO
    if (isset($data['seo_title'])) {
        $sanitized['seo_title'] = sanitize_text_field($data['seo_title']);
    }
    
    if (isset($data['seo_description'])) {
        $sanitized['seo_description'] = sanitize_textarea_field($data['seo_description']);
    }
    
    if (isset($data['seo_keywords'])) {
        $sanitized['seo_keywords'] = sanitize_textarea_field($data['seo_keywords']);
    }
    
    return $sanitized;
}

/**
 * ========================================
 * FUNCIONES DE BASE DE DATOS
 * ========================================
 */

/**
 * Insertar objetivos de aprendizaje
 */
function asg_insert_course_objectives($course_code, $objectives) {
    global $wpdb;
    
    foreach ($objectives as $index => $objective) {
        $objective_code = asg_generate_objective_code($course_code);
        
        $objective_data = array(
            'code_learn' => $objective_code,
            'name_list' => sanitize_text_field($objective['name_list']),
            'icon_learn' => sanitize_text_field($objective['icon_learn'] ?? ''),
            'order_learn' => $index,
            'code_course' => $course_code,
            'date_list' => current_time('mysql')
        );
        
        $result = $wpdb->insert("{$wpdb->prefix}learn_list", $objective_data);
        
        if ($result === false) {
            throw new Exception('Error al insertar objetivo: ' . $objective['name_list']);
        }
    }
}

/**
 * Insertar beneficios del curso
 */
function asg_insert_course_benefits($course_code, $benefits) {
    global $wpdb;
    
    foreach ($benefits as $index => $benefit) {
        $benefit_code = asg_generate_benefit_code($course_code);
        
        $benefit_data = array(
            'code_benefit' => $benefit_code,
            'name_list' => sanitize_text_field($benefit['name_list']),
            'icon_benefit' => sanitize_text_field($benefit['icon_benefit'] ?? ''),
            'order_benefit' => $index,
            'code_course' => $course_code,
            'date_list' => current_time('mysql')
        );
        
        $result = $wpdb->insert("{$wpdb->prefix}benefit_list", $benefit_data);
        
        if ($result === false) {
            throw new Exception('Error al insertar beneficio: ' . $benefit['name_list']);
        }
    }
}

/**
 * Insertar metadatos del curso
 */
function asg_insert_course_metadata($course_code, $metadata) {
    global $wpdb;
    
    foreach ($metadata as $key => $meta_info) {
        $value = $meta_info['value'];
        $type = $meta_info['type'] ?? 'string';
        $is_public = $meta_info['is_public'] ?? true;
        
        // Convertir valor según tipo
        if (in_array($type, ['json', 'array'])) {
            $value = json_encode($value);
        }
        
        $meta_data = array(
            'code_course' => $course_code,
            'meta_key' => sanitize_key($key),
            'meta_value' => $value,
            'meta_type' => $type,
            'is_public' => $is_public ? 1 : 0,
            'created_at' => current_time('mysql')
        );
        
        $result = $wpdb->insert("{$wpdb->prefix}course_meta", $meta_data);
        
        if ($result === false) {
            throw new Exception('Error al insertar metadato: ' . $key);
        }
    }
}

/**
 * Insertar lecciones de módulo
 */
function asg_insert_module_lessons($module_code, $course_code, $lessons) {
    global $wpdb;
    
    foreach ($lessons as $index => $lesson) {
        $lesson_code = asg_generate_lesson_code($module_code);
        
        $lesson_data = array(
            'code_lesson' => $lesson_code,
            'title_lesson' => sanitize_text_field($lesson['title_lesson']),
            'description_lesson' => wp_kses_post($lesson['description_lesson'] ?? ''),
            'content_lesson' => wp_kses_post($lesson['content_lesson'] ?? ''),
            'video_url' => esc_url_raw($lesson['video_url'] ?? ''),
            'cover_img' => esc_url_raw($lesson['cover_img'] ?? ''),
            'duration_lesson' => intval($lesson['duration_lesson'] ?? 0),
            'order_lesson' => $index,
            'lesson_type' => sanitize_text_field($lesson['lesson_type'] ?? 'video'),
            'is_preview' => intval($lesson['is_preview'] ?? 0),
            'code_module' => $module_code,
            'code_course' => $course_code,
            'id_user' => get_current_user_id() ?: 1,
            'date_lesson' => current_time('mysql')
        );
        
        $result = $wpdb->insert("{$wpdb->prefix}lessons", $lesson_data);
        
        if ($result === false) {
            throw new Exception('Error al insertar lección: ' . $lesson['title_lesson']);
        }
    }
}

/**
 * ========================================
 * GENERADORES DE CÓDIGOS ÚNICOS
 * ========================================
 */

/**
 * Generar código único para objetivo
 */
function asg_generate_objective_code($course_code) {
    global $wpdb;
    
    $course_number = str_replace('course_', '', $course_code);
    
    $last_objective = $wpdb->get_var($wpdb->prepare("
        SELECT code_learn FROM {$wpdb->prefix}learn_list 
        WHERE code_course = %s 
        ORDER BY id_learn DESC LIMIT 1
    ", $course_code));
    
    if ($last_objective) {
        $parts = explode('_', $last_objective);
        $last_number = intval(end($parts));
        $new_number = $last_number + 1;
    } else {
        $new_number = 1;
    }
    
    return "objective_{$course_number}_{$new_number}";
}

/**
 * Generar código único para beneficio
 */
function asg_generate_benefit_code($course_code) {
    global $wpdb;
    
    $course_number = str_replace('course_', '', $course_code);
    
    $last_benefit = $wpdb->get_var($wpdb->prepare("
        SELECT code_benefit FROM {$wpdb->prefix}benefit_list 
        WHERE code_course = %s 
        ORDER BY id_benefit DESC LIMIT 1
    ", $course_code));
    
    if ($last_benefit) {
        $parts = explode('_', $last_benefit);
        $last_number = intval(end($parts));
        $new_number = $last_number + 1;
    } else {
        $new_number = 1;
    }
    
    return "benefit_{$course_number}_{$new_number}";
}

/**
 * Generar código único para lección
 */
function asg_generate_lesson_code($module_code) {
    global $wpdb;
    
    $last_lesson = $wpdb->get_var($wpdb->prepare("
        SELECT code_lesson FROM {$wpdb->prefix}lessons 
        WHERE code_module = %s 
        ORDER BY id_lesson DESC LIMIT 1
    ", $module_code));
    
    if ($last_lesson) {
        $parts = explode('_', $last_lesson);
        $last_number = intval(end($parts));
        $new_number = $last_number + 1;
    } else {
        $new_number = 1;
    }
    
    return $module_code . "_lesson_{$new_number}";
}

/**
 * ========================================
 * FUNCIONES DE RESPUESTA
 * ========================================
 */

/**
 * Crear respuesta de error estándar
 */
function asg_error_response($code, $message, $status = 400, $data = null) {
    $response = array(
        'success' => false,
        'error' => array(
            'code' => $code,
            'message' => $message,
            'timestamp' => current_time('mysql')
        )
    );
    
    if ($data !== null) {
        $response['error']['data'] = $data;
    }
    
    return new WP_Error($code, $message, array('status' => $status, 'response' => $response));
}

/**
 * Crear respuesta de éxito estándar
 */
function asg_success_response($data, $message = null, $status = 200) {
    $response = array(
        'success' => true,
        'data' => $data,
        'timestamp' => current_time('mysql')
    );
    
    if ($message !== null) {
        $response['message'] = $message;
    }
    
    return new WP_REST_Response($response, $status);
}
