<?php
/**
 * ASG All Courses Page - WordPress Code Snippet
 * 
 * Descripción: Crea la página de gestión de todos los cursos
 * Versión: 2.0.0
 * Autor: AbilitySeminarsGroup
 */

// Evitar acceso directo
if (!defined('ABSPATH')) {
    exit;
}

/**
 * Crear página de todos los cursos
 */
function asg_create_all_courses_page() {
    // Verificar permisos
    if (!current_user_can('manage_options')) {
        wp_die(__('No tienes permisos para acceder a esta página.'));
    }
    
    // Obtener URL base
    $site_url = get_site_url();
    ?>
    <!DOCTYPE html>
    <html lang="es">
    <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>Todos los Cursos - ASG - <?php echo get_bloginfo('name'); ?></title>
        <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.3/dist/css/bootstrap.min.css" rel="stylesheet">
        <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.11.3/font/bootstrap-icons.css">
        <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
        
        <style>
            * {
                margin: 0;
                padding: 0;
                box-sizing: border-box;
            }

            body {
                font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
                background-color: #f8f9fa;
                overflow-x: hidden;
            }

            /* Navbar Styles */
            .navbar {
                background: linear-gradient(135deg, #1e3a5f 0%, #2563eb 100%);
                height: 70px;
                position: fixed;
                top: 0;
                left: 0;
                right: 0;
                z-index: 1000;
                box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            }

            .navbar-brand img {
                width: 130px;
                height: auto;
            }

            .navbar-nav .nav-link {
                color: white !important;
                font-weight: 500;
                padding: 0.5rem 1rem;
                border-radius: 6px;
                transition: all 0.3s ease;
            }

            .navbar-nav .nav-link:hover {
                background-color: rgba(255,255,255,0.1);
                transform: translateY(-1px);
            }

            .navbar-nav .nav-link.active {
                background-color: rgba(255,255,255,0.2);
            }

            /* Main Content */
            .main-content {
                margin-top: 70px;
                padding: 2rem;
                min-height: calc(100vh - 70px);
            }

            /* Page Header */
            .page-header {
                background: white;
                border-radius: 12px;
                padding: 2rem;
                margin-bottom: 2rem;
                box-shadow: 0 2px 10px rgba(0,0,0,0.05);
                border: 1px solid #e9ecef;
            }

            .page-title {
                font-size: 2rem;
                font-weight: 700;
                color: #1e3a5f;
                margin-bottom: 0.5rem;
            }

            .page-subtitle {
                color: #6c757d;
                font-size: 1.1rem;
            }

            /* Filters Section */
            .filters-section {
                background: white;
                border-radius: 12px;
                padding: 1.5rem;
                margin-bottom: 2rem;
                box-shadow: 0 2px 10px rgba(0,0,0,0.05);
                border: 1px solid #e9ecef;
            }

            .filter-row {
                display: grid;
                grid-template-columns: 2fr 1fr 1fr 1fr auto;
                gap: 1rem;
                align-items: end;
            }

            .filter-group label {
                font-weight: 500;
                color: #374151;
                margin-bottom: 0.5rem;
                display: block;
            }

            .filter-input {
                width: 100%;
                padding: 0.75rem;
                border: 1px solid #d1d5db;
                border-radius: 6px;
                font-size: 0.9rem;
                transition: border-color 0.3s ease;
            }

            .filter-input:focus {
                outline: none;
                border-color: #2563eb;
                box-shadow: 0 0 0 3px rgba(37, 99, 235, 0.1);
            }

            /* Stats Bar */
            .stats-bar {
                background: white;
                border-radius: 12px;
                padding: 1rem 1.5rem;
                margin-bottom: 2rem;
                box-shadow: 0 2px 10px rgba(0,0,0,0.05);
                border: 1px solid #e9ecef;
                display: flex;
                justify-content: space-between;
                align-items: center;
            }

            .stats-info {
                display: flex;
                gap: 2rem;
                align-items: center;
            }

            .stat-item {
                display: flex;
                align-items: center;
                gap: 0.5rem;
                font-size: 0.9rem;
                color: #6b7280;
            }

            .stat-number {
                font-weight: 600;
                color: #1f2937;
            }

            /* View Toggle */
            .view-toggle {
                display: flex;
                background: #f3f4f6;
                border-radius: 6px;
                padding: 0.25rem;
            }

            .view-btn {
                padding: 0.5rem 1rem;
                border: none;
                background: transparent;
                border-radius: 4px;
                cursor: pointer;
                transition: all 0.3s ease;
                color: #6b7280;
            }

            .view-btn.active {
                background: white;
                color: #2563eb;
                box-shadow: 0 1px 3px rgba(0,0,0,0.1);
            }

            /* Courses Grid */
            .courses-container {
                background: white;
                border-radius: 12px;
                padding: 1.5rem;
                box-shadow: 0 2px 10px rgba(0,0,0,0.05);
                border: 1px solid #e9ecef;
            }

            .courses-grid {
                display: grid;
                grid-template-columns: repeat(auto-fill, minmax(320px, 1fr));
                gap: 1.5rem;
            }

            .courses-list {
                display: flex;
                flex-direction: column;
                gap: 1rem;
            }

            /* Course Card */
            .course-card {
                border: 1px solid #e5e7eb;
                border-radius: 12px;
                overflow: hidden;
                transition: all 0.3s ease;
                background: white;
                cursor: pointer;
            }

            .course-card:hover {
                transform: translateY(-2px);
                box-shadow: 0 8px 25px rgba(0,0,0,0.1);
                border-color: #2563eb;
            }

            .course-image {
                width: 100%;
                height: 180px;
                background-size: cover;
                background-position: center;
                position: relative;
            }

            .course-status-badge {
                position: absolute;
                top: 0.75rem;
                right: 0.75rem;
                padding: 0.25rem 0.75rem;
                border-radius: 20px;
                font-size: 0.75rem;
                font-weight: 500;
                text-transform: uppercase;
            }

            .status-published {
                background-color: #d1fae5;
                color: #065f46;
            }

            .status-draft {
                background-color: #fef3c7;
                color: #92400e;
            }

            .status-archived {
                background-color: #f3f4f6;
                color: #374151;
            }

            .course-content {
                padding: 1.25rem;
            }

            .course-category {
                display: inline-flex;
                align-items: center;
                gap: 0.5rem;
                font-size: 0.8rem;
                color: #6b7280;
                margin-bottom: 0.75rem;
            }

            .course-title {
                font-size: 1.1rem;
                font-weight: 600;
                color: #1f2937;
                margin-bottom: 0.5rem;
                line-height: 1.4;
            }

            .course-description {
                font-size: 0.9rem;
                color: #6b7280;
                line-height: 1.5;
                margin-bottom: 1rem;
                display: -webkit-box;
                -webkit-line-clamp: 2;
                -webkit-box-orient: vertical;
                overflow: hidden;
            }

            .course-meta {
                display: flex;
                justify-content: space-between;
                align-items: center;
                margin-bottom: 1rem;
                font-size: 0.85rem;
                color: #6b7280;
            }

            .course-stats {
                display: flex;
                gap: 1rem;
            }

            .course-price {
                font-size: 1.1rem;
                font-weight: 700;
                color: #2563eb;
            }

            .course-actions {
                display: flex;
                gap: 0.5rem;
                padding-top: 1rem;
                border-top: 1px solid #f3f4f6;
            }

            .btn-action {
                flex: 1;
                padding: 0.5rem;
                border: 1px solid #d1d5db;
                background: white;
                border-radius: 6px;
                font-size: 0.85rem;
                cursor: pointer;
                transition: all 0.3s ease;
                display: flex;
                align-items: center;
                justify-content: center;
                gap: 0.25rem;
            }

            .btn-action:hover {
                background: #f9fafb;
                border-color: #2563eb;
                color: #2563eb;
            }

            .btn-action.primary {
                background: #2563eb;
                color: white;
                border-color: #2563eb;
            }

            .btn-action.primary:hover {
                background: #1d4ed8;
            }

            /* Loading States */
            .loading-skeleton {
                background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
                background-size: 200% 100%;
                animation: loading 1.5s infinite;
                border-radius: 4px;
            }

            @keyframes loading {
                0% { background-position: 200% 0; }
                100% { background-position: -200% 0; }
            }

            /* Empty State */
            .empty-state {
                text-align: center;
                padding: 3rem 1rem;
                color: #6b7280;
            }

            .empty-state i {
                font-size: 4rem;
                margin-bottom: 1rem;
                color: #d1d5db;
            }

            /* Toast Animations */
            @keyframes slideIn {
                from { transform: translateX(100%); opacity: 0; }
                to { transform: translateX(0); opacity: 1; }
            }

            @keyframes slideOut {
                from { transform: translateX(0); opacity: 1; }
                to { transform: translateX(100%); opacity: 0; }
            }

            .spin {
                animation: spin 1s linear infinite;
            }

            @keyframes spin {
                from { transform: rotate(0deg); }
                to { transform: rotate(360deg); }
            }

            /* Responsive */
            @media (max-width: 768px) {
                .main-content {
                    padding: 1rem;
                }
                
                .page-header {
                    padding: 1.5rem;
                }
                
                .filter-row {
                    grid-template-columns: 1fr;
                    gap: 1rem;
                }
                
                .stats-bar {
                    flex-direction: column;
                    gap: 1rem;
                    align-items: stretch;
                }
                
                .stats-info {
                    justify-content: space-between;
                }
                
                .courses-grid {
                    grid-template-columns: 1fr;
                }
            }
        </style>
    </head>
    <body>
        <!-- Navbar -->
        <nav class="navbar navbar-expand-lg">
            <div class="container-fluid">
                <a class="navbar-brand" href="<?php echo $site_url; ?>/admin-dashboard/">
                    <img src="https://abilityseminarsgroup.com/wp-content/uploads/2023/09/Asset-6-4-768x355.png" alt="ASG Logo">
                </a>
                
                <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                    <span class="navbar-toggler-icon"></span>
                </button>
                
                <div class="collapse navbar-collapse" id="navbarNav">
                    <ul class="navbar-nav ms-auto">
                        <li class="nav-item">
                            <a class="nav-link" href="<?php echo $site_url; ?>/admin-dashboard/">
                                <i class="bi bi-speedometer2"></i> Dashboard
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link active" href="<?php echo $site_url; ?>/all-courses/">
                                <i class="bi bi-collection"></i> Todos los Cursos
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="<?php echo $site_url; ?>/new-course/">
                                <i class="bi bi-plus-circle"></i> Nuevo Curso
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="#" onclick="showNotification('⚙️ Configuración próximamente', 'info')">
                                <i class="bi bi-gear"></i> Configuración
                            </a>
                        </li>
                    </ul>
                </div>
            </div>
        </nav>

        <!-- Main Content -->
        <div class="main-content">
            <!-- Page Header -->
            <div class="page-header">
                <h1 class="page-title">📚 Todos los Cursos</h1>
                <p class="page-subtitle">Gestiona y organiza todos tus cursos desde un solo lugar</p>
                <div class="mt-3">
                    <button class="btn btn-primary" onclick="refreshCourses()">
                        <i class="bi bi-arrow-clockwise"></i> Actualizar
                    </button>
                    <button class="btn btn-success ms-2" onclick="window.location.href='<?php echo $site_url; ?>/new-course/'">
                        <i class="bi bi-plus-circle"></i> Nuevo Curso
                    </button>
                </div>
            </div>

            <!-- Courses Container -->
            <div class="courses-container">
                <div id="coursesGrid" class="courses-grid">
                    <!-- Loading skeleton -->
                    <div class="course-card">
                        <div class="loading-skeleton" style="height: 180px;"></div>
                        <div class="course-content">
                            <div class="loading-skeleton" style="height: 1rem; margin-bottom: 0.5rem;"></div>
                            <div class="loading-skeleton" style="height: 1.5rem; margin-bottom: 0.5rem;"></div>
                            <div class="loading-skeleton" style="height: 3rem; margin-bottom: 1rem;"></div>
                            <div class="loading-skeleton" style="height: 1rem;"></div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Scripts -->
        <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.3/dist/js/bootstrap.bundle.min.js"></script>

        <!-- JavaScript con API Real -->
        <script>
            // Configuración con API Real
            const ASG_CONFIG = {
                version: '2.0.0',
                apiUrl: '<?php echo $site_url; ?>/wp-json/asg/v1',
                siteUrl: '<?php echo $site_url; ?>',
                categories: {
                    finanzas: { label: 'Finanzas', icon: '💰' },
                    marketing: { label: 'Marketing', icon: '📱' },
                    'desarrollo-personal': { label: 'Desarrollo Personal', icon: '🧠' },
                    tecnologia: { label: 'Tecnología', icon: '💻' },
                    negocios: { label: 'Negocios', icon: '💼' },
                    salud: { label: 'Salud', icon: '🏥' }
                },
                states: {
                    draft: { label: 'Borrador', badge: 'status-draft' },
                    published: { label: 'Publicado', badge: 'status-published' },
                    archived: { label: 'Archivado', badge: 'status-archived' }
                }
            };

            // API Client con conexión real
            class ASGApiClient {
                constructor() {
                    this.baseUrl = ASG_CONFIG.apiUrl;
                    console.log('🚀 ASG API Client initialized with:', this.baseUrl);
                }

                async request(endpoint, options = {}) {
                    const url = `${this.baseUrl}${endpoint}`;
                    const defaultOptions = {
                        method: 'GET',
                        headers: {
                            'Content-Type': 'application/json',
                            'Accept': 'application/json',
                            'X-WP-Nonce': '<?php echo wp_create_nonce('wp_rest'); ?>'
                        }
                    };

                    try {
                        console.log(`📡 API Request: ${url}`);
                        const response = await fetch(url, { ...defaultOptions, ...options });

                        if (!response.ok) {
                            throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                        }

                        const data = await response.json();
                        console.log(`✅ API Success:`, data);
                        return data;
                    } catch (error) {
                        console.warn(`⚠️ API Error: ${error.message}`);
                        throw error;
                    }
                }

                async getCourses() {
                    try {
                        const response = await this.request('/courses');
                        return response.data || response;
                    } catch (error) {
                        console.warn('📚 API Error, using fallback courses');
                        return this.getFallbackCourses();
                    }
                }

                getFallbackCourses() {
                    return [
                        {
                            id: 1,
                            code: 'course_1',
                            title: 'Como hacerte millonario?',
                            description: 'Aprende las estrategias más efectivas para generar riqueza y alcanzar la libertad financiera.',
                            image: 'https://images.unsplash.com/photo-1579621970563-ebec7560ff3e?w=400&h=300&fit=crop',
                            price: 299.99,
                            status: 'published',
                            category: 'finanzas',
                            modules: 8,
                            lessons: 24,
                            students: 156
                        },
                        {
                            id: 2,
                            code: 'course_2',
                            title: 'Marketing Digital Avanzado',
                            description: 'Domina las técnicas más avanzadas del marketing digital y haz crecer tu negocio online.',
                            image: 'https://images.unsplash.com/photo-1460925895917-afdab827c52f?w=400&h=300&fit=crop',
                            price: 199.99,
                            status: 'published',
                            category: 'marketing',
                            modules: 6,
                            lessons: 18,
                            students: 89
                        },
                        {
                            id: 3,
                            code: 'course_3',
                            title: 'Desarrollo Personal Integral',
                            description: 'Transforma tu vida con técnicas probadas de crecimiento personal y liderazgo.',
                            image: 'https://images.unsplash.com/photo-1516321318423-f06f85e504b3?w=400&h=300&fit=crop',
                            price: 149.99,
                            status: 'draft',
                            category: 'desarrollo-personal',
                            modules: 4,
                            lessons: 12,
                            students: 0
                        }
                    ];
                }
            }

            const ASG_API = new ASGApiClient();

            // Utilidades
            const ASG_UTILS = {
                formatCurrency: (amount) => {
                    return new Intl.NumberFormat('es-ES', {
                        style: 'currency',
                        currency: 'EUR'
                    }).format(amount || 0);
                },
                getCategoryInfo: (code) => ASG_CONFIG.categories[code] || { label: code, icon: '📚' },
                getStatusInfo: (code) => ASG_CONFIG.states[code] || { label: code, badge: 'status-draft' }
            };

            // Notificaciones
            function showNotification(message, type = 'info') {
                const colors = { success: '#10b981', error: '#ef4444', warning: '#f59e0b', info: '#06b6d4' };
                const icons = { success: '✅', error: '❌', warning: '⚠️', info: 'ℹ️' };

                const toast = document.createElement('div');
                toast.style.cssText = `
                    position: fixed; top: 90px; right: 20px; background: ${colors[type]};
                    color: white; padding: 12px 16px; border-radius: 8px;
                    box-shadow: 0 4px 12px rgba(0,0,0,0.15); z-index: 9999;
                    animation: slideIn 0.3s ease-out; cursor: pointer; max-width: 400px;
                `;
                toast.innerHTML = `
                    <div style="display: flex; align-items: center; gap: 8px;">
                        <span>${icons[type]}</span><span>${message}</span><span style="opacity: 0.7; margin-left: auto;">×</span>
                    </div>
                `;

                toast.addEventListener('click', () => {
                    toast.style.animation = 'slideOut 0.3s ease-in';
                    setTimeout(() => document.body.removeChild(toast), 300);
                });

                document.body.appendChild(toast);
                setTimeout(() => {
                    if (toast.parentNode) {
                        toast.style.animation = 'slideOut 0.3s ease-in';
                        setTimeout(() => toast.parentNode && document.body.removeChild(toast), 300);
                    }
                }, 5000);
            }

            // Cargar cursos
            async function loadCourses() {
                try {
                    const courses = await ASG_API.getCourses();
                    renderCourses(courses);
                } catch (error) {
                    showNotification('❌ Error al cargar los cursos', 'error');
                }
            }

            function renderCourses(courses) {
                const container = document.getElementById('coursesGrid');

                if (!courses || courses.length === 0) {
                    container.innerHTML = `
                        <div style="grid-column: 1 / -1; text-align: center; padding: 3rem;">
                            <i class="bi bi-collection" style="font-size: 4rem; color: #d1d5db;"></i>
                            <h3>No hay cursos disponibles</h3>
                            <p>Crea tu primer curso para comenzar</p>
                            <button class="btn btn-primary" onclick="window.location.href='<?php echo $site_url; ?>/new-course/'">
                                <i class="bi bi-plus-circle"></i> Crear Curso
                            </button>
                        </div>
                    `;
                    return;
                }

                container.innerHTML = courses.map(course => {
                    const category = ASG_UTILS.getCategoryInfo(course.category);
                    const status = ASG_UTILS.getStatusInfo(course.status);

                    return `
                        <div class="course-card" onclick="editCourse('${course.code}')">
                            <div class="course-image" style="background-image: url('${course.image}')">
                                <div class="course-status-badge ${status.badge}">
                                    ${status.label}
                                </div>
                            </div>
                            <div class="course-content">
                                <div class="course-category">
                                    ${category.icon} ${category.label}
                                </div>
                                <h3 class="course-title">${course.title}</h3>
                                <p class="course-description">${course.description}</p>
                                <div class="course-meta">
                                    <div class="course-stats">
                                        <span><i class="bi bi-collection"></i> ${course.modules} módulos</span>
                                        <span><i class="bi bi-play-circle"></i> ${course.lessons} lecciones</span>
                                        ${course.students > 0 ? `<span><i class="bi bi-people"></i> ${course.students}</span>` : ''}
                                    </div>
                                    <div class="course-price">${ASG_UTILS.formatCurrency(course.price)}</div>
                                </div>
                                <div class="course-actions">
                                    <button class="btn-action primary" onclick="event.stopPropagation(); editCourse('${course.code}')">
                                        <i class="bi bi-pencil"></i> Editar
                                    </button>
                                    <button class="btn-action" onclick="event.stopPropagation(); viewCourse('${course.code}')">
                                        <i class="bi bi-eye"></i> Ver
                                    </button>
                                </div>
                            </div>
                        </div>
                    `;
                }).join('');
            }

            function editCourse(courseCode) {
                window.location.href = `<?php echo $site_url; ?>/edit-course/?course=${courseCode}`;
            }

            function viewCourse(courseCode) {
                showNotification(`👁️ Viendo curso: ${courseCode}`, 'info');
            }

            async function refreshCourses() {
                const btn = event.target;
                const originalText = btn.innerHTML;
                btn.innerHTML = '<i class="bi bi-arrow-clockwise spin"></i> Actualizando...';
                btn.disabled = true;

                try {
                    await loadCourses();
                    showNotification('✅ Cursos actualizados correctamente', 'success');
                } catch (error) {
                    showNotification('❌ Error al actualizar los cursos', 'error');
                } finally {
                    btn.innerHTML = originalText;
                    btn.disabled = false;
                }
            }

            // Inicialización
            document.addEventListener('DOMContentLoaded', function() {
                console.log('🚀 All Courses ASG WordPress v2.0.0 loaded');

                setTimeout(() => {
                    showNotification('📚 Sistema de gestión de cursos cargado', 'success');
                }, 500);

                setTimeout(() => {
                    loadCourses();
                }, 1000);
            });
        </script>
    </body>
    </html>
    <?php
}

/**
 * Registrar la página de todos los cursos
 */
function asg_register_all_courses_page() {
    $page_slug = 'all-courses';
    $page = get_page_by_path($page_slug);

    if (!$page) {
        $page_data = array(
            'post_title'    => 'Todos los Cursos ASG',
            'post_content'  => '[asg_all_courses]',
            'post_status'   => 'publish',
            'post_type'     => 'page',
            'post_name'     => $page_slug
        );

        wp_insert_post($page_data);
    }
}

/**
 * Shortcode para mostrar todos los cursos
 */
function asg_all_courses_shortcode($atts) {
    ob_start();
    asg_create_all_courses_page();
    return ob_get_clean();
}

// Registrar hooks
add_action('init', 'asg_register_all_courses_page');
add_shortcode('asg_all_courses', 'asg_all_courses_shortcode');

// Registrar ruta personalizada
add_action('init', function() {
    add_rewrite_rule('^all-courses/?$', 'index.php?asg_page=all_courses', 'top');
});

add_filter('query_vars', function($vars) {
    $vars[] = 'asg_page';
    return $vars;
});

add_action('template_redirect', function() {
    $asg_page = get_query_var('asg_page');
    if ($asg_page === 'all_courses') {
        asg_create_all_courses_page();
        exit;
    }
});

?>