<!DOCTYPE html>
<html lang="es">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Nuevo Curso - ASG</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.11.3/font/bootstrap-icons.css">
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background-color: #f8f9fa;
            overflow-x: hidden;
        }

        /* Navbar Styles */
        .navbar {
            background: linear-gradient(135deg, #1e3a5f 0%, #2563eb 100%);
            height: 70px;
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            z-index: 1000;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }

        .navbar-brand img {
            width: 130px;
            height: auto;
        }

        .navbar-nav .nav-link {
            color: white !important;
            font-weight: 500;
            padding: 0.5rem 1rem;
            border-radius: 6px;
            transition: all 0.3s ease;
        }

        .navbar-nav .nav-link:hover {
            background-color: rgba(255,255,255,0.1);
            transform: translateY(-1px);
        }

        .navbar-nav .nav-link.active {
            background-color: rgba(255,255,255,0.2);
        }

        /* Main Content */
        .main-content {
            margin-top: 70px;
            padding: 2rem;
            min-height: calc(100vh - 70px);
        }

        /* Page Header */
        .page-header {
            background: white;
            border-radius: 12px;
            padding: 2rem;
            margin-bottom: 2rem;
            box-shadow: 0 2px 10px rgba(0,0,0,0.05);
            border: 1px solid #e9ecef;
        }

        .page-title {
            font-size: 2rem;
            font-weight: 700;
            color: #1e3a5f;
            margin-bottom: 0.5rem;
        }

        .page-subtitle {
            color: #6c757d;
            font-size: 1.1rem;
        }

        /* Form Container */
        .form-container {
            background: white;
            border-radius: 12px;
            padding: 2rem;
            box-shadow: 0 2px 10px rgba(0,0,0,0.05);
            border: 1px solid #e9ecef;
        }

        /* Form Sections */
        .form-section {
            margin-bottom: 2rem;
            padding-bottom: 2rem;
            border-bottom: 1px solid #f1f3f4;
        }

        .form-section:last-child {
            border-bottom: none;
            margin-bottom: 0;
        }

        .section-title {
            font-size: 1.25rem;
            font-weight: 600;
            color: #1e3a5f;
            margin-bottom: 1rem;
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }

        .section-description {
            color: #6b7280;
            font-size: 0.9rem;
            margin-bottom: 1.5rem;
        }

        /* Form Groups */
        .form-group {
            margin-bottom: 1.5rem;
        }

        .form-group label {
            font-weight: 500;
            color: #374151;
            margin-bottom: 0.5rem;
            display: block;
        }

        .form-group .required {
            color: #ef4444;
        }

        .form-control {
            width: 100%;
            padding: 0.75rem;
            border: 1px solid #d1d5db;
            border-radius: 6px;
            font-size: 0.9rem;
            transition: all 0.3s ease;
            background: white;
        }

        .form-control:focus {
            outline: none;
            border-color: #2563eb;
            box-shadow: 0 0 0 3px rgba(37, 99, 235, 0.1);
        }

        .form-control.error {
            border-color: #ef4444;
            box-shadow: 0 0 0 3px rgba(239, 68, 68, 0.1);
        }

        .form-text {
            font-size: 0.8rem;
            color: #6b7280;
            margin-top: 0.25rem;
        }

        .error-message {
            font-size: 0.8rem;
            color: #ef4444;
            margin-top: 0.25rem;
            display: none;
        }

        /* Image Upload */
        .image-upload {
            border: 2px dashed #d1d5db;
            border-radius: 8px;
            padding: 2rem;
            text-align: center;
            cursor: pointer;
            transition: all 0.3s ease;
            background: #fafbfc;
        }

        .image-upload:hover {
            border-color: #2563eb;
            background: #f0f9ff;
        }

        .image-upload.dragover {
            border-color: #2563eb;
            background: #dbeafe;
        }

        .image-preview {
            max-width: 100%;
            max-height: 200px;
            border-radius: 8px;
            margin-top: 1rem;
        }

        .upload-text {
            color: #6b7280;
            margin-top: 0.5rem;
        }

        .upload-icon {
            font-size: 2rem;
            color: #9ca3af;
            margin-bottom: 0.5rem;
        }

        /* Grid Layout */
        .form-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 1.5rem;
        }

        .form-grid-3 {
            display: grid;
            grid-template-columns: 1fr 1fr 1fr;
            gap: 1rem;
        }

        /* Buttons */
        .btn-group {
            display: flex;
            gap: 1rem;
            justify-content: flex-end;
            margin-top: 2rem;
            padding-top: 2rem;
            border-top: 1px solid #f1f3f4;
        }

        .btn {
            padding: 0.75rem 1.5rem;
            border-radius: 6px;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.3s ease;
            border: none;
            display: inline-flex;
            align-items: center;
            gap: 0.5rem;
        }

        .btn-primary {
            background: #2563eb;
            color: white;
        }

        .btn-primary:hover {
            background: #1d4ed8;
            transform: translateY(-1px);
        }

        .btn-secondary {
            background: #6b7280;
            color: white;
        }

        .btn-secondary:hover {
            background: #4b5563;
        }

        .btn-outline {
            background: white;
            color: #6b7280;
            border: 1px solid #d1d5db;
        }

        .btn-outline:hover {
            background: #f9fafb;
            border-color: #2563eb;
            color: #2563eb;
        }

        .btn:disabled {
            opacity: 0.5;
            cursor: not-allowed;
            transform: none !important;
        }

        /* Progress Steps */
        .progress-steps {
            display: flex;
            justify-content: center;
            margin-bottom: 2rem;
        }

        .step {
            display: flex;
            align-items: center;
            gap: 0.5rem;
            padding: 0.5rem 1rem;
            border-radius: 20px;
            font-size: 0.85rem;
            font-weight: 500;
            margin: 0 0.5rem;
        }

        .step.active {
            background: #dbeafe;
            color: #1e40af;
        }

        .step.completed {
            background: #d1fae5;
            color: #065f46;
        }

        .step.pending {
            background: #f3f4f6;
            color: #6b7280;
        }

        /* Category Pills */
        .category-pills {
            display: flex;
            flex-wrap: wrap;
            gap: 0.5rem;
        }

        .category-pill {
            padding: 0.5rem 1rem;
            border: 1px solid #d1d5db;
            border-radius: 20px;
            cursor: pointer;
            transition: all 0.3s ease;
            font-size: 0.85rem;
            background: white;
        }

        .category-pill:hover {
            border-color: #2563eb;
            background: #f0f9ff;
        }

        .category-pill.selected {
            background: #2563eb;
            color: white;
            border-color: #2563eb;
        }

        /* Toast Animations */
        @keyframes slideIn {
            from { transform: translateX(100%); opacity: 0; }
            to { transform: translateX(0); opacity: 1; }
        }

        @keyframes slideOut {
            from { transform: translateX(0); opacity: 1; }
            to { transform: translateX(100%); opacity: 0; }
        }

        .spin {
            animation: spin 1s linear infinite;
        }

        @keyframes spin {
            from { transform: rotate(0deg); }
            to { transform: rotate(360deg); }
        }

        /* Responsive */
        @media (max-width: 768px) {
            .main-content {
                padding: 1rem;
            }
            
            .page-header {
                padding: 1.5rem;
            }
            
            .form-container {
                padding: 1.5rem;
            }
            
            .form-grid {
                grid-template-columns: 1fr;
            }
            
            .form-grid-3 {
                grid-template-columns: 1fr;
            }
            
            .btn-group {
                flex-direction: column;
            }
            
            .category-pills {
                justify-content: center;
            }
        }
    </style>
</head>
<body>
    <!-- Navbar -->
    <nav class="navbar navbar-expand-lg">
        <div class="container-fluid">
            <a class="navbar-brand" href="dashboard-inline.html">
                <img src="https://abilityseminarsgroup.com/wp-content/uploads/2023/09/Asset-6-4-768x355.png" alt="ASG Logo">
            </a>
            
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav ms-auto">
                    <li class="nav-item">
                        <a class="nav-link" href="dashboard-inline.html">
                            <i class="bi bi-speedometer2"></i> Dashboard
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="all-courses-inline.html">
                            <i class="bi bi-collection"></i> Todos los Cursos
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link active" href="new-course-inline.html">
                            <i class="bi bi-plus-circle"></i> Nuevo Curso
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="#" onclick="showNotification('⚙️ Configuración próximamente', 'info')">
                            <i class="bi bi-gear"></i> Configuración
                        </a>
                    </li>
                </ul>
            </div>
        </div>
    </nav>

    <!-- Main Content -->
    <div class="main-content">
        <!-- Page Header -->
        <div class="page-header">
            <h1 class="page-title">➕ Crear Nuevo Curso</h1>
            <p class="page-subtitle">Completa la información para crear un nuevo curso en la plataforma</p>
            
            <!-- Progress Steps -->
            <div class="progress-steps">
                <div class="step active" id="step1">
                    <i class="bi bi-info-circle"></i> Información Básica
                </div>
                <div class="step pending" id="step2">
                    <i class="bi bi-image"></i> Contenido
                </div>
                <div class="step pending" id="step3">
                    <i class="bi bi-gear"></i> Configuración
                </div>
            </div>
        </div>

        <!-- Form Container -->
        <div class="form-container">
            <form id="newCourseForm">
                <!-- Información Básica -->
                <div class="form-section" id="basicInfo">
                    <div class="section-title">
                        <i class="bi bi-info-circle"></i>
                        Información Básica
                    </div>
                    <div class="section-description">
                        Proporciona la información fundamental del curso que será visible para los estudiantes.
                    </div>

                    <div class="form-group">
                        <label for="courseTitle">Título del Curso <span class="required">*</span></label>
                        <input type="text" id="courseTitle" class="form-control" placeholder="Ej: Marketing Digital Avanzado" required>
                        <div class="form-text">Un título claro y atractivo que describa el contenido del curso</div>
                        <div class="error-message" id="courseTitleError"></div>
                    </div>

                    <div class="form-group">
                        <label for="courseDescription">Descripción <span class="required">*</span></label>
                        <textarea id="courseDescription" class="form-control" rows="4" placeholder="Describe qué aprenderán los estudiantes en este curso..." required></textarea>
                        <div class="form-text">Una descripción detallada que motive a los estudiantes a inscribirse</div>
                        <div class="error-message" id="courseDescriptionError"></div>
                    </div>

                    <div class="form-grid">
                        <div class="form-group">
                            <label for="courseCategory">Categoría <span class="required">*</span></label>
                            <div class="category-pills" id="categoryPills">
                                <!-- Se cargan dinámicamente -->
                            </div>
                            <input type="hidden" id="courseCategory" required>
                            <div class="error-message" id="courseCategoryError"></div>
                        </div>

                        <div class="form-group">
                            <label for="coursePrice">Precio (€) <span class="required">*</span></label>
                            <input type="number" id="coursePrice" class="form-control" placeholder="199.99" step="0.01" min="0" required>
                            <div class="form-text">Precio en euros (usar punto para decimales)</div>
                            <div class="error-message" id="coursePriceError"></div>
                        </div>
                    </div>
                </div>

                <!-- Contenido del Curso -->
                <div class="form-section" id="courseContent">
                    <div class="section-title">
                        <i class="bi bi-image"></i>
                        Contenido del Curso
                    </div>
                    <div class="section-description">
                        Agrega la imagen principal y define la estructura del curso.
                    </div>

                    <div class="form-group">
                        <label>Imagen Principal <span class="required">*</span></label>
                        <div class="image-upload" id="imageUpload">
                            <div class="upload-icon">
                                <i class="bi bi-cloud-upload"></i>
                            </div>
                            <div>
                                <strong>Haz clic para subir</strong> o arrastra una imagen aquí
                            </div>
                            <div class="upload-text">
                                Formatos: JPG, PNG, WebP (máx. 2MB)
                            </div>
                            <img id="imagePreview" class="image-preview" style="display: none;">
                        </div>
                        <input type="file" id="imageInput" accept="image/*" style="display: none;">
                        <div class="error-message" id="courseImageError"></div>
                    </div>

                    <div class="form-grid-3">
                        <div class="form-group">
                            <label for="courseModules">Número de Módulos</label>
                            <input type="number" id="courseModules" class="form-control" placeholder="6" min="1" max="50">
                            <div class="form-text">Cantidad de módulos del curso</div>
                        </div>

                        <div class="form-group">
                            <label for="courseLessons">Número de Lecciones</label>
                            <input type="number" id="courseLessons" class="form-control" placeholder="18" min="1" max="200">
                            <div class="form-text">Total de lecciones</div>
                        </div>

                        <div class="form-group">
                            <label for="courseDuration">Duración (horas)</label>
                            <input type="number" id="courseDuration" class="form-control" placeholder="12" min="1" max="500">
                            <div class="form-text">Duración estimada en horas</div>
                        </div>
                    </div>
                </div>

                <!-- Configuración -->
                <div class="form-section" id="courseSettings">
                    <div class="section-title">
                        <i class="bi bi-gear"></i>
                        Configuración
                    </div>
                    <div class="section-description">
                        Define el estado inicial y configuraciones adicionales del curso.
                    </div>

                    <div class="form-grid">
                        <div class="form-group">
                            <label for="courseStatus">Estado Inicial</label>
                            <select id="courseStatus" class="form-control">
                                <option value="draft">✏️ Borrador</option>
                                <option value="published">✅ Publicado</option>
                            </select>
                            <div class="form-text">Los borradores no son visibles para los estudiantes</div>
                        </div>

                        <div class="form-group">
                            <label for="courseCode">Código del Curso</label>
                            <input type="text" id="courseCode" class="form-control" placeholder="Se genera automáticamente" readonly>
                            <div class="form-text">Identificador único del curso</div>
                        </div>
                    </div>

                    <div class="form-group">
                        <label for="courseNotes">Notas Internas</label>
                        <textarea id="courseNotes" class="form-control" rows="3" placeholder="Notas para uso interno del equipo..."></textarea>
                        <div class="form-text">Información adicional para el equipo (no visible para estudiantes)</div>
                    </div>
                </div>

                <!-- Botones de Acción -->
                <div class="btn-group">
                    <button type="button" class="btn btn-outline" onclick="cancelCourse()">
                        <i class="bi bi-x-circle"></i> Cancelar
                    </button>
                    <button type="button" class="btn btn-secondary" onclick="saveDraft()">
                        <i class="bi bi-file-earmark"></i> Guardar Borrador
                    </button>
                    <button type="submit" class="btn btn-primary">
                        <i class="bi bi-check-circle"></i> Crear Curso
                    </button>
                </div>
            </form>
        </div>
    </div>

    <!-- Scripts -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.3/dist/js/bootstrap.bundle.min.js"></script>

    <!-- JavaScript Inline -->
    <script>
        // ========================================
        // ASG CONFIGURACIÓN INLINE
        // ========================================
        const ASG_CONFIG = {
            version: '2.0.0',
            name: 'AbilitySeminarsGroup Course Management',
            apiUrl: 'https://abilityseminarsgroup.com/wp-json/asg/v1',
            mockData: true,

            // Endpoints
            endpoints: {
                courses: '/courses',
                uploadImage: '/courses/upload-image',
                generateCode: '/courses/generate-code'
            },

            // Categorías
            categories: {
                finanzas: { label: 'Finanzas', icon: '💰', color: '#10b981' },
                marketing: { label: 'Marketing', icon: '📱', color: '#f59e0b' },
                'desarrollo-personal': { label: 'Desarrollo Personal', icon: '🧠', color: '#8b5cf6' },
                tecnologia: { label: 'Tecnología', icon: '💻', color: '#06b6d4' },
                negocios: { label: 'Negocios', icon: '💼', color: '#ef4444' },
                salud: { label: 'Salud', icon: '🏥', color: '#84cc16' }
            },

            // Validaciones
            validation: {
                title: { min: 5, max: 100 },
                description: { min: 20, max: 500 },
                price: { min: 0, max: 9999 },
                modules: { min: 1, max: 50 },
                lessons: { min: 1, max: 200 },
                duration: { min: 1, max: 500 },
                imageSize: 2 * 1024 * 1024 // 2MB
            }
        };

        // ========================================
        // VARIABLES GLOBALES
        // ========================================
        let courseData = {
            title: '',
            description: '',
            category: '',
            price: 0,
            image: null,
            modules: 1,
            lessons: 1,
            duration: 1,
            status: 'draft',
            code: '',
            notes: ''
        };

        let validationErrors = {};
        let isFormValid = false;

        // ========================================
        // UTILIDADES
        // ========================================
        const ASG_UTILS = {
            generateCourseCode: (title) => {
                const cleanTitle = title
                    .toLowerCase()
                    .replace(/[^a-z0-9\s]/g, '')
                    .replace(/\s+/g, '-')
                    .substring(0, 30);
                const timestamp = Date.now().toString(36);
                return `course_${cleanTitle}_${timestamp}`;
            },

            validateField: (fieldName, value) => {
                const validation = ASG_CONFIG.validation;

                switch (fieldName) {
                    case 'title':
                        if (!value || value.trim().length < validation.title.min) {
                            return `El título debe tener al menos ${validation.title.min} caracteres`;
                        }
                        if (value.length > validation.title.max) {
                            return `El título no puede exceder ${validation.title.max} caracteres`;
                        }
                        break;

                    case 'description':
                        if (!value || value.trim().length < validation.description.min) {
                            return `La descripción debe tener al menos ${validation.description.min} caracteres`;
                        }
                        if (value.length > validation.description.max) {
                            return `La descripción no puede exceder ${validation.description.max} caracteres`;
                        }
                        break;

                    case 'category':
                        if (!value) {
                            return 'Debes seleccionar una categoría';
                        }
                        break;

                    case 'price':
                        const price = parseFloat(value);
                        if (isNaN(price) || price < validation.price.min) {
                            return 'El precio debe ser un número válido mayor o igual a 0';
                        }
                        if (price > validation.price.max) {
                            return `El precio no puede exceder €${validation.price.max}`;
                        }
                        break;

                    case 'image':
                        if (!value) {
                            return 'Debes subir una imagen para el curso';
                        }
                        break;

                    case 'modules':
                        const modules = parseInt(value);
                        if (isNaN(modules) || modules < validation.modules.min || modules > validation.modules.max) {
                            return `Los módulos deben estar entre ${validation.modules.min} y ${validation.modules.max}`;
                        }
                        break;

                    case 'lessons':
                        const lessons = parseInt(value);
                        if (isNaN(lessons) || lessons < validation.lessons.min || lessons > validation.lessons.max) {
                            return `Las lecciones deben estar entre ${validation.lessons.min} y ${validation.lessons.max}`;
                        }
                        break;

                    case 'duration':
                        const duration = parseInt(value);
                        if (isNaN(duration) || duration < validation.duration.min || duration > validation.duration.max) {
                            return `La duración debe estar entre ${validation.duration.min} y ${validation.duration.max} horas`;
                        }
                        break;
                }

                return null;
            },

            formatFileSize: (bytes) => {
                if (bytes === 0) return '0 Bytes';
                const k = 1024;
                const sizes = ['Bytes', 'KB', 'MB', 'GB'];
                const i = Math.floor(Math.log(bytes) / Math.log(k));
                return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
            },

            debounce: (func, wait) => {
                let timeout;
                return function executedFunction(...args) {
                    const later = () => {
                        clearTimeout(timeout);
                        func(...args);
                    };
                    clearTimeout(timeout);
                    timeout = setTimeout(later, wait);
                };
            }
        };

        // ========================================
        // FUNCIONES DE INTERFAZ
        // ========================================
        function showNotification(message, type = 'info') {
            const toast = document.createElement('div');
            const colors = {
                success: '#10b981',
                error: '#ef4444',
                warning: '#f59e0b',
                info: '#06b6d4'
            };

            const icons = {
                success: '✅',
                error: '❌',
                warning: '⚠️',
                info: 'ℹ️'
            };

            toast.style.cssText = `
                position: fixed;
                top: 90px;
                right: 20px;
                background: ${colors[type] || colors.info};
                color: white;
                padding: 12px 16px;
                border-radius: 8px;
                box-shadow: 0 4px 12px rgba(0,0,0,0.15);
                z-index: 9999;
                animation: slideIn 0.3s ease-out;
                cursor: pointer;
                max-width: 400px;
            `;

            toast.innerHTML = `
                <div style="display: flex; align-items: center; gap: 8px;">
                    <span>${icons[type] || icons.info}</span>
                    <span>${message}</span>
                    <span style="opacity: 0.7; margin-left: auto;">×</span>
                </div>
            `;

            toast.addEventListener('click', () => {
                toast.style.animation = 'slideOut 0.3s ease-in';
                setTimeout(() => document.body.removeChild(toast), 300);
            });

            document.body.appendChild(toast);

            setTimeout(() => {
                if (toast.parentNode) {
                    toast.style.animation = 'slideOut 0.3s ease-in';
                    setTimeout(() => {
                        if (toast.parentNode) {
                            document.body.removeChild(toast);
                        }
                    }, 300);
                }
            }, 5000);
        }

        function showFieldError(fieldName, message) {
            const field = document.getElementById(fieldName);
            const errorElement = document.getElementById(fieldName + 'Error');

            if (field && errorElement) {
                field.classList.add('error');
                errorElement.textContent = message;
                errorElement.style.display = 'block';
                validationErrors[fieldName] = message;
            }
        }

        function clearFieldError(fieldName) {
            const field = document.getElementById(fieldName);
            const errorElement = document.getElementById(fieldName + 'Error');

            if (field && errorElement) {
                field.classList.remove('error');
                errorElement.style.display = 'none';
                delete validationErrors[fieldName];
            }
        }

        function validateForm() {
            validationErrors = {};

            // Validar campos requeridos
            const requiredFields = ['courseTitle', 'courseDescription', 'courseCategory', 'coursePrice'];

            requiredFields.forEach(fieldId => {
                const field = document.getElementById(fieldId);
                const fieldName = fieldId.replace('course', '').toLowerCase();
                let value = field.value;

                if (fieldId === 'courseCategory') {
                    value = courseData.category;
                }

                const error = ASG_UTILS.validateField(fieldName, value);
                if (error) {
                    showFieldError(fieldId, error);
                } else {
                    clearFieldError(fieldId);
                }
            });

            // Validar imagen
            if (!courseData.image) {
                showFieldError('courseImage', 'Debes subir una imagen para el curso');
            } else {
                clearFieldError('courseImage');
            }

            // Validar campos numéricos opcionales
            const numericFields = ['courseModules', 'courseLessons', 'courseDuration'];
            numericFields.forEach(fieldId => {
                const field = document.getElementById(fieldId);
                if (field.value) {
                    const fieldName = fieldId.replace('course', '').toLowerCase();
                    const error = ASG_UTILS.validateField(fieldName, field.value);
                    if (error) {
                        showFieldError(fieldId, error);
                    } else {
                        clearFieldError(fieldId);
                    }
                }
            });

            isFormValid = Object.keys(validationErrors).length === 0;
            return isFormValid;
        }

        // ========================================
        // FUNCIONES DE CATEGORÍAS
        // ========================================
        function renderCategories() {
            const container = document.getElementById('categoryPills');

            container.innerHTML = Object.entries(ASG_CONFIG.categories).map(([code, info]) => `
                <div class="category-pill" data-category="${code}" onclick="selectCategory('${code}')">
                    ${info.icon} ${info.label}
                </div>
            `).join('');
        }

        function selectCategory(categoryCode) {
            // Limpiar selección anterior
            document.querySelectorAll('.category-pill').forEach(pill => {
                pill.classList.remove('selected');
            });

            // Seleccionar nueva categoría
            const selectedPill = document.querySelector(`[data-category="${categoryCode}"]`);
            if (selectedPill) {
                selectedPill.classList.add('selected');
                courseData.category = categoryCode;
                document.getElementById('courseCategory').value = categoryCode;
                clearFieldError('courseCategory');
            }
        }

        // ========================================
        // FUNCIONES DE IMAGEN
        // ========================================
        function setupImageUpload() {
            const uploadArea = document.getElementById('imageUpload');
            const fileInput = document.getElementById('imageInput');
            const preview = document.getElementById('imagePreview');

            // Click para seleccionar archivo
            uploadArea.addEventListener('click', () => {
                fileInput.click();
            });

            // Drag and drop
            uploadArea.addEventListener('dragover', (e) => {
                e.preventDefault();
                uploadArea.classList.add('dragover');
            });

            uploadArea.addEventListener('dragleave', () => {
                uploadArea.classList.remove('dragover');
            });

            uploadArea.addEventListener('drop', (e) => {
                e.preventDefault();
                uploadArea.classList.remove('dragover');

                const files = e.dataTransfer.files;
                if (files.length > 0) {
                    handleImageFile(files[0]);
                }
            });

            // Cambio de archivo
            fileInput.addEventListener('change', (e) => {
                if (e.target.files.length > 0) {
                    handleImageFile(e.target.files[0]);
                }
            });
        }

        function handleImageFile(file) {
            // Validar tipo de archivo
            const validTypes = ['image/jpeg', 'image/png', 'image/webp'];
            if (!validTypes.includes(file.type)) {
                showFieldError('courseImage', 'Formato de imagen no válido. Usa JPG, PNG o WebP');
                return;
            }

            // Validar tamaño
            if (file.size > ASG_CONFIG.validation.imageSize) {
                showFieldError('courseImage', `La imagen es muy grande. Máximo ${ASG_UTILS.formatFileSize(ASG_CONFIG.validation.imageSize)}`);
                return;
            }

            // Mostrar preview
            const reader = new FileReader();
            reader.onload = (e) => {
                const preview = document.getElementById('imagePreview');
                preview.src = e.target.result;
                preview.style.display = 'block';

                courseData.image = file;
                clearFieldError('courseImage');

                showNotification(`✅ Imagen cargada: ${file.name}`, 'success');
            };

            reader.readAsDataURL(file);
        }

        // ========================================
        // FUNCIONES DE FORMULARIO
        // ========================================
        function updateCourseData() {
            courseData.title = document.getElementById('courseTitle').value;
            courseData.description = document.getElementById('courseDescription').value;
            courseData.price = parseFloat(document.getElementById('coursePrice').value) || 0;
            courseData.modules = parseInt(document.getElementById('courseModules').value) || 1;
            courseData.lessons = parseInt(document.getElementById('courseLessons').value) || 1;
            courseData.duration = parseInt(document.getElementById('courseDuration').value) || 1;
            courseData.status = document.getElementById('courseStatus').value;
            courseData.notes = document.getElementById('courseNotes').value;

            // Generar código si no existe
            if (!courseData.code && courseData.title) {
                courseData.code = ASG_UTILS.generateCourseCode(courseData.title);
                document.getElementById('courseCode').value = courseData.code;
            }
        }

        function setupFormValidation() {
            // Validación en tiempo real
            const titleInput = document.getElementById('courseTitle');
            const descriptionInput = document.getElementById('courseDescription');
            const priceInput = document.getElementById('coursePrice');

            const debouncedValidation = ASG_UTILS.debounce(() => {
                updateCourseData();
                validateForm();
            }, 500);

            [titleInput, descriptionInput, priceInput].forEach(input => {
                input.addEventListener('input', debouncedValidation);
            });

            // Generar código automáticamente
            titleInput.addEventListener('input', ASG_UTILS.debounce(() => {
                if (titleInput.value.length >= 3) {
                    const code = ASG_UTILS.generateCourseCode(titleInput.value);
                    document.getElementById('courseCode').value = code;
                    courseData.code = code;
                }
            }, 1000));
        }

        // ========================================
        // FUNCIONES DE ENVÍO
        // ========================================
        async function saveDraft() {
            updateCourseData();
            courseData.status = 'draft';

            if (!validateForm()) {
                showNotification('❌ Por favor corrige los errores antes de guardar', 'error');
                return;
            }

            try {
                showNotification('💾 Guardando borrador...', 'info');

                // Simular guardado
                await new Promise(resolve => setTimeout(resolve, 1500));

                showNotification('✅ Borrador guardado correctamente', 'success');
                console.log('Draft saved:', courseData);

            } catch (error) {
                showNotification('❌ Error al guardar el borrador', 'error');
                console.error('Save draft error:', error);
            }
        }

        async function submitForm(e) {
            e.preventDefault();

            updateCourseData();

            if (!validateForm()) {
                showNotification('❌ Por favor corrige los errores antes de crear el curso', 'error');
                return;
            }

            try {
                const submitBtn = document.querySelector('button[type="submit"]');
                const originalText = submitBtn.innerHTML;

                submitBtn.innerHTML = '<i class="bi bi-arrow-clockwise spin"></i> Creando Curso...';
                submitBtn.disabled = true;

                showNotification('🚀 Creando curso...', 'info');

                // Simular creación del curso
                await new Promise(resolve => setTimeout(resolve, 2000));

                showNotification('✅ Curso creado exitosamente', 'success');
                console.log('Course created:', courseData);

                // Redirigir después de un momento
                setTimeout(() => {
                    if (confirm('¿Quieres ir a la lista de cursos o crear otro curso?')) {
                        window.location.href = 'all-courses-inline.html';
                    } else {
                        resetForm();
                    }
                }, 2000);

            } catch (error) {
                showNotification('❌ Error al crear el curso', 'error');
                console.error('Submit error:', error);

                const submitBtn = document.querySelector('button[type="submit"]');
                submitBtn.innerHTML = '<i class="bi bi-check-circle"></i> Crear Curso';
                submitBtn.disabled = false;
            }
        }

        function cancelCourse() {
            if (confirm('¿Estás seguro de que quieres cancelar? Se perderán todos los cambios.')) {
                window.location.href = 'all-courses-inline.html';
            }
        }

        function resetForm() {
            document.getElementById('newCourseForm').reset();
            document.getElementById('imagePreview').style.display = 'none';
            document.querySelectorAll('.category-pill').forEach(pill => {
                pill.classList.remove('selected');
            });

            courseData = {
                title: '',
                description: '',
                category: '',
                price: 0,
                image: null,
                modules: 1,
                lessons: 1,
                duration: 1,
                status: 'draft',
                code: '',
                notes: ''
            };

            validationErrors = {};

            // Limpiar errores
            document.querySelectorAll('.error-message').forEach(error => {
                error.style.display = 'none';
            });

            document.querySelectorAll('.form-control').forEach(field => {
                field.classList.remove('error');
            });

            showNotification('🔄 Formulario reiniciado', 'info');
        }

        // ========================================
        // INICIALIZACIÓN
        // ========================================
        document.addEventListener('DOMContentLoaded', function() {
            console.log('🚀 New Course ASG Inline v2.0.0 loaded');

            // Renderizar categorías
            renderCategories();

            // Configurar upload de imagen
            setupImageUpload();

            // Configurar validación del formulario
            setupFormValidation();

            // Configurar envío del formulario
            document.getElementById('newCourseForm').addEventListener('submit', submitForm);

            // Mostrar notificación de bienvenida
            setTimeout(() => {
                showNotification('📝 Formulario de nuevo curso listo', 'success');
            }, 500);

            // Configurar atajos de teclado
            document.addEventListener('keydown', (e) => {
                if (e.ctrlKey || e.metaKey) {
                    switch(e.key) {
                        case 's':
                            e.preventDefault();
                            saveDraft();
                            break;
                        case 'Enter':
                            e.preventDefault();
                            document.getElementById('newCourseForm').dispatchEvent(new Event('submit'));
                            break;
                    }
                }
            });
        });

        // ========================================
        // FUNCIONES GLOBALES
        // ========================================
        window.ASG_NEW_COURSE = {
            saveDraft: saveDraft,
            submitForm: submitForm,
            cancelCourse: cancelCourse,
            resetForm: resetForm,
            showNotification: showNotification,
            config: ASG_CONFIG,
            utils: ASG_UTILS,
            data: courseData
        };

        // Log de información del sistema
        console.log('📝 ASG New Course System Info:', {
            version: ASG_CONFIG.version,
            categories: Object.keys(ASG_CONFIG.categories).length,
            validation: ASG_CONFIG.validation,
            timestamp: new Date().toISOString()
        });
    </script>
</body>
</html>

        // ========================================
        // FUNCIONES DE CATEGORÍAS
        // ========================================
        function renderCategories() {
            const container = document.getElementById('categoryPills');

            container.innerHTML = Object.entries(ASG_CONFIG.categories).map(([code, info]) => `
                <div class="category-pill" data-category="${code}" onclick="selectCategory('${code}')">
                    ${info.icon} ${info.label}
                </div>
            `).join('');
        }

        function selectCategory(categoryCode) {
            // Limpiar selección anterior
            document.querySelectorAll('.category-pill').forEach(pill => {
                pill.classList.remove('selected');
            });

            // Seleccionar nueva categoría
            const selectedPill = document.querySelector(`[data-category="${categoryCode}"]`);
            if (selectedPill) {
                selectedPill.classList.add('selected');
                courseData.category = categoryCode;
                document.getElementById('courseCategory').value = categoryCode;
                clearFieldError('courseCategory');
            }
        }

        // ========================================
        // FUNCIONES DE IMAGEN
        // ========================================
        function setupImageUpload() {
            const uploadArea = document.getElementById('imageUpload');
            const fileInput = document.getElementById('imageInput');
            const preview = document.getElementById('imagePreview');

            // Click para seleccionar archivo
            uploadArea.addEventListener('click', () => {
                fileInput.click();
            });

            // Drag and drop
            uploadArea.addEventListener('dragover', (e) => {
                e.preventDefault();
                uploadArea.classList.add('dragover');
            });

            uploadArea.addEventListener('dragleave', () => {
                uploadArea.classList.remove('dragover');
            });

            uploadArea.addEventListener('drop', (e) => {
                e.preventDefault();
                uploadArea.classList.remove('dragover');

                const files = e.dataTransfer.files;
                if (files.length > 0) {
                    handleImageFile(files[0]);
                }
            });

            // Cambio de archivo
            fileInput.addEventListener('change', (e) => {
                if (e.target.files.length > 0) {
                    handleImageFile(e.target.files[0]);
                }
            });
        }

        function handleImageFile(file) {
            // Validar tipo de archivo
            const validTypes = ['image/jpeg', 'image/png', 'image/webp'];
            if (!validTypes.includes(file.type)) {
                showFieldError('courseImage', 'Formato de imagen no válido. Usa JPG, PNG o WebP');
                return;
            }

            // Validar tamaño
            if (file.size > ASG_CONFIG.validation.imageSize) {
                showFieldError('courseImage', `La imagen es muy grande. Máximo ${ASG_UTILS.formatFileSize(ASG_CONFIG.validation.imageSize)}`);
                return;
            }

            // Mostrar preview
            const reader = new FileReader();
            reader.onload = (e) => {
                const preview = document.getElementById('imagePreview');
                preview.src = e.target.result;
                preview.style.display = 'block';

                courseData.image = file;
                clearFieldError('courseImage');

                showNotification(`✅ Imagen cargada: ${file.name}`, 'success');
            };

            reader.readAsDataURL(file);
        }

        // ========================================
        // FUNCIONES DE FORMULARIO
        // ========================================
        function updateCourseData() {
            courseData.title = document.getElementById('courseTitle').value;
            courseData.description = document.getElementById('courseDescription').value;
            courseData.price = parseFloat(document.getElementById('coursePrice').value) || 0;
            courseData.modules = parseInt(document.getElementById('courseModules').value) || 1;
            courseData.lessons = parseInt(document.getElementById('courseLessons').value) || 1;
            courseData.duration = parseInt(document.getElementById('courseDuration').value) || 1;
            courseData.status = document.getElementById('courseStatus').value;
            courseData.notes = document.getElementById('courseNotes').value;

            // Generar código si no existe
            if (!courseData.code && courseData.title) {
                courseData.code = ASG_UTILS.generateCourseCode(courseData.title);
                document.getElementById('courseCode').value = courseData.code;
            }
        }

        function setupFormValidation() {
            // Validación en tiempo real
            const titleInput = document.getElementById('courseTitle');
            const descriptionInput = document.getElementById('courseDescription');
            const priceInput = document.getElementById('coursePrice');

            const debouncedValidation = ASG_UTILS.debounce(() => {
                updateCourseData();
                validateForm();
            }, 500);

            [titleInput, descriptionInput, priceInput].forEach(input => {
                input.addEventListener('input', debouncedValidation);
            });

            // Generar código automáticamente
            titleInput.addEventListener('input', ASG_UTILS.debounce(() => {
                if (titleInput.value.length >= 3) {
                    const code = ASG_UTILS.generateCourseCode(titleInput.value);
                    document.getElementById('courseCode').value = code;
                    courseData.code = code;
                }
            }, 1000));
        }

        // ========================================
        // FUNCIONES DE ENVÍO
        // ========================================
        async function saveDraft() {
            updateCourseData();
            courseData.status = 'draft';

            if (!validateForm()) {
                showNotification('❌ Por favor corrige los errores antes de guardar', 'error');
                return;
            }

            try {
                showNotification('💾 Guardando borrador...', 'info');

                // Simular guardado
                await new Promise(resolve => setTimeout(resolve, 1500));

                showNotification('✅ Borrador guardado correctamente', 'success');
                console.log('Draft saved:', courseData);

            } catch (error) {
                showNotification('❌ Error al guardar el borrador', 'error');
                console.error('Save draft error:', error);
            }
        }

        async function submitForm(e) {
            e.preventDefault();

            updateCourseData();

            if (!validateForm()) {
                showNotification('❌ Por favor corrige los errores antes de crear el curso', 'error');
                return;
            }

            try {
                const submitBtn = document.querySelector('button[type="submit"]');
                const originalText = submitBtn.innerHTML;

                submitBtn.innerHTML = '<i class="bi bi-arrow-clockwise spin"></i> Creando Curso...';
                submitBtn.disabled = true;

                showNotification('🚀 Creando curso...', 'info');

                // Simular creación del curso
                await new Promise(resolve => setTimeout(resolve, 2000));

                showNotification('✅ Curso creado exitosamente', 'success');
                console.log('Course created:', courseData);

                // Redirigir después de un momento
                setTimeout(() => {
                    if (confirm('¿Quieres ir a la lista de cursos o crear otro curso?')) {
                        window.location.href = 'all-courses-inline.html';
                    } else {
                        resetForm();
                    }
                }, 2000);

            } catch (error) {
                showNotification('❌ Error al crear el curso', 'error');
                console.error('Submit error:', error);

                const submitBtn = document.querySelector('button[type="submit"]');
                submitBtn.innerHTML = '<i class="bi bi-check-circle"></i> Crear Curso';
                submitBtn.disabled = false;
            }
        }

        function cancelCourse() {
            if (confirm('¿Estás seguro de que quieres cancelar? Se perderán todos los cambios.')) {
                window.location.href = 'all-courses-inline.html';
            }
        }

        function resetForm() {
            document.getElementById('newCourseForm').reset();
            document.getElementById('imagePreview').style.display = 'none';
            document.querySelectorAll('.category-pill').forEach(pill => {
                pill.classList.remove('selected');
            });

            courseData = {
                title: '',
                description: '',
                category: '',
                price: 0,
                image: null,
                modules: 1,
                lessons: 1,
                duration: 1,
                status: 'draft',
                code: '',
                notes: ''
            };

            validationErrors = {};

            // Limpiar errores
            document.querySelectorAll('.error-message').forEach(error => {
                error.style.display = 'none';
            });

            document.querySelectorAll('.form-control').forEach(field => {
                field.classList.remove('error');
            });

            showNotification('🔄 Formulario reiniciado', 'info');
        }

        // ========================================
        // INICIALIZACIÓN
        // ========================================
        document.addEventListener('DOMContentLoaded', function() {
            console.log('🚀 New Course ASG Inline v2.0.0 loaded');

            // Renderizar categorías
            renderCategories();

            // Configurar upload de imagen
            setupImageUpload();

            // Configurar validación del formulario
            setupFormValidation();

            // Configurar envío del formulario
            document.getElementById('newCourseForm').addEventListener('submit', submitForm);

            // Mostrar notificación de bienvenida
            setTimeout(() => {
                showNotification('📝 Formulario de nuevo curso listo', 'success');
            }, 500);

            // Configurar atajos de teclado
            document.addEventListener('keydown', (e) => {
                if (e.ctrlKey || e.metaKey) {
                    switch(e.key) {
                        case 's':
                            e.preventDefault();
                            saveDraft();
                            break;
                        case 'Enter':
                            e.preventDefault();
                            document.getElementById('newCourseForm').dispatchEvent(new Event('submit'));
                            break;
                    }
                }
            });
        });

        // ========================================
        // FUNCIONES GLOBALES
        // ========================================
        window.ASG_NEW_COURSE = {
            saveDraft: saveDraft,
            submitForm: submitForm,
            cancelCourse: cancelCourse,
            resetForm: resetForm,
            showNotification: showNotification,
            config: ASG_CONFIG,
            utils: ASG_UTILS,
            data: courseData
        };

        // Log de información del sistema
        console.log('📝 ASG New Course System Info:', {
            version: ASG_CONFIG.version,
            categories: Object.keys(ASG_CONFIG.categories).length,
            validation: ASG_CONFIG.validation,
            timestamp: new Date().toISOString()
        });
    </script>
