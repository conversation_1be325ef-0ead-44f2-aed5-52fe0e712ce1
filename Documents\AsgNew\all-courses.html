<!-- 
         * ========================================
         * LISTADO DE CURSOS - ABILITYSEMINARSGROUP
         * ========================================
         *
         * Descripción: Sistema completo para la visualización y gestión
         * de todos los cursos creados en la plataforma.
         *
         * Funcionalidades:
         * - Listado completo de cursos con paginación
         * - Filtros por categoría y estado
         * - Búsqueda en tiempo real
         * - Acciones de edición y eliminación
         * - Vista responsive con cards
         *
         * Autor: <PERSON>.M
         * Fecha: 2025
         * Versión: 1.1 - FIXED FOR NEW DB STRUCTURE
         
-->
<!DOCTYPE html>
<html lang="es">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>All Courses - AbilitySeminarsGroup</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.11.3/font/bootstrap-icons.css">
    <link href="https://fonts.googleapis.com/css2?family=Inter:ital,opsz,wght@0,14..32,100..900;1,14..32,100..900&display=swap" rel="stylesheet">
    
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background-color: #1e3a5f;
            overflow-x: hidden;
            min-height: 100vh;
        }

        /* Navbar Styles */
        .navbar {
            background-color: #1e3a5f;
            height: 70px;
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            z-index: 1030;
            padding: 0 1rem;
            border-bottom: 1px solid #2c5282;
        }

        .navbar .btn {
            color: white;
            border: none;
            background: none;
            padding: 0.5rem;
            border-radius: 4px;
            transition: background-color 0.2s ease;
        }

        .navbar .btn:hover {
            background-color: rgba(255, 255, 255, 0.1);
        }

        .navbar .btn:focus {
            box-shadow: none;
            outline: none;
        }

        .avatar {
            width: 35px;
            height: 35px;
            background-color: #4299e1;
            font-weight: bold;
            font-size: 1rem;
        }

        /* Sidebar Styles */
        .sidebar {
            position: fixed;
            top: 0;
            left: 0;
            width: 220px;
            height: 100vh;
            background-color: #1e3a5f;
            color: white;
            transform: translateX(-100%);
            transition: all 0.3s ease;
            z-index: 1040;
            padding-top: 70px;
            border-right: 1px solid #2c5282;
        }

        .sidebar.show {
            transform: translateX(0);
        }

        .sidebar.collapsed {
            width: 55px;
        }

        .sidebar.collapsed .menu-text {
            display: none;
        }

        .sidebar.collapsed .dropdown {
            display: none;
        }

        .sidebar.collapsed:not(.desktop-hidden):hover {
            width: 220px;
        }

        .sidebar.collapsed:not(.desktop-hidden):hover .menu-text {
            display: inline;
        }

        .sidebar.collapsed:not(.desktop-hidden):hover .dropdown {
            display: inline;
        }

        .sidebar.desktop-hidden:hover {
            transform: translateX(-100%) !important;
            width: 220px !important;
        }

        .sidebar-nav {
            padding: 0.5rem 0;
            height: calc(100vh - 70px);
            overflow: visible;
        }

        .sidebar-parent > a {
            padding: 0.6rem 1rem;
            display: flex;
            align-items: center;
            color: #b8d4f0;
            text-decoration: none;
            transition: all 0.3s ease;
            border-left: 3px solid transparent;
            font-size: 0.9rem;
        }

        .sidebar-parent > a:hover,
        .sidebar-parent.active > a {
            background-color: #2c5282;
            color: white;
            border-left-color: #4299e1;
        }

        .sidebar-children {
            list-style: none;
            padding: 0;
            margin: 0;
            background-color: #2c5282;
            max-height: 0;
            overflow: hidden;
            transition: max-height 0.3s ease;
        }

        .sidebar-parent.expanded .sidebar-children {
            max-height: 200px;
        }

        .sidebar-children li a {
            padding: 0.4rem 1rem 0.4rem 2.5rem;
            display: flex;
            align-items: center;
            color: #b8d4f0;
            text-decoration: none;
            transition: all 0.3s ease;
            font-size: 0.85rem;
        }

        .sidebar-children li a:hover,
        .sidebar-children li a.active {
            background-color: #3182ce;
            color: white;
        }

        /* Body Content */
        .body-content {
            margin-left: 0;
            margin-top: 70px;
            transition: margin-left 0.3s ease;
            min-height: calc(100vh - 70px);
            background-color: #f8f9fa;
        }

        .body-content.collapsed {
            margin-left: 55px;
        }

        .page-header {
            background-color: #1e3a5f;
            color: white;
            padding: 1rem 2rem;
            margin-bottom: 0;
            position: sticky;
            top: 70px;
            z-index: 1020;
        }

        /* Main Content */
        .main-content {
            padding: 2rem;
        }

        /* Controls Section */
        .controls-section {
            background: white;
            border-radius: 12px;
            padding: 1.5rem;
            margin-bottom: 2rem;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
        }

        .search-box {
            position: relative;
        }

        .search-box input {
            padding-left: 2.5rem;
            border: 1px solid #ddd;
            border-radius: 8px;
            height: 45px;
        }

        .search-box i {
            position: absolute;
            left: 1rem;
            top: 50%;
            transform: translateY(-50%);
            color: #666;
        }

        .filter-select {
            border: 1px solid #ddd;
            border-radius: 8px;
            height: 45px;
            padding: 0 1rem;
        }

        .btn-new-course {
            background-color: #1e3a5f;
            color: white;
            border: none;
            border-radius: 8px;
            padding: 0.75rem 1.5rem;
            font-weight: 500;
            text-decoration: none;
            display: inline-flex;
            align-items: center;
            gap: 0.5rem;
            transition: all 0.3s ease;
        }

        .btn-new-course:hover {
            background-color: #2c5282;
            color: white;
        }

        /* Course Cards */
        .courses-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
            gap: 1.5rem;
        }

        .course-card {
            background: white;
            border-radius: 12px;
            overflow: hidden;
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
            transition: all 0.3s ease;
            position: relative;
        }

        .course-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
        }

        .course-image {
            width: 100%;
            height: 200px;
            background: linear-gradient(135deg, #1e3a5f, #4299e1);
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 3rem;
            position: relative;
        }

        .course-image img {
            width: 100%;
            height: 100%;
            object-fit: cover;
        }

        .course-price {
            position: absolute;
            top: 1rem;
            right: 1rem;
            background: rgba(0, 0, 0, 0.8);
            color: white;
            padding: 0.25rem 0.75rem;
            border-radius: 20px;
            font-size: 0.9rem;
            font-weight: 600;
        }

        .course-price.free {
            background: #28a745;
        }

        .course-content {
            padding: 1.5rem;
        }

        .course-category {
            display: inline-block;
            background: #e3f2fd;
            color: #1976d2;
            padding: 0.25rem 0.75rem;
            border-radius: 20px;
            font-size: 0.8rem;
            font-weight: 500;
            margin-bottom: 1rem;
        }

        .course-title {
            font-size: 1.25rem;
            font-weight: 600;
            color: #333;
            margin-bottom: 0.75rem;
            line-height: 1.4;
        }

        .course-description {
            color: #666;
            font-size: 0.95rem;
            line-height: 1.5;
            margin-bottom: 1rem;
            display: -webkit-box;
            -webkit-line-clamp: 3;
            line-clamp: 3;
            -webkit-box-orient: vertical;
            overflow: hidden;
        }

        .course-meta {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 1rem;
            font-size: 0.9rem;
            color: #666;
        }

        .course-actions {
            display: flex;
            gap: 0.5rem;
        }

        .btn-action {
            padding: 0.5rem 1rem;
            border: none;
            border-radius: 6px;
            font-size: 0.9rem;
            cursor: pointer;
            transition: all 0.3s ease;
            text-decoration: none;
            display: inline-flex;
            align-items: center;
            gap: 0.25rem;
        }

        .btn-edit {
            background-color: #17a2b8;
            color: white;
        }

        .btn-edit:hover {
            background-color: #138496;
            color: white;
        }

        .btn-delete {
            background-color: #dc3545;
            color: white;
        }

        .btn-delete:hover {
            background-color: #c82333;
        }

        /* Loading and Empty States */
        .loading-state {
            text-align: center;
            padding: 3rem;
            color: #666;
        }

        .loading-spinner {
            width: 3rem;
            height: 3rem;
            border: 3px solid #f3f3f3;
            border-top: 3px solid #1e3a5f;
            border-radius: 50%;
            animation: spin 1s linear infinite;
            margin: 0 auto 1rem;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        .empty-state {
            text-align: center;
            padding: 3rem;
            color: #666;
        }

        .empty-state i {
            font-size: 4rem;
            color: #ddd;
            margin-bottom: 1rem;
        }

        /* Pagination */
        .pagination-wrapper {
            display: flex;
            justify-content: center;
            margin-top: 2rem;
        }

        .pagination {
            display: flex;
            gap: 0.5rem;
            align-items: center;
        }

        .pagination button {
            padding: 0.5rem 1rem;
            border: 1px solid #ddd;
            background: white;
            color: #333;
            border-radius: 6px;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .pagination button:hover:not(:disabled) {
            background-color: #1e3a5f;
            color: white;
            border-color: #1e3a5f;
        }

        .pagination button.active {
            background-color: #1e3a5f;
            color: white;
            border-color: #1e3a5f;
        }

        .pagination button:disabled {
            opacity: 0.5;
            cursor: not-allowed;
        }

        /* Alerts */
        .alert {
            padding: 1rem;
            border-radius: 8px;
            margin-bottom: 1rem;
            display: none;
        }

        .alert.show {
            display: block;
        }

        .alert-success {
            background-color: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }

        .alert-danger {
            background-color: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }

        .alert-warning {
            background-color: #fff3cd;
            color: #856404;
            border: 1px solid #ffeaa7;
        }

        /* Overlay */
        .sidebar-overlay {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0, 0, 0, 0.5);
            z-index: 1035;
            display: none;
        }

        .sidebar-overlay.show {
            display: block;
        }

        /* Responsive */
        @media (min-width: 992px) {
            .sidebar:not(.desktop-hidden) {
                transform: translateX(0);
                position: fixed;
            }

            .sidebar.desktop-hidden {
                transform: translateX(-100%);
            }

            .body-content {
                margin-left: 220px;
                transition: margin-left 0.3s ease;
            }

            .body-content.sidebar-hidden {
                margin-left: 0;
            }

            .sidebar-overlay {
                display: none !important;
            }
        }

        @media (max-width: 991px) {
            .main-content {
                padding: 1rem;
            }
            
            .courses-grid {
                grid-template-columns: 1fr;
            }
            
            .controls-section .row > div {
                margin-bottom: 1rem;
            }
        }

        @media (max-width: 576px) {
            .course-actions {
                flex-direction: column;
            }
            
            .btn-action {
                justify-content: center;
            }
        }
    </style>
</head>
<body>
    <!-- Navbar -->
    <nav class="navbar">
        <div class="container-fluid d-flex align-items-center">
            <div class="d-flex align-items-center">
                <button class="btn fs-5 me-3" id="toggleSidebar">☰</button>
                <a class="navbar-brand p-0 pb-1" href="https://abilityseminarsgroup.com/admin-dashboard/">
                    <img src="https://abilityseminarsgroup.com/wp-content/uploads/2023/09/Asset-6-4-768x355.png" alt="Logo" style="width:130px;height:auto">
                </a>
            </div>

            <div class="ms-auto d-none d-md-flex align-items-center">
                <div class="dropdown me-3">
                    <i id="confi" class="bi bi-gear fs-5" data-bs-toggle="dropdown" role="button" aria-expanded="false" style="cursor: pointer;"></i>
                    <ul class="dropdown-menu dropdown-menu-end" aria-labelledby="confi">
                        <li><a class="dropdown-item" href="#"><i class="bi bi-person-circle fs-5 me-2"></i>Mi perfil</a></li>
                        <li><a class="dropdown-item" href="#"><i class="bi bi-shield-lock fs-5 me-2"></i>Contraseña</a></li>
                    </ul>
                </div>

                <figure class="m-0 me-4 ms-2 d-flex align-items-center">
                    <div class="avatar rounded-circle d-flex justify-content-center align-items-center text-light">
                        A
                    </div>
                    <p class="ms-2 mb-0 text-white">anthony sosa</p>
                    <i class="bi bi-chevron-down ms-2 text-white"></i>
                </figure>
            </div>
        </div>
    </nav>

    <!-- Sidebar Overlay -->
    <div class="sidebar-overlay" id="sidebarOverlay"></div>

    <!-- Sidebar -->
    <div id="sidebar" class="sidebar text-white">
        <div class="w-100 d-flex justify-content-end align-items-center" style="height: 50px; padding-top: 0.5rem;">
            <button id="toggleSidebar2" class="btn btn-sm text-white fs-6">☰</button>
        </div>
        
        <ul class="sidebar-nav list-unstyled">
            <li class="sidebar-parent">
                <a href="https://abilityseminarsgroup.com/admin-dashboard/" class="d-block text-white text-decoration-none">
                    <i class="bi bi-house fs-6 me-2"></i><span class="menu-text">Dashboard</span>
                </a>
            </li>
            
            <li class="sidebar-parent">
                <a href="#" class="d-block text-white text-decoration-none">
                    <i class="bi bi-credit-card-2-back fs-6 me-2"></i><span class="menu-text">Sales<i class="ms-2 bi bi-chevron-down dropdown"></i></span>
                </a>
                <ul class="sidebar-children">
                    <li>
                        <a href="#" class="">
                            <i class="bi-cart-check fs-6 me-2"></i><span class="menu-text">Offers</span>
                        </a>
                    </li>
                    <li>
                        <a href="#" class="">
                            <i class="bi bi-credit-card-fill fs-6 me-2"></i><span class="menu-text">Payments</span>
                        </a>
                    </li>
                </ul>
            </li>
            
            <li class="sidebar-parent">
                <a href="#" class="d-block text-white text-decoration-none">
                    <i class="bi bi-envelope fs-6 me-2"></i><span class="menu-text">Marketing<i class="ms-2 bi bi-chevron-down dropdown"></i></span>
                </a>
                <ul class="sidebar-children">
                    <li>
                        <a href="#" class="">
                            <i class="bi bi-envelope-fill fs-6 me-2"></i><span class="menu-text">Email Campaigns</span>
                        </a>
                    </li>
                </ul>
            </li>
            
            <li class="sidebar-parent active expanded">
                <a href="#" class="d-block text-white text-decoration-none">
                    <i class="bi bi-book fs-6 me-2"></i><span class="menu-text">Courses<i class="ms-2 bi bi-chevron-down dropdown"></i></span>
                </a>
                <ul class="sidebar-children">
                    <li>
                        <a href="https://abilityseminarsgroup.com/new-course/" class="">
                            <i class="bi bi-plus-circle fs-6 me-2"></i><span class="menu-text">New Courses</span>
                        </a>
                    </li>
                    <li>
                        <a href="https://abilityseminarsgroup.com/all-courses/" class="active">
                            <i class="bi bi-box-fill fs-6 me-2"></i><span class="menu-text">All Courses</span>
                        </a>
                    </li>
                </ul>
            </li>
            
            <li class="sidebar-parent">
                <a href="#" class="d-block text-white text-decoration-none">
                    <i class="bi bi-people-fill fs-6 me-2"></i><span class="menu-text">Students<i class="ms-2 bi bi-chevron-down dropdown"></i></span>
                </a>
                <ul class="sidebar-children">
                    <li>
                        <a href="#" class="">
                            <i class="bi bi-people fs-6 me-2"></i><span class="menu-text">All Students</span>
                        </a>
                    </li>
                    <li>
                        <a href="#" class="">
                            <i class="bi bi-clipboard-data fs-6 me-2"></i><span class="menu-text">Student Progress</span>
                        </a>
                    </li>
                    <li>
                        <a href="#" class="">
                            <i class="bi bi-chat-text-fill fs-6 me-2"></i><span class="menu-text">Student Message</span>
                        </a>
                    </li>
                </ul>
            </li>
            
            <li class="sidebar-parent">
                <a href="#" class="d-block text-white text-decoration-none">
                    <i class="bi bi-bar-chart-fill fs-6 me-2"></i><span class="menu-text">Analytics</span>
                </a>
            </li>
            
            <li class="sidebar-parent">
                <a href="#" class="d-block text-white text-decoration-none">
                    <i class="bi bi-gear fs-6 me-2"></i><span class="menu-text">Setting<i class="ms-2 bi bi-chevron-down dropdown"></i></span>
                </a>
                <ul class="sidebar-children">
                    <li>
                        <a href="#" class="">
                            <i class="bi bi-person-fill-gear fs-6 me-2"></i><span class="menu-text">Manage Users</span>
                        </a>
                    </li>
                    <li>
                        <a href="#" class="">
                            <i class="bi-file-earmark-text fs-6 me-2"></i><span class="menu-text">Manage Courses</span>
                        </a>
                    </li>
                </ul>
            </li>
            
            <li class="sidebar-parent">
                <a href="#" class="d-block text-white text-decoration-none">
                    <i class="bi bi-box-arrow-left fs-6 me-2"></i><span class="menu-text">Cerrar sesión</span>
                </a>
            </li>
        </ul>
    </div>

    <!-- Main Content -->
    <section class="body-content">
        <header class="page-header d-flex p-2 align-items-center justify-content-between">
            <h5 class="mb-0 px-3 py-2 text-white">All Courses</h5>
            <div class="text-white">
                <span id="coursesCount">Loading...</span> courses found
            </div>
        </header>
        
        <section class="main-content">
            <!-- Alerts -->
            <div id="alertSuccess" class="alert alert-success">
                <i class="bi bi-check-circle me-2"></i>
                <span id="alertSuccessText">Operation completed successfully</span>
            </div>
            <div id="alertDanger" class="alert alert-danger">
                <i class="bi bi-exclamation-triangle me-2"></i>
                <span id="alertDangerText">An error occurred</span>
            </div>
            <div id="alertWarning" class="alert alert-warning">
                <i class="bi bi-exclamation-triangle me-2"></i>
                <span id="alertWarningText">Warning message</span>
            </div>

            <!-- Controls Section -->
            <div class="controls-section">
                <div class="row align-items-center">
                    <div class="col-md-4">
                        <div class="search-box">
                            <i class="bi bi-search"></i>
                            <input type="text" id="searchInput" class="form-control" placeholder="Search courses...">
                        </div>
                    </div>
                    <div class="col-md-3">
                        <select id="categoryFilter" class="form-select filter-select">
                            <option value="">All Categories</option>
                        </select>
                    </div>
                    <div class="col-md-3">
                        <select id="statusFilter" class="form-select filter-select">
                            <option value="published">Published Courses</option>
                            <option value="draft">Draft Courses</option>
                            <option value="archived">Archived Courses</option>
                        </select>
                    </div>
                    <div class="col-md-1">
                        <button class="btn btn-outline-secondary w-100" onclick="enterBulkMode()" title="Bulk Actions">
                            <i class="bi bi-check2-square"></i>
                        </button>
                    </div>
                    <div class="col-md-1">
                        <a href="https://abilityseminarsgroup.com/new-course/" class="btn-new-course">
                            <i class="bi bi-plus-circle"></i>
                            New Course
                        </a>
                    </div>
                </div>

                <!-- Bulk Actions Bar -->
                <div id="bulkActionsBar" class="bulk-actions-bar mt-3" style="display: none;">
                    <div class="d-flex align-items-center justify-content-between p-3 bg-light rounded">
                        <div class="d-flex align-items-center gap-3">
                            <label class="d-flex align-items-center mb-0">
                                <input type="checkbox" id="selectAllCourses" class="form-check-input me-2">
                                <span>Select All</span>
                            </label>
                            <span id="selectedCount" class="text-muted">0 selected</span>
                        </div>
                        <div class="d-flex gap-2">
                            <select id="bulkAction" class="form-select form-select-sm" style="width: auto;">
                                <option value="">Bulk Actions</option>
                                <option value="published">Publish</option>
                                <option value="draft">Move to Draft</option>
                                <option value="archived">Archive</option>
                                <option value="delete">Delete</option>
                            </select>
                            <button id="applyBulkAction" class="btn btn-primary btn-sm">Apply</button>
                            <button id="cancelBulkMode" class="btn btn-secondary btn-sm">Cancel</button>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Courses Grid -->
            <div id="coursesContainer">
                <!-- Loading State -->
                <div id="loadingState" class="loading-state">
                    <div class="loading-spinner"></div>
                    <p>Loading courses...</p>
                </div>

                <!-- Empty State -->
                <div id="emptyState" class="empty-state" style="display: none;">
                    <i class="bi bi-book"></i>
                    <h4>No courses found</h4>
                    <p>No courses match your current filters. Try adjusting your search criteria or create a new course.</p>
                    <a href="https://abilityseminarsgroup.com/new-course/" class="btn-new-course mt-3">
                        <i class="bi bi-plus-circle"></i>
                        Create Your First Course
                    </a>
                </div>

                <!-- Courses Grid -->
                <div id="coursesGrid" class="courses-grid" style="display: none;">
                    <!-- Course cards will be inserted here -->
                </div>
            </div>

            <!-- Pagination -->
            <div id="paginationWrapper" class="pagination-wrapper" style="display: none;">
                <div class="pagination">
                    <button id="prevPage" disabled>
                        <i class="bi bi-chevron-left"></i>
                        Previous
                    </button>
                    <div id="pageNumbers"></div>
                    <button id="nextPage" disabled>
                        Next
                        <i class="bi bi-chevron-right"></i>
                    </button>
                </div>
            </div>
        </section>
    </section>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.3/dist/js/bootstrap.bundle.min.js"></script>
    
    <script>
        // ========================================
        // VARIABLES GLOBALES
        // ========================================

        /**
         * Configuración del sistema de listado de cursos
         */
        const CoursesManager = {
            currentPage: 1,
            perPage: 12,
            totalPages: 1,
            totalCourses: 0,
            filters: {
                search: '',
                category: '',
                status: 'published'
            },
            courses: [],
            categories: {},
            isLoading: false,
            elements: {},

            // Bulk actions
            selectedCourses: new Set(),
            bulkMode: false
        };

        // ========================================
        // INICIALIZACIÓN DEL SISTEMA
        // ========================================

        document.addEventListener('DOMContentLoaded', function() {
            console.log('🚀 Iniciando Sistema de Listado de Cursos...');
            
            // Inicializar elementos del DOM
            initializeElements();
            
            // Inicializar componentes
            initializeSidebar();
            initializeFilters();
            initializePagination();
            initializeBulkActions();
            
            // Cargar datos iniciales
            loadCategories();
            loadCourses();
            
            console.log('✅ Sistema inicializado correctamente');
        });

        /**
         * Inicializar elementos del DOM
         */
        function initializeElements() {
            CoursesManager.elements = {
                // Containers
                coursesContainer: document.getElementById('coursesContainer'),
                loadingState: document.getElementById('loadingState'),
                emptyState: document.getElementById('emptyState'),
                coursesGrid: document.getElementById('coursesGrid'),
                
                // Filters
                searchInput: document.getElementById('searchInput'),
                categoryFilter: document.getElementById('categoryFilter'),
                statusFilter: document.getElementById('statusFilter'),
                
                // Pagination
                paginationWrapper: document.getElementById('paginationWrapper'),
                prevPage: document.getElementById('prevPage'),
                nextPage: document.getElementById('nextPage'),
                pageNumbers: document.getElementById('pageNumbers'),
                
                // Counters
                coursesCount: document.getElementById('coursesCount'),

                // Bulk actions
                bulkActionsBar: document.getElementById('bulkActionsBar'),
                selectAllCourses: document.getElementById('selectAllCourses'),
                selectedCount: document.getElementById('selectedCount'),
                bulkAction: document.getElementById('bulkAction'),
                applyBulkAction: document.getElementById('applyBulkAction'),
                cancelBulkMode: document.getElementById('cancelBulkMode'),

                // Alerts
                alertSuccess: document.getElementById('alertSuccess'),
                alertDanger: document.getElementById('alertDanger'),
                alertWarning: document.getElementById('alertWarning'),
                alertSuccessText: document.getElementById('alertSuccessText'),
                alertDangerText: document.getElementById('alertDangerText'),
                alertWarningText: document.getElementById('alertWarningText'),
                
                // Sidebar
                sidebar: document.getElementById('sidebar'),
                sidebarOverlay: document.getElementById('sidebarOverlay'),
                bodyContent: document.querySelector('.body-content'),
                toggleSidebar: document.getElementById('toggleSidebar'),
                toggleSidebar2: document.getElementById('toggleSidebar2')
            };
            
            console.log('✅ Elementos del DOM inicializados');
        }

        /**
         * Inicializar sidebar
         */
        function initializeSidebar() {
            const { sidebar, sidebarOverlay, bodyContent, toggleSidebar, toggleSidebar2 } = CoursesManager.elements;
            
            // Toggle sidebar
            toggleSidebar.addEventListener('click', () => toggleSidebarVisibility());
            toggleSidebar2.addEventListener('click', () => toggleSidebarVisibility());
            
            // Close sidebar on overlay click (mobile)
            sidebarOverlay.addEventListener('click', () => closeSidebar());
            
            // Handle submenu toggles
            document.querySelectorAll('.sidebar-parent').forEach(parent => {
                const link = parent.querySelector('a');
                const hasChildren = parent.querySelector('.sidebar-children');
                
                if (hasChildren) {
                    link.addEventListener('click', (e) => {
                        e.preventDefault();
                        parent.classList.toggle('expanded');
                    });
                }
            });
            
            // Handle responsive behavior
            window.addEventListener('resize', handleSidebarResize);
            
            console.log('✅ Sidebar inicializado');
        }

        /**
         * Toggle sidebar visibility
         */
        function toggleSidebarVisibility() {
            const { sidebar, sidebarOverlay, bodyContent } = CoursesManager.elements;
            
            if (window.innerWidth >= 992) {
                // Desktop behavior
                sidebar.classList.toggle('desktop-hidden');
                bodyContent.classList.toggle('sidebar-hidden');
            } else {
                // Mobile behavior
                sidebar.classList.toggle('show');
                sidebarOverlay.classList.toggle('show');
            }
        }

        /**
         * Close sidebar (mobile)
         */
        function closeSidebar() {
            const { sidebar, sidebarOverlay } = CoursesManager.elements;
            sidebar.classList.remove('show');
            sidebarOverlay.classList.remove('show');
        }

        /**
         * Handle sidebar responsive behavior
         */
        function handleSidebarResize() {
            const { sidebar, sidebarOverlay, bodyContent } = CoursesManager.elements;
            
            if (window.innerWidth >= 992) {
                // Desktop: clean mobile classes
                sidebar.classList.remove('show');
                sidebarOverlay.classList.remove('show');
            } else {
                // Mobile: clean desktop classes
                sidebar.classList.remove('desktop-hidden');
                bodyContent.classList.remove('sidebar-hidden');
            }
        }

        /**
         * Inicializar filtros
         */
        function initializeFilters() {
            const { searchInput, categoryFilter, statusFilter } = CoursesManager.elements;
            
            // Search input with debounce
            let searchTimeout;
            searchInput.addEventListener('input', function() {
                clearTimeout(searchTimeout);
                searchTimeout = setTimeout(() => {
                    CoursesManager.filters.search = this.value.trim();
                    CoursesManager.currentPage = 1;
                    loadCourses();
                }, 500);
            });
            
            // Category filter
            categoryFilter.addEventListener('change', function() {
                CoursesManager.filters.category = this.value;
                CoursesManager.currentPage = 1;
                loadCourses();
            });
            
            // Status filter
            statusFilter.addEventListener('change', function() {
                CoursesManager.filters.status = this.value;
                CoursesManager.currentPage = 1;
                loadCourses();
            });
            
            console.log('✅ Filtros inicializados');
        }

        /**
         * Inicializar paginación
         */
        function initializePagination() {
            const { prevPage, nextPage } = CoursesManager.elements;
            
            prevPage.addEventListener('click', () => {
                if (CoursesManager.currentPage > 1) {
                    CoursesManager.currentPage--;
                    loadCourses();
                }
            });
            
            nextPage.addEventListener('click', () => {
                if (CoursesManager.currentPage < CoursesManager.totalPages) {
                    CoursesManager.currentPage++;
                    loadCourses();
                }
            });
            
            console.log('✅ Paginación inicializada');
        }

        /**
         * Inicializar acciones masivas
         */
        function initializeBulkActions() {
            const { selectAllCourses, applyBulkAction, cancelBulkMode } = CoursesManager.elements;

            // Select all checkbox
            selectAllCourses.addEventListener('change', function() {
                if (this.checked) {
                    // Select all visible courses
                    CoursesManager.courses.forEach(course => {
                        CoursesManager.selectedCourses.add(course.course_id);
                    });
                } else {
                    // Deselect all
                    CoursesManager.selectedCourses.clear();
                }
                updateBulkActionsUI();
                updateCourseCheckboxes();
            });

            // Apply bulk action
            applyBulkAction.addEventListener('click', async function() {
                const action = CoursesManager.elements.bulkAction.value;
                if (!action || CoursesManager.selectedCourses.size === 0) {
                    showAlert('warning', 'Please select an action and at least one course');
                    return;
                }

                await performBulkAction(action);
            });

            // Cancel bulk mode
            cancelBulkMode.addEventListener('click', function() {
                exitBulkMode();
            });

            console.log('✅ Acciones masivas inicializadas');
        }

        /**
         * Cargar categorías
         */
        async function loadCategories() {
            try {
                console.log('📋 Cargando categorías...');
                
                const response = await fetch('https://abilityseminarsgroup.com/wp-json/asg/v1/courses/categories');
                const result = await response.json();
                
                if (result.success) {
                    CoursesManager.categories = result.data;
                    
                    const { categoryFilter } = CoursesManager.elements;
                    
                    // Clear existing options (except "All Categories")
                    categoryFilter.innerHTML = '<option value="">All Categories</option>';
                    
                    // Add categories
                    Object.entries(result.data).forEach(([value, label]) => {
                        const option = document.createElement('option');
                        option.value = value;
                        option.textContent = label;
                        categoryFilter.appendChild(option);
                    });
                    
                    console.log('✅ Categorías cargadas:', Object.keys(result.data).length);
                    
                } else {
                    throw new Error(result.error?.message || 'Error loading categories');
                }
                
            } catch (error) {
                console.error('❌ Error cargando categorías:', error);
                showAlert('warning', 'Could not load categories');
            }
        }

        /**
         * Cargar cursos
         */
        async function loadCourses() {
            if (CoursesManager.isLoading) {
                return;
            }
            
            try {
                CoursesManager.isLoading = true;
                showLoadingState();
                
                console.log('📚 Cargando cursos...', {
                    page: CoursesManager.currentPage,
                    filters: CoursesManager.filters
                });
                
                // Construir URL con parámetros
                const params = new URLSearchParams({
                    page: CoursesManager.currentPage,
                    per_page: CoursesManager.perPage,
                    status: CoursesManager.filters.status
                });
                
                if (CoursesManager.filters.search) {
                    params.append('search', CoursesManager.filters.search);
                }
                
                if (CoursesManager.filters.category) {
                    params.append('category', CoursesManager.filters.category);
                }
                
                const response = await fetch(`https://abilityseminarsgroup.com/wp-json/asg/v1/courses?${params}`);
                
                if (!response.ok) {
                    throw new Error(`HTTP error! status: ${response.status}`);
                }
                
                const result = await response.json();
                
                console.log('📚 Respuesta de la API:', result);
                
                if (result.success) {
                    CoursesManager.courses = result.data || [];
                    CoursesManager.totalCourses = result.pagination?.total || 0;
                    CoursesManager.totalPages = result.pagination?.total_pages || 1;
                    
                    console.log('✅ Cursos cargados:', {
                        courses: CoursesManager.courses.length,
                        total: CoursesManager.totalCourses,
                        pages: CoursesManager.totalPages
                    });
                    
                    renderCourses();
                    updatePagination();
                    updateCoursesCount();
                    
                } else {
                    throw new Error(result.error?.message || 'Error loading courses');
                }
                
            } catch (error) {
                console.error('❌ Error cargando cursos:', error);
                showAlert('danger', `Error loading courses: ${error.message}`);
                showEmptyState();
                
            } finally {
                CoursesManager.isLoading = false;
            }
        }

        /**
         * Mostrar estado de carga
         */
        function showLoadingState() {
            const { loadingState, emptyState, coursesGrid, paginationWrapper } = CoursesManager.elements;
            
            loadingState.style.display = 'block';
            emptyState.style.display = 'none';
            coursesGrid.style.display = 'none';
            paginationWrapper.style.display = 'none';
        }

        /**
         * Mostrar estado vacío
         */
        function showEmptyState() {
            const { loadingState, emptyState, coursesGrid, paginationWrapper } = CoursesManager.elements;
            
            loadingState.style.display = 'none';
            emptyState.style.display = 'block';
            coursesGrid.style.display = 'none';
            paginationWrapper.style.display = 'none';
        }

        /**
         * Renderizar cursos
         */
        function renderCourses() {
            const { loadingState, emptyState, coursesGrid, paginationWrapper } = CoursesManager.elements;
            
            if (CoursesManager.courses.length === 0) {
                showEmptyState();
                return;
            }
            
            // Show courses grid
            loadingState.style.display = 'none';
            emptyState.style.display = 'none';
            coursesGrid.style.display = 'grid';
            paginationWrapper.style.display = 'flex';
            
            // Clear existing courses
            coursesGrid.innerHTML = '';
            
            // Render each course
            CoursesManager.courses.forEach(course => {
                const courseCard = createCourseCard(course);
                coursesGrid.appendChild(courseCard);
            });
            
            console.log('✅ Cursos renderizados:', CoursesManager.courses.length);
        }

        /**
         * Crear tarjeta de curso
         */
        function createCourseCard(course) {
            const card = document.createElement('div');
            card.className = 'course-card';
            
            console.log('🎨 Creando tarjeta para curso:', course);
            
            // Format price
            const priceText = course.price === 0 || course.price === '0' ? 'Free' : `$${parseFloat(course.price).toFixed(2)}`;
            const priceClass = course.price === 0 || course.price === '0' ? 'free' : '';
            
            // Format date
            const createdDate = new Date(course.created_at).toLocaleDateString();
            
            // Get category name
            const categoryName = CoursesManager.categories[course.category] || course.category || 'General';
            
            // Course image - check for featured image from post meta or use default
            const imageContent = course.featured_image_url 
                ? `<img src="${course.featured_image_url}" alt="${course.title}">`
                : `<i class="bi bi-book"></i>`;
            
            // Truncate description
            const description = course.description || 'No description available';
            const truncatedDescription = description.length > 150 
                ? description.substring(0, 150) + '...' 
                : description;
            
            // Use course_id for actions (this is the ID from asg_courses table)
            const courseId = course.course_id || course.id;
            
            card.innerHTML = `
                <div class="course-image">
                    ${imageContent}
                    <div class="course-price ${priceClass}">${priceText}</div>
                </div>
                <div class="course-content">
                    <div class="course-category">${categoryName}</div>
                    <h3 class="course-title">${course.title || 'Untitled Course'}</h3>
                    <p class="course-description">${truncatedDescription}</p>
                    <div class="course-meta">
                        <span><i class="bi bi-calendar me-1"></i>${createdDate}</span>
                        <span><i class="bi bi-clock me-1"></i>${course.duration || 0}h</span>
                    </div>
                    <div class="course-actions">
                        <a href="https://abilityseminarsgroup.com/edit-course/?id=${courseId}" class="btn-action btn-edit">
                            <i class="bi bi-pencil"></i>
                            Edit
                        </a>
                        <button class="btn-action btn-delete" onclick="deleteCourse(${courseId})">
                            <i class="bi bi-trash"></i>
                            Delete
                        </button>
                    </div>
                </div>
            `;
            
            return card;
        }

        /**
         * Actualizar paginación
         */
        function updatePagination() {
            const { prevPage, nextPage, pageNumbers, paginationWrapper } = CoursesManager.elements;
            
            if (CoursesManager.totalPages <= 1) {
                paginationWrapper.style.display = 'none';
                return;
            }
            
            paginationWrapper.style.display = 'flex';
            
            // Update prev/next buttons
            prevPage.disabled = CoursesManager.currentPage <= 1;
            nextPage.disabled = CoursesManager.currentPage >= CoursesManager.totalPages;
            
            // Update page numbers
            pageNumbers.innerHTML = '';
            
            const maxVisiblePages = 5;
            let startPage = Math.max(1, CoursesManager.currentPage - Math.floor(maxVisiblePages / 2));
            let endPage = Math.min(CoursesManager.totalPages, startPage + maxVisiblePages - 1);
            
            // Adjust start page if we're near the end
            if (endPage - startPage < maxVisiblePages - 1) {
                startPage = Math.max(1, endPage - maxVisiblePages + 1);
            }
            
            // Add first page and ellipsis if needed
            if (startPage > 1) {
                addPageButton(1);
                if (startPage > 2) {
                    const ellipsis = document.createElement('span');
                    ellipsis.textContent = '...';
                    ellipsis.style.padding = '0.5rem';
                    pageNumbers.appendChild(ellipsis);
                }
            }
            
            // Add visible page numbers
            for (let i = startPage; i <= endPage; i++) {
                addPageButton(i);
            }
            
            // Add last page and ellipsis if needed
            if (endPage < CoursesManager.totalPages) {
                if (endPage < CoursesManager.totalPages - 1) {
                    const ellipsis = document.createElement('span');
                    ellipsis.textContent = '...';
                    ellipsis.style.padding = '0.5rem';
                    pageNumbers.appendChild(ellipsis);
                }
                addPageButton(CoursesManager.totalPages);
            }
        }

        /**
         * Agregar botón de página
         */
        function addPageButton(pageNum) {
            const { pageNumbers } = CoursesManager.elements;
            
            const button = document.createElement('button');
            button.textContent = pageNum;
            button.className = pageNum === CoursesManager.currentPage ? 'active' : '';
            button.addEventListener('click', () => {
                CoursesManager.currentPage = pageNum;
                loadCourses();
            });
            
            pageNumbers.appendChild(button);
        }

        /**
         * Actualizar contador de cursos
         */
        function updateCoursesCount() {
            const { coursesCount } = CoursesManager.elements;
            coursesCount.textContent = CoursesManager.totalCourses;
        }

        /**
         * Eliminar curso
         */
        async function deleteCourse(courseId) {
            if (!confirm(`Are you sure you want to delete this course?`)) {
                return;
            }
            
            try {
                console.log('🗑️ Eliminando curso:', courseId);
                
                const response = await fetch(`https://abilityseminarsgroup.com/wp-json/asg/v1/courses/${courseId}`, {
                    method: 'DELETE'
                });
                
                const result = await response.json();
                
                if (result.success) {
                    console.log('✅ Curso eliminado exitosamente');
                    showAlert('success', `Course "${courseTitle}" deleted successfully`);
                    
                    // Reload courses
                    loadCourses();
                    
                } else {
                    throw new Error(result.error?.message || 'Error deleting course');
                }
                
            } catch (error) {
                console.error('❌ Error eliminando curso:', error);
                showAlert('danger', `Error deleting course: ${error.message}`);
            }
        }

        /**
         * Mostrar alerta
         */
        function showAlert(type, message, duration = 5000) {
            const alertElement = CoursesManager.elements[`alert${type.charAt(0).toUpperCase() + type.slice(1)}`];
            const textElement = CoursesManager.elements[`alert${type.charAt(0).toUpperCase() + type.slice(1)}Text`];
            
            if (alertElement) {
                if (textElement) {
                    textElement.textContent = message;
                }
                
                // Hide all alerts first
                Object.keys(CoursesManager.elements).forEach(key => {
                    if (key.startsWith('alert') && CoursesManager.elements[key].classList) {
                        CoursesManager.elements[key].classList.remove('show');
                    }
                });
                
                // Show current alert
                alertElement.classList.add('show');
                
                // Auto hide
                setTimeout(() => {
                    alertElement.classList.remove('show');
                }, duration);
            }
        }

        // ========================================
        // FUNCIONES DE BULK ACTIONS
        // ========================================

        /**
         * Entrar en modo de selección masiva
         */
        function enterBulkMode() {
            CoursesManager.bulkMode = true;
            CoursesManager.elements.bulkActionsBar.style.display = 'block';
            updateBulkActionsUI();
            addCourseCheckboxes();
        }

        /**
         * Salir del modo de selección masiva
         */
        function exitBulkMode() {
            CoursesManager.bulkMode = false;
            CoursesManager.selectedCourses.clear();
            CoursesManager.elements.bulkActionsBar.style.display = 'none';
            CoursesManager.elements.selectAllCourses.checked = false;
            removeCourseCheckboxes();
        }

        /**
         * Actualizar UI de acciones masivas
         */
        function updateBulkActionsUI() {
            const count = CoursesManager.selectedCourses.size;
            CoursesManager.elements.selectedCount.textContent = `${count} selected`;

            // Update select all checkbox state
            const totalVisible = CoursesManager.courses.length;
            const allSelected = count === totalVisible && totalVisible > 0;
            const someSelected = count > 0 && count < totalVisible;

            CoursesManager.elements.selectAllCourses.checked = allSelected;
            CoursesManager.elements.selectAllCourses.indeterminate = someSelected;
        }

        /**
         * Agregar checkboxes a las tarjetas de curso
         */
        function addCourseCheckboxes() {
            const courseCards = document.querySelectorAll('.course-card');
            courseCards.forEach(card => {
                const courseId = parseInt(card.dataset.courseId);
                if (!card.querySelector('.course-checkbox')) {
                    const checkbox = document.createElement('div');
                    checkbox.className = 'course-checkbox';
                    checkbox.innerHTML = `
                        <input type="checkbox" class="form-check-input"
                               ${CoursesManager.selectedCourses.has(courseId) ? 'checked' : ''}>
                    `;
                    checkbox.style.cssText = 'position: absolute; top: 10px; left: 10px; z-index: 10;';

                    const checkboxInput = checkbox.querySelector('input');
                    checkboxInput.addEventListener('change', function() {
                        if (this.checked) {
                            CoursesManager.selectedCourses.add(courseId);
                        } else {
                            CoursesManager.selectedCourses.delete(courseId);
                        }
                        updateBulkActionsUI();
                    });

                    card.appendChild(checkbox);
                }
            });
        }

        /**
         * Remover checkboxes de las tarjetas de curso
         */
        function removeCourseCheckboxes() {
            const checkboxes = document.querySelectorAll('.course-checkbox');
            checkboxes.forEach(checkbox => checkbox.remove());
        }

        /**
         * Actualizar estado de checkboxes
         */
        function updateCourseCheckboxes() {
            const checkboxes = document.querySelectorAll('.course-checkbox input');
            checkboxes.forEach(checkbox => {
                const card = checkbox.closest('.course-card');
                const courseId = parseInt(card.dataset.courseId);
                checkbox.checked = CoursesManager.selectedCourses.has(courseId);
            });
        }

        /**
         * Realizar acción masiva
         */
        async function performBulkAction(action) {
            const selectedIds = Array.from(CoursesManager.selectedCourses);

            if (selectedIds.length === 0) {
                showAlert('warning', 'No courses selected');
                return;
            }

            const confirmMessage = action === 'delete'
                ? `Are you sure you want to delete ${selectedIds.length} course(s)? This action cannot be undone.`
                : `Are you sure you want to ${action} ${selectedIds.length} course(s)?`;

            if (!confirm(confirmMessage)) {
                return;
            }

            try {
                let endpoint, method, body;

                if (action === 'delete') {
                    endpoint = 'https://abilityseminarsgroup.com/wp-json/asg/v1/courses/bulk-delete';
                    method = 'DELETE';
                    body = JSON.stringify({ course_ids: selectedIds });
                } else {
                    endpoint = 'https://abilityseminarsgroup.com/wp-json/asg/v1/courses/bulk-status';
                    method = 'PUT';
                    body = JSON.stringify({
                        course_ids: selectedIds,
                        status: action
                    });
                }

                const response = await fetch(endpoint, {
                    method: method,
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: body
                });

                const result = await response.json();

                if (result.success) {
                    const actionText = action === 'delete' ? 'deleted' : `moved to ${action}`;
                    showAlert('success', `${result.data.updated_count || result.data.deleted_count} course(s) ${actionText} successfully`);

                    // Exit bulk mode and reload courses
                    exitBulkMode();
                    loadCourses();

                } else {
                    throw new Error(result.error?.message || `Failed to ${action} courses`);
                }

            } catch (error) {
                console.error(`❌ Error en acción masiva ${action}:`, error);
                showAlert('danger', `Error performing bulk action: ${error.message}`);
            }
        }

        // ========================================
        // FUNCIONES GLOBALES
        // ========================================

        // Make functions available globally
        window.deleteCourse = deleteCourse;
        window.enterBulkMode = enterBulkMode;
        window.exitBulkMode = exitBulkMode;

        // ========================================
        // FIN DEL SISTEMA
        // ========================================

        console.log('🎉 Sistema de Listado de Cursos cargado completamente');
    </script>
</body>
</html>
