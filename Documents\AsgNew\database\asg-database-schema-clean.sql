-- ========================================
-- ESQUEMA DE BASE DE DATOS - ASG COURSES (VERSIÓN LIMPIA)
-- ========================================
-- 
-- Descripción: Estructura optimizada para sistema dinámico de cursos
-- Versión: 2.0.1 - SIN CONFLICTOS
-- Fecha: 2025-01-04
-- 
-- INSTRUCCIONES:
-- 1. Ejecutar paso a paso cada sección
-- 2. Si hay errores, continuar con la siguiente tabla
-- ========================================

-- ========================================
-- <PERSON><PERSON><PERSON>AR TABLAS EXISTENTES (OPCIONAL)
-- ========================================
-- DESCOMENTA ESTAS LÍNEAS SOLO SI QUIERES EMPEZAR DESDE CERO
-- DROP TABLE IF EXISTS `wpic_course_meta`;
-- DROP TABLE IF EXISTS `wpic_benefit_list`;
-- DROP TABLE IF EXISTS `wpic_learn_list`;
-- DROP TABLE IF EXISTS `wpic_lessons`;
-- DROP TABLE IF EXISTS `wpic_modules`;
-- DROP TABLE IF EXISTS `wpic_courses`;

-- ========================================
-- TABLA PRINCIPAL: CURSOS
-- ========================================
CREATE TABLE IF NOT EXISTS `wpic_courses` (
  `id_course` int(11) NOT NULL AUTO_INCREMENT,
  `code_course` varchar(255) NOT NULL COMMENT 'Código único: course_1, course_2, etc.',
  `name_course` varchar(255) NOT NULL COMMENT 'Título del curso',
  `description_course` text COMMENT 'Descripción completa del curso',
  `cover_img` text COMMENT 'URL de imagen de portada',
  `price_course` decimal(10,2) DEFAULT 0.00 COMMENT 'Precio del curso',
  `date_course` datetime DEFAULT CURRENT_TIMESTAMP COMMENT 'Fecha de creación',
  `category_course` varchar(100) DEFAULT 'general' COMMENT 'Categoría del curso',
  `duration_course` int(11) DEFAULT 0 COMMENT 'Duración en minutos',
  `language_course` varchar(10) DEFAULT 'es' COMMENT 'Idioma del curso',
  `status_course` enum('draft','published','archived') DEFAULT 'draft' COMMENT 'Estado del curso',
  `featured_course` tinyint(1) DEFAULT 0 COMMENT 'Curso destacado',
  `enrollment_count` int(11) DEFAULT 0 COMMENT 'Número de inscripciones',
  `rating_average` decimal(3,2) DEFAULT 0.00 COMMENT 'Calificación promedio',
  `rating_count` int(11) DEFAULT 0 COMMENT 'Número de calificaciones',
  `seo_title` varchar(255) COMMENT 'Título SEO',
  `seo_description` text COMMENT 'Descripción SEO',
  `seo_keywords` text COMMENT 'Palabras clave SEO',
  `id_user` int(11) NOT NULL COMMENT 'ID del usuario creador',
  `is_deleted` tinyint(1) DEFAULT 0 COMMENT 'Eliminación lógica',
  `created_at` datetime DEFAULT CURRENT_TIMESTAMP,
  `updated_at` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id_course`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Agregar índices por separado para evitar conflictos
ALTER TABLE `wpic_courses` ADD UNIQUE KEY `uk_code_course` (`code_course`);
ALTER TABLE `wpic_courses` ADD KEY `idx_courses_status` (`status_course`);
ALTER TABLE `wpic_courses` ADD KEY `idx_courses_category` (`category_course`);
ALTER TABLE `wpic_courses` ADD KEY `idx_courses_featured` (`featured_course`);
ALTER TABLE `wpic_courses` ADD KEY `idx_courses_user` (`id_user`);
ALTER TABLE `wpic_courses` ADD KEY `idx_courses_deleted` (`is_deleted`);

-- ========================================
-- TABLA: MÓDULOS DEL CURSO
-- ========================================
CREATE TABLE IF NOT EXISTS `wpic_modules` (
  `id_modules` int(11) NOT NULL AUTO_INCREMENT,
  `code_module` varchar(60) NOT NULL COMMENT 'Código único del módulo',
  `title_module` varchar(255) NOT NULL COMMENT 'Título del módulo',
  `description_module` text COMMENT 'Descripción del módulo',
  `cover_img` varchar(255) COMMENT 'Imagen del módulo',
  `duration_module` int(11) DEFAULT 0 COMMENT 'Duración en minutos',
  `order_module` int(11) DEFAULT 0 COMMENT 'Orden de visualización',
  `status_module` enum('draft','published','archived') DEFAULT 'draft',
  `code_course` varchar(255) NOT NULL COMMENT 'Referencia al curso',
  `id_user` int(11) NOT NULL COMMENT 'ID del usuario creador',
  `is_deleted` tinyint(1) DEFAULT 0,
  `date_module` datetime DEFAULT CURRENT_TIMESTAMP,
  `updated_at` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id_modules`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Agregar índices por separado
ALTER TABLE `wpic_modules` ADD UNIQUE KEY `uk_code_module` (`code_module`);
ALTER TABLE `wpic_modules` ADD KEY `idx_modules_course` (`code_course`);
ALTER TABLE `wpic_modules` ADD KEY `idx_modules_order` (`order_module`);
ALTER TABLE `wpic_modules` ADD KEY `idx_modules_status` (`status_module`);
ALTER TABLE `wpic_modules` ADD KEY `idx_modules_deleted` (`is_deleted`);

-- ========================================
-- TABLA: LECCIONES
-- ========================================
CREATE TABLE IF NOT EXISTS `wpic_lessons` (
  `id_lesson` int(11) NOT NULL AUTO_INCREMENT,
  `code_lesson` varchar(60) NOT NULL COMMENT 'Código único de la lección',
  `title_lesson` varchar(255) NOT NULL COMMENT 'Título de la lección',
  `description_lesson` text COMMENT 'Descripción de la lección',
  `content_lesson` longtext COMMENT 'Contenido completo de la lección',
  `video_url` text COMMENT 'URL del video de la lección',
  `cover_img` varchar(255) COMMENT 'Imagen de la lección',
  `duration_lesson` int(11) DEFAULT 0 COMMENT 'Duración en minutos',
  `order_lesson` int(11) DEFAULT 0 COMMENT 'Orden dentro del módulo',
  `lesson_type` enum('video','text','quiz','assignment') DEFAULT 'video',
  `is_preview` tinyint(1) DEFAULT 0 COMMENT 'Lección de vista previa gratuita',
  `code_module` varchar(60) NOT NULL COMMENT 'Referencia al módulo',
  `code_course` varchar(255) NOT NULL COMMENT 'Referencia al curso',
  `id_user` int(11) NOT NULL,
  `is_deleted` tinyint(1) DEFAULT 0,
  `date_lesson` datetime DEFAULT CURRENT_TIMESTAMP,
  `updated_at` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id_lesson`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Agregar índices por separado
ALTER TABLE `wpic_lessons` ADD UNIQUE KEY `uk_code_lesson` (`code_lesson`);
ALTER TABLE `wpic_lessons` ADD KEY `idx_lessons_module` (`code_module`);
ALTER TABLE `wpic_lessons` ADD KEY `idx_lessons_course` (`code_course`);
ALTER TABLE `wpic_lessons` ADD KEY `idx_lessons_order` (`order_lesson`);
ALTER TABLE `wpic_lessons` ADD KEY `idx_lessons_type` (`lesson_type`);
ALTER TABLE `wpic_lessons` ADD KEY `idx_lessons_preview` (`is_preview`);
ALTER TABLE `wpic_lessons` ADD KEY `idx_lessons_deleted` (`is_deleted`);

-- ========================================
-- TABLA: OBJETIVOS DE APRENDIZAJE
-- ========================================
CREATE TABLE IF NOT EXISTS `wpic_learn_list` (
  `id_learn` int(11) NOT NULL AUTO_INCREMENT,
  `code_learn` varchar(60) NOT NULL COMMENT 'Código único del objetivo',
  `name_list` varchar(255) NOT NULL COMMENT 'Descripción del objetivo',
  `icon_learn` varchar(100) COMMENT 'Icono del objetivo',
  `order_learn` int(11) DEFAULT 0 COMMENT 'Orden de visualización',
  `code_course` varchar(255) NOT NULL COMMENT 'Referencia al curso',
  `is_deleted` tinyint(1) DEFAULT 0,
  `date_list` datetime DEFAULT CURRENT_TIMESTAMP,
  `updated_at` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id_learn`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Agregar índices por separado
ALTER TABLE `wpic_learn_list` ADD UNIQUE KEY `uk_code_learn` (`code_learn`);
ALTER TABLE `wpic_learn_list` ADD KEY `idx_learn_course` (`code_course`);
ALTER TABLE `wpic_learn_list` ADD KEY `idx_learn_order` (`order_learn`);
ALTER TABLE `wpic_learn_list` ADD KEY `idx_learn_deleted` (`is_deleted`);

-- ========================================
-- TABLA: BENEFICIOS DEL CURSO
-- ========================================
CREATE TABLE IF NOT EXISTS `wpic_benefit_list` (
  `id_benefit` int(11) NOT NULL AUTO_INCREMENT,
  `code_benefit` varchar(60) NOT NULL COMMENT 'Código único del beneficio',
  `name_list` varchar(255) NOT NULL COMMENT 'Descripción del beneficio',
  `icon_benefit` varchar(100) COMMENT 'Icono del beneficio',
  `order_benefit` int(11) DEFAULT 0 COMMENT 'Orden de visualización',
  `code_course` varchar(255) NOT NULL COMMENT 'Referencia al curso',
  `is_deleted` tinyint(1) DEFAULT 0,
  `date_list` datetime DEFAULT CURRENT_TIMESTAMP,
  `updated_at` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id_benefit`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Agregar índices por separado
ALTER TABLE `wpic_benefit_list` ADD UNIQUE KEY `uk_code_benefit` (`code_benefit`);
ALTER TABLE `wpic_benefit_list` ADD KEY `idx_benefit_course` (`code_course`);
ALTER TABLE `wpic_benefit_list` ADD KEY `idx_benefit_order` (`order_benefit`);
ALTER TABLE `wpic_benefit_list` ADD KEY `idx_benefit_deleted` (`is_deleted`);

-- ========================================
-- TABLA: METADATOS ADICIONALES
-- ========================================
CREATE TABLE IF NOT EXISTS `wpic_course_meta` (
  `id_meta` int(11) NOT NULL AUTO_INCREMENT,
  `code_course` varchar(255) NOT NULL,
  `meta_key` varchar(255) NOT NULL,
  `meta_value` longtext,
  `meta_type` enum('string','number','boolean','json','array') DEFAULT 'string',
  `is_public` tinyint(1) DEFAULT 1 COMMENT 'Visible en API pública',
  `created_at` datetime DEFAULT CURRENT_TIMESTAMP,
  `updated_at` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id_meta`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Agregar índices por separado
ALTER TABLE `wpic_course_meta` ADD UNIQUE KEY `uk_course_meta` (`code_course`, `meta_key`);
ALTER TABLE `wpic_course_meta` ADD KEY `idx_meta_course` (`code_course`);
ALTER TABLE `wpic_course_meta` ADD KEY `idx_meta_key` (`meta_key`);
ALTER TABLE `wpic_course_meta` ADD KEY `idx_meta_public` (`is_public`);

-- ========================================
-- AGREGAR FOREIGN KEYS (AL FINAL)
-- ========================================
-- Solo agregar si las tablas están vacías o no hay conflictos
ALTER TABLE `wpic_modules` ADD CONSTRAINT `fk_modules_course` 
  FOREIGN KEY (`code_course`) REFERENCES `wpic_courses`(`code_course`) ON DELETE CASCADE;

ALTER TABLE `wpic_lessons` ADD CONSTRAINT `fk_lessons_module` 
  FOREIGN KEY (`code_module`) REFERENCES `wpic_modules`(`code_module`) ON DELETE CASCADE;

ALTER TABLE `wpic_lessons` ADD CONSTRAINT `fk_lessons_course` 
  FOREIGN KEY (`code_course`) REFERENCES `wpic_courses`(`code_course`) ON DELETE CASCADE;

ALTER TABLE `wpic_learn_list` ADD CONSTRAINT `fk_learn_course` 
  FOREIGN KEY (`code_course`) REFERENCES `wpic_courses`(`code_course`) ON DELETE CASCADE;

ALTER TABLE `wpic_benefit_list` ADD CONSTRAINT `fk_benefit_course` 
  FOREIGN KEY (`code_course`) REFERENCES `wpic_courses`(`code_course`) ON DELETE CASCADE;

ALTER TABLE `wpic_course_meta` ADD CONSTRAINT `fk_meta_course` 
  FOREIGN KEY (`code_course`) REFERENCES `wpic_courses`(`code_course`) ON DELETE CASCADE;

-- ========================================
-- DATOS DE EJEMPLO PARA TESTING
-- ========================================
INSERT IGNORE INTO `wpic_courses` (`code_course`, `name_course`, `description_course`, `price_course`, `category_course`, `status_course`, `id_user`) VALUES
('course_1', 'Como hacerte millonario?', 'Aprende las estrategias más efectivas para generar riqueza y alcanzar la libertad financiera.', 299.99, 'finanzas', 'published', 1),
('course_2', 'Marketing Digital Avanzado', 'Domina las técnicas más avanzadas del marketing digital y haz crecer tu negocio online.', 199.99, 'marketing', 'published', 1),
('course_3', 'Desarrollo Personal Integral', 'Transforma tu vida con técnicas probadas de crecimiento personal y liderazgo.', 149.99, 'desarrollo-personal', 'draft', 1);

-- Verificar que todo se creó correctamente
SELECT 'Tablas creadas correctamente' as status;
SHOW TABLES LIKE 'wpic_%';
