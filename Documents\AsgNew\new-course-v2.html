<!DOCTYPE html>
<html lang="es">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Crear Nuevo Curso - AbilitySeminarsGroup</title>
    
    <!-- Preload critical resources -->
    <link rel="preload" href="./assets/css/asg-design-system.css" as="style">
    <link rel="preload" href="./assets/js/asg-components.js" as="script">
    
    <!-- Styles -->
    <link href="./assets/css/asg-design-system.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.11.3/font/bootstrap-icons.css" rel="stylesheet">
    
    <!-- Meta tags -->
    <meta name="description" content="Crear nuevo curso - AbilitySeminarsGroup">
    <meta name="robots" content="noindex, nofollow">
    
    <!-- Favicon -->
    <link rel="icon" type="image/x-icon" href="https://abilityseminarsgroup.com/favicon.ico">
</head>
<body>
    <!-- Sidebar -->
    <aside class="asg-sidebar">
        <div class="asg-sidebar-header">
            <img src="https://abilityseminarsgroup.com/wp-content/uploads/2023/09/Asset-6-4-768x355.png" 
                 alt="AbilitySeminarsGroup" 
                 class="asg-sidebar-logo"
                 style="width: 130px; height: auto;">
        </div>
        
        <nav class="asg-sidebar-nav">
            <a href="https://abilityseminarsgroup.com/admin-dashboard/" class="asg-sidebar-item">
                <i class="bi bi-house asg-sidebar-icon"></i>
                <span>Dashboard</span>
            </a>
            
            <a href="https://abilityseminarsgroup.com/all-courses/" class="asg-sidebar-item">
                <i class="bi bi-collection asg-sidebar-icon"></i>
                <span>Todos los Cursos</span>
            </a>
            
            <a href="#" class="asg-sidebar-item active">
                <i class="bi bi-plus-circle asg-sidebar-icon"></i>
                <span>Nuevo Curso</span>
            </a>
            
            <div class="asg-sidebar-divider" style="height: 1px; background: var(--asg-gray-200); margin: var(--asg-space-4) var(--asg-space-6);"></div>
            
            <a href="#" class="asg-sidebar-item">
                <i class="bi bi-people asg-sidebar-icon"></i>
                <span>Estudiantes</span>
            </a>
            
            <a href="#" class="asg-sidebar-item">
                <i class="bi bi-bar-chart asg-sidebar-icon"></i>
                <span>Analíticas</span>
            </a>
            
            <a href="#" class="asg-sidebar-item">
                <i class="bi bi-gear asg-sidebar-icon"></i>
                <span>Configuración</span>
            </a>
        </nav>
    </aside>
    
    <!-- Sidebar Overlay (Mobile) -->
    <div class="asg-sidebar-overlay"></div>
    
    <!-- Main Content -->
    <main class="asg-main">
        <!-- Header -->
        <header class="asg-header">
            <div style="display: flex; align-items: center; justify-content: space-between;">
                <div style="display: flex; align-items: center; gap: var(--asg-space-4);">
                    <button class="asg-sidebar-toggle asg-btn asg-btn-secondary" style="display: none;">
                        <i class="bi bi-list"></i>
                    </button>
                    <h1 style="margin: 0; font-size: var(--asg-text-2xl); font-weight: var(--asg-font-semibold); color: var(--asg-gray-900);">
                        Crear Nuevo Curso
                    </h1>
                </div>
                
                <div style="display: flex; align-items: center; gap: var(--asg-space-3);">
                    <button class="asg-btn asg-btn-secondary" id="saveDraftBtn">
                        <i class="bi bi-save"></i>
                        <span class="btn-text">Guardar Borrador</span>
                    </button>
                    
                    <a href="https://abilityseminarsgroup.com/all-courses/" class="asg-btn asg-btn-secondary">
                        <i class="bi bi-x-circle"></i>
                        <span class="btn-text">Cancelar</span>
                    </a>
                </div>
            </div>
        </header>
        
        <!-- Content -->
        <div class="asg-content">
            <!-- Progress Steps -->
            <div class="asg-card" style="margin-bottom: var(--asg-space-6);">
                <div class="asg-card-body">
                    <div class="progress-steps" style="display: flex; align-items: center; justify-content: center; gap: var(--asg-space-4);">
                        <div class="step active" data-step="1" style="display: flex; align-items: center; gap: var(--asg-space-2);">
                            <div class="step-number" style="
                                width: 2rem; 
                                height: 2rem; 
                                border-radius: 50%; 
                                background: var(--asg-primary-600); 
                                color: white; 
                                display: flex; 
                                align-items: center; 
                                justify-content: center; 
                                font-weight: var(--asg-font-semibold);
                                font-size: var(--asg-text-sm);
                            ">1</div>
                            <span style="font-weight: var(--asg-font-medium); color: var(--asg-primary-600);">Información Básica</span>
                        </div>
                        
                        <div class="step-divider" style="width: 3rem; height: 2px; background: var(--asg-gray-200);"></div>
                        
                        <div class="step" data-step="2" style="display: flex; align-items: center; gap: var(--asg-space-2);">
                            <div class="step-number" style="
                                width: 2rem; 
                                height: 2rem; 
                                border-radius: 50%; 
                                background: var(--asg-gray-200); 
                                color: var(--asg-gray-600); 
                                display: flex; 
                                align-items: center; 
                                justify-content: center; 
                                font-weight: var(--asg-font-semibold);
                                font-size: var(--asg-text-sm);
                            ">2</div>
                            <span style="font-weight: var(--asg-font-medium); color: var(--asg-gray-600);">Contenido del Curso</span>
                        </div>
                        
                        <div class="step-divider" style="width: 3rem; height: 2px; background: var(--asg-gray-200);"></div>
                        
                        <div class="step" data-step="3" style="display: flex; align-items: center; gap: var(--asg-space-2);">
                            <div class="step-number" style="
                                width: 2rem; 
                                height: 2rem; 
                                border-radius: 50%; 
                                background: var(--asg-gray-200); 
                                color: var(--asg-gray-600); 
                                display: flex; 
                                align-items: center; 
                                justify-content: center; 
                                font-weight: var(--asg-font-semibold);
                                font-size: var(--asg-text-sm);
                            ">3</div>
                            <span style="font-weight: var(--asg-font-medium); color: var(--asg-gray-600);">Configuración y Precio</span>
                        </div>
                        
                        <div class="step-divider" style="width: 3rem; height: 2px; background: var(--asg-gray-200);"></div>
                        
                        <div class="step" data-step="4" style="display: flex; align-items: center; gap: var(--asg-space-2);">
                            <div class="step-number" style="
                                width: 2rem; 
                                height: 2rem; 
                                border-radius: 50%; 
                                background: var(--asg-gray-200); 
                                color: var(--asg-gray-600); 
                                display: flex; 
                                align-items: center; 
                                justify-content: center; 
                                font-weight: var(--asg-font-semibold);
                                font-size: var(--asg-text-sm);
                            ">4</div>
                            <span style="font-weight: var(--asg-font-medium); color: var(--asg-gray-600);">Revisión y Publicación</span>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- Form Container -->
            <form id="courseForm" style="display: grid; grid-template-columns: 1fr 300px; gap: var(--asg-space-6);">
                <!-- Main Form -->
                <div class="form-main">
                    <!-- Step 1: Basic Information -->
                    <div class="form-step active" data-step="1">
                        <div class="asg-card">
                            <div class="asg-card-header">
                                <h2 style="margin: 0; font-size: var(--asg-text-lg); font-weight: var(--asg-font-semibold);">
                                    Información Básica del Curso
                                </h2>
                                <p style="margin: var(--asg-space-2) 0 0 0; color: var(--asg-gray-600); font-size: var(--asg-text-sm);">
                                    Proporciona la información fundamental de tu curso
                                </p>
                            </div>
                            <div class="asg-card-body">
                                <div class="asg-form-group">
                                    <label for="courseName" class="asg-label">
                                        Título del Curso *
                                    </label>
                                    <input type="text" 
                                           id="courseName" 
                                           name="courseName" 
                                           class="asg-input" 
                                           placeholder="Ej: Como hacerte millonario en 30 días"
                                           required
                                           maxlength="255">
                                    <div class="field-help" style="margin-top: var(--asg-space-1); font-size: var(--asg-text-xs); color: var(--asg-gray-500);">
                                        Un título atractivo y descriptivo ayuda a atraer más estudiantes
                                    </div>
                                </div>
                                
                                <div class="asg-form-group">
                                    <label for="courseDescription" class="asg-label">
                                        Descripción del Curso *
                                    </label>
                                    <textarea id="courseDescription" 
                                              name="courseDescription" 
                                              class="asg-input asg-textarea" 
                                              placeholder="Describe qué aprenderán los estudiantes en este curso..."
                                              required
                                              rows="4"
                                              maxlength="1000"></textarea>
                                    <div class="field-help" style="margin-top: var(--asg-space-1); font-size: var(--asg-text-xs); color: var(--asg-gray-500);">
                                        <span id="descriptionCount">0</span>/1000 caracteres
                                    </div>
                                </div>
                                
                                <div style="display: grid; grid-template-columns: 1fr 1fr; gap: var(--asg-space-4);">
                                    <div class="asg-form-group">
                                        <label for="courseCategory" class="asg-label">
                                            Categoría *
                                        </label>
                                        <select id="courseCategory" name="courseCategory" class="asg-input asg-select" required>
                                            <option value="">Selecciona una categoría</option>
                                            <option value="finanzas">Finanzas</option>
                                            <option value="marketing">Marketing</option>
                                            <option value="desarrollo-personal">Desarrollo Personal</option>
                                            <option value="tecnologia">Tecnología</option>
                                            <option value="negocios">Negocios</option>
                                            <option value="salud">Salud y Bienestar</option>
                                            <option value="educacion">Educación</option>
                                            <option value="arte">Arte y Creatividad</option>
                                        </select>
                                    </div>
                                    
                                    <div class="asg-form-group">
                                        <label for="courseLanguage" class="asg-label">
                                            Idioma
                                        </label>
                                        <select id="courseLanguage" name="courseLanguage" class="asg-input asg-select">
                                            <option value="es" selected>Español</option>
                                            <option value="en">Inglés</option>
                                            <option value="fr">Francés</option>
                                            <option value="pt">Portugués</option>
                                        </select>
                                    </div>
                                </div>
                                
                                <div class="asg-form-group">
                                    <label for="courseImage" class="asg-label">
                                        Imagen de Portada
                                    </label>
                                    <div class="image-upload-area" style="
                                        border: 2px dashed var(--asg-gray-300);
                                        border-radius: var(--asg-radius);
                                        padding: var(--asg-space-8);
                                        text-align: center;
                                        cursor: pointer;
                                        transition: all var(--asg-transition);
                                    " onclick="document.getElementById('courseImageInput').click()">
                                        <div id="imagePreview" style="display: none;">
                                            <img id="previewImg" style="max-width: 100%; max-height: 200px; border-radius: var(--asg-radius);">
                                            <div style="margin-top: var(--asg-space-2);">
                                                <button type="button" class="asg-btn asg-btn-sm asg-btn-secondary" onclick="event.stopPropagation(); removeImage()">
                                                    <i class="bi bi-trash"></i> Eliminar
                                                </button>
                                            </div>
                                        </div>
                                        <div id="uploadPlaceholder">
                                            <i class="bi bi-cloud-upload" style="font-size: 3rem; color: var(--asg-gray-400); margin-bottom: var(--asg-space-2);"></i>
                                            <p style="margin: 0; color: var(--asg-gray-600); font-weight: var(--asg-font-medium);">
                                                Haz clic para subir una imagen
                                            </p>
                                            <p style="margin: var(--asg-space-1) 0 0 0; color: var(--asg-gray-500); font-size: var(--asg-text-sm);">
                                                JPG, PNG o GIF (máx. 5MB)
                                            </p>
                                        </div>
                                    </div>
                                    <input type="file" 
                                           id="courseImageInput" 
                                           name="courseImage" 
                                           accept="image/*" 
                                           style="display: none;"
                                           onchange="handleImageUpload(this)">
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <!-- Step 2: Course Content (will be added in next part) -->
                    <div class="form-step" data-step="2" style="display: none;">
                        <div class="asg-card">
                            <div class="asg-card-header">
                                <h2 style="margin: 0; font-size: var(--asg-text-lg); font-weight: var(--asg-font-semibold);">
                                    Contenido del Curso
                                </h2>
                                <p style="margin: var(--asg-space-2) 0 0 0; color: var(--asg-gray-600); font-size: var(--asg-text-sm);">
                                    Estructura el contenido de tu curso en módulos y lecciones
                                </p>
                            </div>
                            <div class="asg-card-body">
                                <p style="text-align: center; color: var(--asg-gray-500); padding: var(--asg-space-8);">
                                    Contenido del paso 2 - En desarrollo
                                </p>
                            </div>
                        </div>
                    </div>
                    
                    <!-- Step 3: Configuration (will be added in next part) -->
                    <div class="form-step" data-step="3" style="display: none;">
                        <div class="asg-card">
                            <div class="asg-card-header">
                                <h2 style="margin: 0; font-size: var(--asg-text-lg); font-weight: var(--asg-font-semibold);">
                                    Configuración y Precio
                                </h2>
                                <p style="margin: var(--asg-space-2) 0 0 0; color: var(--asg-gray-600); font-size: var(--asg-text-sm);">
                                    Configura el precio y opciones avanzadas
                                </p>
                            </div>
                            <div class="asg-card-body">
                                <p style="text-align: center; color: var(--asg-gray-500); padding: var(--asg-space-8);">
                                    Contenido del paso 3 - En desarrollo
                                </p>
                            </div>
                        </div>
                    </div>
                    
                    <!-- Step 4: Review (will be added in next part) -->
                    <div class="form-step" data-step="4" style="display: none;">
                        <div class="asg-card">
                            <div class="asg-card-header">
                                <h2 style="margin: 0; font-size: var(--asg-text-lg); font-weight: var(--asg-font-semibold);">
                                    Revisión y Publicación
                                </h2>
                                <p style="margin: var(--asg-space-2) 0 0 0; color: var(--asg-gray-600); font-size: var(--asg-text-sm);">
                                    Revisa toda la información antes de publicar
                                </p>
                            </div>
                            <div class="asg-card-body">
                                <p style="text-align: center; color: var(--asg-gray-500); padding: var(--asg-space-8);">
                                    Contenido del paso 4 - En desarrollo
                                </p>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- Sidebar Preview -->
                <div class="form-sidebar">
                    <div class="asg-card" style="position: sticky; top: var(--asg-space-6);">
                        <div class="asg-card-header">
                            <h3 style="margin: 0; font-size: var(--asg-text-base); font-weight: var(--asg-font-semibold);">
                                Vista Previa
                            </h3>
                        </div>
                        <div class="asg-card-body">
                            <div id="coursePreview">
                                <div class="preview-image" style="
                                    width: 100%;
                                    height: 120px;
                                    background: var(--asg-gray-200);
                                    border-radius: var(--asg-radius);
                                    margin-bottom: var(--asg-space-3);
                                    display: flex;
                                    align-items: center;
                                    justify-content: center;
                                    color: var(--asg-gray-500);
                                ">
                                    <i class="bi bi-image" style="font-size: 2rem;"></i>
                                </div>
                                
                                <h4 id="previewTitle" style="
                                    margin: 0 0 var(--asg-space-2) 0;
                                    font-size: var(--asg-text-base);
                                    font-weight: var(--asg-font-semibold);
                                    color: var(--asg-gray-500);
                                ">Título del curso</h4>
                                
                                <p id="previewDescription" style="
                                    margin: 0 0 var(--asg-space-3) 0;
                                    font-size: var(--asg-text-sm);
                                    color: var(--asg-gray-500);
                                    line-height: 1.4;
                                ">Descripción del curso...</p>
                                
                                <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: var(--asg-space-3);">
                                    <span id="previewCategory" class="asg-badge asg-badge-gray">Categoría</span>
                                    <span id="previewPrice" style="font-weight: var(--asg-font-bold); color: var(--asg-primary-600);">€0.00</span>
                                </div>
                                
                                <div style="font-size: var(--asg-text-xs); color: var(--asg-gray-500);">
                                    <div style="margin-bottom: var(--asg-space-1);">
                                        <i class="bi bi-collection"></i> <span id="previewModules">0 módulos</span>
                                    </div>
                                    <div style="margin-bottom: var(--asg-space-1);">
                                        <i class="bi bi-play-circle"></i> <span id="previewLessons">0 lecciones</span>
                                    </div>
                                    <div>
                                        <i class="bi bi-globe"></i> <span id="previewLanguage">Español</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </form>
            
            <!-- Navigation Buttons -->
            <div style="display: flex; justify-content: space-between; margin-top: var(--asg-space-6);">
                <button type="button" id="prevStepBtn" class="asg-btn asg-btn-secondary" style="display: none;">
                    <i class="bi bi-chevron-left"></i>
                    Anterior
                </button>
                
                <div style="margin-left: auto; display: flex; gap: var(--asg-space-3);">
                    <button type="button" id="nextStepBtn" class="asg-btn asg-btn-primary">
                        Siguiente
                        <i class="bi bi-chevron-right"></i>
                    </button>
                    
                    <button type="submit" id="publishBtn" class="asg-btn asg-btn-success" style="display: none;">
                        <i class="bi bi-check-circle"></i>
                        Publicar Curso
                    </button>
                </div>
            </div>
        </div>
    </main>
    
    <!-- Scripts -->
    <script src="./assets/js/asg-components.js"></script>
    <script src="./assets/js/new-course-v2.js"></script>
    
    <!-- Styles -->
    <style>
        .image-upload-area:hover {
            border-color: var(--asg-primary-500);
            background-color: var(--asg-primary-50);
        }
        
        .step.active .step-number {
            background: var(--asg-primary-600) !important;
            color: white !important;
        }
        
        .step.active span {
            color: var(--asg-primary-600) !important;
        }
        
        .step.completed .step-number {
            background: var(--asg-success) !important;
            color: white !important;
        }
        
        .step.completed span {
            color: var(--asg-success) !important;
        }
        
        @media (max-width: 1024px) {
            .asg-sidebar-toggle {
                display: flex !important;
            }
            
            .btn-text {
                display: none;
            }
            
            #courseForm {
                grid-template-columns: 1fr !important;
            }
            
            .form-sidebar {
                order: -1;
            }
            
            .progress-steps {
                flex-direction: column !important;
                gap: var(--asg-space-2) !important;
            }
            
            .step-divider {
                width: 2px !important;
                height: 1rem !important;
            }
        }
    </style>
</body>
</html>
