<!DOCTYPE html>
<html lang="es">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Dashboard ASG - API Connected</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.11.3/font/bootstrap-icons.css">
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background-color: #f8f9fa;
            overflow-x: hidden;
        }

        /* Navbar Styles */
        .navbar {
            background: linear-gradient(135deg, #1e3a5f 0%, #2563eb 100%);
            height: 70px;
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            z-index: 1000;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }

        .navbar-brand img {
            width: 130px;
            height: auto;
        }

        .navbar-nav .nav-link {
            color: white !important;
            font-weight: 500;
            padding: 0.5rem 1rem;
            border-radius: 6px;
            transition: all 0.3s ease;
        }

        .navbar-nav .nav-link:hover {
            background-color: rgba(255,255,255,0.1);
            transform: translateY(-1px);
        }

        /* Main Content */
        .main-content {
            margin-top: 70px;
            padding: 2rem;
            min-height: calc(100vh - 70px);
        }

        /* Dashboard Header */
        .dashboard-header {
            background: white;
            border-radius: 12px;
            padding: 2rem;
            margin-bottom: 2rem;
            box-shadow: 0 2px 10px rgba(0,0,0,0.05);
            border: 1px solid #e9ecef;
        }

        .dashboard-title {
            font-size: 2rem;
            font-weight: 700;
            color: #1e3a5f;
            margin-bottom: 0.5rem;
        }

        .dashboard-subtitle {
            color: #6c757d;
            font-size: 1.1rem;
        }

        /* Stats Cards */
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
            gap: 1.5rem;
            margin-bottom: 2rem;
        }

        .stat-card {
            background: white;
            border-radius: 12px;
            padding: 1.5rem;
            box-shadow: 0 2px 10px rgba(0,0,0,0.05);
            border: 1px solid #e9ecef;
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
        }

        .stat-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 20px rgba(0,0,0,0.1);
        }

        .stat-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 4px;
            background: var(--card-color, #2563eb);
        }

        .stat-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 1rem;
        }

        .stat-icon {
            width: 3rem;
            height: 3rem;
            border-radius: 10px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 1.5rem;
            background: var(--icon-bg, #e3f2fd);
            color: var(--icon-color, #2563eb);
        }

        .stat-number {
            font-size: 2.5rem;
            font-weight: 700;
            color: #1e3a5f;
            margin: 0.5rem 0;
            line-height: 1;
        }

        .stat-label {
            font-size: 0.9rem;
            color: #6c757d;
            font-weight: 500;
            margin: 0;
        }

        .stat-change {
            display: flex;
            align-items: center;
            gap: 0.5rem;
            margin-top: 1rem;
            font-size: 0.85rem;
        }

        .stat-change.positive {
            color: #10b981;
        }

        .stat-change.negative {
            color: #ef4444;
        }

        /* Recent Courses */
        .recent-courses {
            background: white;
            border-radius: 12px;
            padding: 1.5rem;
            box-shadow: 0 2px 10px rgba(0,0,0,0.05);
            border: 1px solid #e9ecef;
        }

        .section-title {
            font-size: 1.25rem;
            font-weight: 600;
            color: #1e3a5f;
            margin-bottom: 1.5rem;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .course-item {
            display: flex;
            align-items: center;
            gap: 1rem;
            padding: 1rem;
            border-radius: 8px;
            transition: all 0.3s ease;
            border-bottom: 1px solid #f1f3f4;
        }

        .course-item:hover {
            background-color: #f8f9fa;
        }

        .course-item:last-child {
            border-bottom: none;
        }

        .course-image {
            width: 60px;
            height: 45px;
            border-radius: 6px;
            background-size: cover;
            background-position: center;
            flex-shrink: 0;
        }

        .course-info {
            flex: 1;
            min-width: 0;
        }

        .course-title {
            font-weight: 600;
            color: #1e3a5f;
            margin-bottom: 0.25rem;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
        }

        .course-meta {
            font-size: 0.85rem;
            color: #6c757d;
        }

        .course-status {
            padding: 0.25rem 0.75rem;
            border-radius: 20px;
            font-size: 0.75rem;
            font-weight: 500;
            text-transform: uppercase;
        }

        .status-published {
            background-color: #d1fae5;
            color: #065f46;
        }

        .status-draft {
            background-color: #fef3c7;
            color: #92400e;
        }

        .course-price {
            font-weight: 600;
            color: #2563eb;
            margin-left: 1rem;
        }

        /* Loading States */
        .loading-skeleton {
            background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
            background-size: 200% 100%;
            animation: loading 1.5s infinite;
            border-radius: 4px;
        }

        @keyframes loading {
            0% { background-position: 200% 0; }
            100% { background-position: -200% 0; }
        }

        .loading-number {
            height: 2.5rem;
            width: 100px;
            margin: 0.5rem 0;
        }

        .loading-text {
            height: 1rem;
            width: 150px;
        }

        /* Responsive */
        @media (max-width: 768px) {
            .main-content {
                padding: 1rem;
            }
            
            .dashboard-header {
                padding: 1.5rem;
            }
            
            .stats-grid {
                grid-template-columns: 1fr;
            }
            
            .course-item {
                flex-direction: column;
                align-items: flex-start;
                gap: 0.75rem;
            }
            
            .course-image {
                width: 100%;
                height: 120px;
            }
        }

        /* API Status Indicator */
        .api-status {
            position: fixed;
            bottom: 20px;
            right: 20px;
            padding: 0.5rem 1rem;
            border-radius: 20px;
            font-size: 0.8rem;
            font-weight: 500;
            z-index: 1000;
            transition: all 0.3s ease;
        }

        .api-status.online {
            background-color: #d1fae5;
            color: #065f46;
        }

        .api-status.offline {
            background-color: #fef2f2;
            color: #991b1b;
        }
    </style>
</head>
<body>
    <!-- Navbar -->
    <nav class="navbar navbar-expand-lg">
        <div class="container-fluid">
            <a class="navbar-brand" href="#">
                <img src="https://abilityseminarsgroup.com/wp-content/uploads/2023/09/Asset-6-4-768x355.png" alt="ASG Logo">
            </a>
            
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav ms-auto">
                    <li class="nav-item">
                        <a class="nav-link active" href="dashboard-api.html">
                            <i class="bi bi-speedometer2"></i> Dashboard
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="all-courses-api.html">
                            <i class="bi bi-collection"></i> Todos los Cursos
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="new-course-api.html">
                            <i class="bi bi-plus-circle"></i> Nuevo Curso
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="#" onclick="ASG_API.checkApiStatus()">
                            <i class="bi bi-gear"></i> API Test
                        </a>
                    </li>
                </ul>
            </div>
        </div>
    </nav>

    <!-- Main Content -->
    <div class="main-content">
        <!-- Dashboard Header -->
        <div class="dashboard-header">
            <h1 class="dashboard-title">📊 Dashboard ASG</h1>
            <p class="dashboard-subtitle">Panel de control y estadísticas del sistema de cursos</p>
            <div class="mt-3">
                <button class="btn btn-primary" onclick="refreshDashboard()">
                    <i class="bi bi-arrow-clockwise"></i> Actualizar
                </button>
                <button class="btn btn-outline-primary ms-2" onclick="toggleApiMode()">
                    <i class="bi bi-toggle-on"></i> <span id="apiModeText">Modo Público</span>
                </button>
            </div>
        </div>

        <!-- Stats Grid -->
        <div class="stats-grid">
            <!-- Total Courses -->
            <div class="stat-card" style="--card-color: #2563eb; --icon-bg: #dbeafe; --icon-color: #2563eb;">
                <div class="stat-header">
                    <div>
                        <p class="stat-label">Total de Cursos</p>
                        <div class="stat-number" id="totalCourses">
                            <div class="loading-skeleton loading-number"></div>
                        </div>
                    </div>
                    <div class="stat-icon">📚</div>
                </div>
                <div class="stat-change positive" id="coursesChange">
                    <i class="bi bi-arrow-up"></i>
                    <span>Cargando...</span>
                </div>
            </div>

            <!-- Published Courses -->
            <div class="stat-card" style="--card-color: #10b981; --icon-bg: #d1fae5; --icon-color: #10b981;">
                <div class="stat-header">
                    <div>
                        <p class="stat-label">Cursos Publicados</p>
                        <div class="stat-number" id="publishedCourses">
                            <div class="loading-skeleton loading-number"></div>
                        </div>
                    </div>
                    <div class="stat-icon">✅</div>
                </div>
                <div class="stat-change positive" id="publishedPercentage">
                    <i class="bi bi-check-circle"></i>
                    <span>Cargando...</span>
                </div>
            </div>

            <!-- Draft Courses -->
            <div class="stat-card" style="--card-color: #f59e0b; --icon-bg: #fef3c7; --icon-color: #f59e0b;">
                <div class="stat-header">
                    <div>
                        <p class="stat-label">Borradores</p>
                        <div class="stat-number" id="draftCourses">
                            <div class="loading-skeleton loading-number"></div>
                        </div>
                    </div>
                    <div class="stat-icon">✏️</div>
                </div>
                <div class="stat-change" id="draftPercentage">
                    <i class="bi bi-pencil"></i>
                    <span>Cargando...</span>
                </div>
            </div>

            <!-- Revenue -->
            <div class="stat-card" style="--card-color: #06b6d4; --icon-bg: #cffafe; --icon-color: #06b6d4;">
                <div class="stat-header">
                    <div>
                        <p class="stat-label">Ingresos Potenciales</p>
                        <div class="stat-number" id="totalRevenue">
                            <div class="loading-skeleton loading-number"></div>
                        </div>
                    </div>
                    <div class="stat-icon">💰</div>
                </div>
                <div class="stat-change" id="avgPrice">
                    <i class="bi bi-currency-euro"></i>
                    <span>Cargando...</span>
                </div>
            </div>
        </div>

        <!-- Recent Courses -->
        <div class="recent-courses">
            <div class="section-title">
                <span>📚 Cursos Recientes</span>
                <a href="all-courses-api.html" class="btn btn-outline-primary btn-sm">
                    Ver todos <i class="bi bi-arrow-right"></i>
                </a>
            </div>
            <div id="recentCourses">
                <!-- Loading skeleton -->
                <div class="course-item">
                    <div class="loading-skeleton" style="width: 60px; height: 45px; border-radius: 6px;"></div>
                    <div style="flex: 1;">
                        <div class="loading-skeleton loading-text" style="margin-bottom: 0.5rem;"></div>
                        <div class="loading-skeleton" style="height: 0.8rem; width: 200px;"></div>
                    </div>
                </div>
                <div class="course-item">
                    <div class="loading-skeleton" style="width: 60px; height: 45px; border-radius: 6px;"></div>
                    <div style="flex: 1;">
                        <div class="loading-skeleton loading-text" style="margin-bottom: 0.5rem;"></div>
                        <div class="loading-skeleton" style="height: 0.8rem; width: 180px;"></div>
                    </div>
                </div>
                <div class="course-item">
                    <div class="loading-skeleton" style="width: 60px; height: 45px; border-radius: 6px;"></div>
                    <div style="flex: 1;">
                        <div class="loading-skeleton loading-text" style="margin-bottom: 0.5rem;"></div>
                        <div class="loading-skeleton" style="height: 0.8rem; width: 160px;"></div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- API Status Indicator -->
    <div class="api-status offline" id="apiStatus">
        <i class="bi bi-wifi-off"></i> Conectando...
    </div>

    <!-- Scripts -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.3/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://abilityseminarsgroup.com/wp-content/uploads/asg/js/asg-config.js"></script>
    <script src="https://abilityseminarsgroup.com/wp-content/uploads/asg/js/asg-api-client.js"></script>
    
    <script>
        // Variables globales
        let dashboardData = {
            stats: {},
            courses: [],
            isLoading: true
        };

        // Inicializar dashboard
        document.addEventListener('DOMContentLoaded', function() {
            console.log('🚀 Dashboard API initialized');
            initializeDashboard();
        });

        async function initializeDashboard() {
            try {
                // Verificar estado de API
                const apiOnline = await ASG_API.checkApiStatus();
                updateApiStatus(apiOnline);
                
                // Cargar datos
                await loadDashboardData();
                
            } catch (error) {
                console.error('❌ Error initializing dashboard:', error);
                updateApiStatus(false);
                loadMockData();
            }
        }

        async function loadDashboardData() {
            try {
                dashboardData.isLoading = true;
                
                // Cargar estadísticas
                const stats = await ASG_API.getDashboardStats();
                dashboardData.stats = stats;
                updateStatsDisplay(stats);
                
                // Cargar cursos recientes
                const courses = await ASG_API.getCourses({ per_page: 5 });
                dashboardData.courses = Array.isArray(courses) ? courses : courses.data || [];
                updateRecentCourses(dashboardData.courses);
                
                dashboardData.isLoading = false;
                
            } catch (error) {
                console.error('❌ Error loading dashboard data:', error);
                loadMockData();
            }
        }

        function loadMockData() {
            const mockStats = ASG_API.getMockDashboardStats();
            const mockCourses = ASG_API.getMockCourses().slice(0, 5);
            
            updateStatsDisplay(mockStats);
            updateRecentCourses(mockCourses);
            
            dashboardData.isLoading = false;
        }

        function updateStatsDisplay(stats) {
            // Animar números
            animateNumber('totalCourses', stats.totalCourses || 0);
            animateNumber('publishedCourses', stats.publishedCourses || 0);
            animateNumber('draftCourses', stats.draftCourses || 0);
            
            // Formatear ingresos
            document.getElementById('totalRevenue').textContent = formatCurrency(stats.totalRevenue || 0);
            
            // Calcular porcentajes
            const total = stats.totalCourses || 0;
            const publishedPercentage = total > 0 ? Math.round((stats.publishedCourses / total) * 100) : 0;
            const draftPercentage = total > 0 ? Math.round((stats.draftCourses / total) * 100) : 0;
            
            document.getElementById('publishedPercentage').innerHTML = `
                <i class="bi bi-check-circle"></i>
                <span>${publishedPercentage}% del total</span>
            `;
            
            document.getElementById('draftPercentage').innerHTML = `
                <i class="bi bi-pencil"></i>
                <span>${draftPercentage}% pendientes</span>
            `;
            
            document.getElementById('avgPrice').innerHTML = `
                <i class="bi bi-currency-euro"></i>
                <span>Promedio: ${formatCurrency(stats.avgPrice || 0)}</span>
            `;
            
            document.getElementById('coursesChange').innerHTML = `
                <i class="bi bi-arrow-up"></i>
                <span>+${stats.coursesChange || 0}% vs mes anterior</span>
            `;
        }

        function updateRecentCourses(courses) {
            const container = document.getElementById('recentCourses');
            
            if (!courses || courses.length === 0) {
                container.innerHTML = `
                    <div class="text-center py-4">
                        <i class="bi bi-collection" style="font-size: 3rem; color: #6c757d;"></i>
                        <h5 class="mt-3 text-muted">No hay cursos recientes</h5>
                        <p class="text-muted">Crea tu primer curso para comenzar</p>
                        <a href="new-course-api.html" class="btn btn-primary">
                            <i class="bi bi-plus-circle"></i> Crear Curso
                        </a>
                    </div>
                `;
                return;
            }
            
            container.innerHTML = courses.map(course => `
                <div class="course-item" onclick="viewCourse('${course.code_course}')">
                    <div class="course-image" style="background-image: url('${course.cover_img || 'https://images.unsplash.com/photo-1516321318423-f06f85e504b3?w=400&h=300&fit=crop'}')"></div>
                    <div class="course-info">
                        <div class="course-title">${course.name_course}</div>
                        <div class="course-meta">
                            ${getCategoryIcon(course.category_course)} ${getCategoryName(course.category_course)} • 
                            ${course.module_count || 0} módulos • 
                            ${course.lesson_count || 0} lecciones
                        </div>
                    </div>
                    <div class="course-status status-${course.status_course}">
                        ${getStatusName(course.status_course)}
                    </div>
                    <div class="course-price">
                        ${formatCurrency(course.price_course)}
                    </div>
                </div>
            `).join('');
        }

        function updateApiStatus(isOnline) {
            const statusEl = document.getElementById('apiStatus');
            if (isOnline) {
                statusEl.className = 'api-status online';
                statusEl.innerHTML = '<i class="bi bi-wifi"></i> API Online';
            } else {
                statusEl.className = 'api-status offline';
                statusEl.innerHTML = '<i class="bi bi-wifi-off"></i> Modo Offline';
            }
        }

        // Utilidades
        function animateNumber(elementId, targetValue) {
            const element = document.getElementById(elementId);
            if (!element) return;
            
            const startValue = 0;
            const duration = 1000;
            const startTime = performance.now();
            
            function animate(currentTime) {
                const elapsed = currentTime - startTime;
                const progress = Math.min(elapsed / duration, 1);
                const currentValue = startValue + (targetValue - startValue) * progress;
                
                element.textContent = Math.round(currentValue);
                
                if (progress < 1) {
                    requestAnimationFrame(animate);
                }
            }
            
            requestAnimationFrame(animate);
        }

        function formatCurrency(amount) {
            return new Intl.NumberFormat('es-ES', {
                style: 'currency',
                currency: 'EUR'
            }).format(amount || 0);
        }

        function getCategoryIcon(category) {
            const icons = {
                'finanzas': '💰',
                'marketing': '📱',
                'desarrollo-personal': '🧠',
                'tecnologia': '💻',
                'negocios': '💼',
                'salud': '🏥'
            };
            return icons[category] || '📚';
        }

        function getCategoryName(category) {
            const names = {
                'finanzas': 'Finanzas',
                'marketing': 'Marketing',
                'desarrollo-personal': 'Desarrollo Personal',
                'tecnologia': 'Tecnología',
                'negocios': 'Negocios',
                'salud': 'Salud'
            };
            return names[category] || 'General';
        }

        function getStatusName(status) {
            const names = {
                'published': 'Publicado',
                'draft': 'Borrador',
                'archived': 'Archivado'
            };
            return names[status] || 'Borrador';
        }

        // Acciones
        async function refreshDashboard() {
            const btn = event.target;
            const originalText = btn.innerHTML;
            
            btn.innerHTML = '<i class="bi bi-arrow-clockwise spin"></i> Actualizando...';
            btn.disabled = true;
            
            try {
                await loadDashboardData();
                
                // Mostrar notificación de éxito
                showNotification('✅ Dashboard actualizado correctamente', 'success');
                
            } catch (error) {
                showNotification('❌ Error al actualizar el dashboard', 'error');
            } finally {
                btn.innerHTML = originalText;
                btn.disabled = false;
            }
        }

        function toggleApiMode() {
            const currentMode = ASG_API.usePublicEndpoints;
            ASG_API.setPublicMode(!currentMode);
            
            const modeText = document.getElementById('apiModeText');
            modeText.textContent = ASG_API.usePublicEndpoints ? 'Modo Público' : 'Modo Admin';
            
            // Recargar datos con el nuevo modo
            refreshDashboard();
        }

        function viewCourse(courseCode) {
            window.open(`/course?id=${courseCode}`, '_blank');
        }

        function showNotification(message, type = 'info') {
            // Crear notificación toast simple
            const toast = document.createElement('div');
            toast.style.cssText = `
                position: fixed;
                top: 90px;
                right: 20px;
                background: ${type === 'success' ? '#10b981' : type === 'error' ? '#ef4444' : '#06b6d4'};
                color: white;
                padding: 1rem 1.5rem;
                border-radius: 8px;
                box-shadow: 0 4px 12px rgba(0,0,0,0.15);
                z-index: 9999;
                animation: slideIn 0.3s ease-out;
            `;
            toast.textContent = message;
            
            document.body.appendChild(toast);
            
            setTimeout(() => {
                toast.style.animation = 'slideOut 0.3s ease-in';
                setTimeout(() => document.body.removeChild(toast), 300);
            }, 3000);
        }

        // CSS para animaciones
        const style = document.createElement('style');
        style.textContent = `
            @keyframes slideIn {
                from { transform: translateX(100%); opacity: 0; }
                to { transform: translateX(0); opacity: 1; }
            }
            @keyframes slideOut {
                from { transform: translateX(0); opacity: 1; }
                to { transform: translateX(100%); opacity: 0; }
            }
            .spin {
                animation: spin 1s linear infinite;
            }
            @keyframes spin {
                from { transform: rotate(0deg); }
                to { transform: rotate(360deg); }
            }
        `;
        document.head.appendChild(style);
    </script>
</body>
</html>
