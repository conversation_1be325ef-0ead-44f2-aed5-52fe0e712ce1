# 🗄️ DECISIONES DE DISEÑO - BASE DE DATOS ASG

## 📋 **DECISIONES TÉCNICAS CLAVE**

### **1. SISTEMA DE CÓDIGOS ÚNICOS**
**Decisión:** Usar `code_course` (course_1, course_2) como clave principal de negocio
**Razón:** 
- ✅ Soporte nativo para sistema dinámico `/course?id=course_1`
- ✅ URLs amigables y predecibles
- ✅ Fácil integración con frontend público
- ✅ Independiente de IDs auto-incrementales

### **2. ESTRUCTURA JERÁRQUICA**
```
courses (1) → modules (N) → lessons (N)
courses (1) → learn_objectives (N)
courses (1) → benefits (N)
```
**Decisión:** Relaciones por `code_course` en lugar de `id_course`
**Razón:**
- ✅ Consistencia con sistema dinámico
- ✅ Queries más simples desde frontend
- ✅ Mejor rendimiento en JOINs complejos

### **3. ELIMINACIÓN LÓGICA**
**Decisión:** Campo `is_deleted` en todas las tablas
**Razón:**
- ✅ Preservar integridad referencial
- ✅ Auditoría y recuperación de datos
- ✅ Mejor experiencia de usuario (deshacer)

### **4. METADATOS FLEXIBLES**
**Decisión:** Tabla `wpic_course_meta` para datos adicionales
**Razón:**
- ✅ Extensibilidad sin modificar esquema
- ✅ Soporte para configuraciones personalizadas
- ✅ Separación de datos estructurados vs. flexibles

### **5. CAMPOS DE AUDITORÍA**
**Decisión:** `created_at`, `updated_at`, `id_user` en todas las tablas
**Razón:**
- ✅ Trazabilidad completa de cambios
- ✅ Soporte para colaboración multi-usuario
- ✅ Debugging y análisis de uso

## 🎯 **OPTIMIZACIONES IMPLEMENTADAS**

### **ÍNDICES ESTRATÉGICOS**
```sql
-- Búsquedas frecuentes
KEY `idx_status` (`status_course`)
KEY `idx_category` (`category_course`)
KEY `idx_featured` (`featured_course`)

-- Relaciones jerárquicas
KEY `idx_course` (`code_course`)
KEY `idx_order` (`order_module`)

-- Eliminación lógica
KEY `idx_deleted` (`is_deleted`)
```

### **CONSTRAINTS DE INTEGRIDAD**
- ✅ **FOREIGN KEYS** con CASCADE DELETE
- ✅ **UNIQUE CONSTRAINTS** en códigos de negocio
- ✅ **ENUM VALUES** para estados controlados

### **TIPOS DE DATOS OPTIMIZADOS**
- ✅ **VARCHAR(255)** para títulos y nombres
- ✅ **TEXT/LONGTEXT** para contenido variable
- ✅ **DECIMAL(10,2)** para precios precisos
- ✅ **DATETIME** con timezone support

## 🚀 **SOPORTE PARA SISTEMA DINÁMICO**

### **QUERY PRINCIPAL PARA FRONTEND**
```sql
-- Obtener curso completo para /course?id=course_1
SELECT 
    c.*,
    m.*, 
    l.*,
    obj.*,
    ben.*
FROM wpic_courses c
LEFT JOIN wpic_modules m ON c.code_course = m.code_course
LEFT JOIN wpic_lessons l ON m.code_module = l.code_module
LEFT JOIN wpic_learn_list obj ON c.code_course = obj.code_course
LEFT JOIN wpic_benefit_list ben ON c.code_course = ben.code_course
WHERE c.code_course = 'course_1' 
  AND c.status_course = 'published'
  AND c.is_deleted = 0
ORDER BY m.order_module, l.order_lesson, obj.order_learn, ben.order_benefit;
```

### **ESCALABILIDAD**
- ✅ **Particionamiento** por `code_course` si es necesario
- ✅ **Caching** a nivel de curso completo
- ✅ **Índices compuestos** para queries complejas

## 📊 **MÉTRICAS Y ANALYTICS**

### **CAMPOS PARA ESTADÍSTICAS**
```sql
enrollment_count    -- Número de inscripciones
rating_average      -- Calificación promedio
rating_count        -- Número de calificaciones
```

### **SOPORTE SEO**
```sql
seo_title          -- Título optimizado para SEO
seo_description    -- Meta descripción
seo_keywords       -- Palabras clave
```

## 🔧 **CONFIGURACIÓN TÉCNICA**

### **ENGINE Y CHARSET**
```sql
ENGINE=InnoDB                    -- Transacciones y FK
CHARSET=utf8mb4                  -- Soporte Unicode completo
COLLATE=utf8mb4_unicode_ci       -- Ordenamiento internacional
```

### **COMPATIBILIDAD WORDPRESS**
- ✅ Prefijo `wpic_` existente mantenido
- ✅ Estructura compatible con WP_Query si es necesario
- ✅ Hooks de WordPress para sincronización

## 🎯 **PRÓXIMOS PASOS**

1. **Migración de datos** existentes al nuevo esquema
2. **Creación de índices** adicionales según patrones de uso
3. **Implementación de triggers** para auditoría automática
4. **Configuración de backups** automáticos por tabla

---

**Autor:** ASG Team  
**Fecha:** 2025-01-04  
**Versión:** 2.0.0
