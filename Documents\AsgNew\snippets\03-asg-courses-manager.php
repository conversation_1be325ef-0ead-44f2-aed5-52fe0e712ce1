<?php
/**
 * ========================================
 * ASG GESTIÓN DE CURSOS - SNIPPET 3
 * ========================================
 *
 * Descripción: Gestión completa de cursos con filtros y búsqueda
 * Uso: Copiar y pegar en WP Code Snippets
 * Tipo: PHP Snippet
 * Ubicación: Solo en admin
 *
 * Shortcode: [asg_courses_manager]
 *
 * Autor: ASG Team
 * Fecha: 2025-01-04
 * Versión: 2.0.0 - CODE SNIPPETS
 */

// Prevenir ejecución directa
if (!defined('ABSPATH')) {
    exit;
}

/**
 * ========================================
 * SHORTCODE DE GESTIÓN DE CURSOS
 * ========================================
 */
add_shortcode('asg_courses_manager', 'asg_render_courses_manager');

function asg_render_courses_manager($atts) {
    // Solo para usuarios con permisos
    if (!current_user_can('manage_options') && !current_user_can('edit_posts')) {
        return '<p>No tienes permisos para acceder a esta sección.</p>';
    }

    ob_start();
    ?>

    <!-- CSS de Gestión de Cursos -->
    <style>
    .asg-courses-manager {
        font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
        max-width: 1400px;
        margin: 0 auto;
        padding: 2rem 1rem;
    }

    .asg-manager-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 2rem;
        padding-bottom: 1rem;
        border-bottom: 1px solid var(--asg-gray-200);
    }

    .asg-manager-title {
        margin: 0;
        font-size: 2rem;
        font-weight: 600;
        color: var(--asg-gray-900);
        display: flex;
        align-items: center;
        gap: 1rem;
    }

    .asg-courses-count {
        font-size: 1rem;
        font-weight: 500;
        background: var(--asg-primary);
        color: white;
        padding: 0.25rem 0.75rem;
        border-radius: 9999px;
    }

    .asg-manager-actions {
        display: flex;
        gap: 1rem;
        align-items: center;
    }

    .asg-filters-card {
        background: white;
        border: 1px solid var(--asg-gray-200);
        border-radius: var(--asg-radius);
        padding: 1.5rem;
        margin-bottom: 2rem;
        box-shadow: var(--asg-shadow);
    }

    .asg-filters-grid {
        display: grid;
        grid-template-columns: 2fr 1fr 1fr auto;
        gap: 1rem;
        align-items: end;
    }

    .asg-form-group {
        display: flex;
        flex-direction: column;
        gap: 0.5rem;
    }

    .asg-label {
        font-size: 0.875rem;
        font-weight: 500;
        color: var(--asg-gray-700);
    }

    .asg-input {
        padding: 0.75rem;
        border: 1px solid var(--asg-gray-300);
        border-radius: var(--asg-radius);
        font-size: 0.875rem;
        transition: all var(--asg-transition);
    }

    .asg-input:focus {
        outline: none;
        border-color: var(--asg-primary);
        box-shadow: 0 0 0 3px rgba(37, 99, 235, 0.1);
    }

    .asg-search-container {
        position: relative;
    }

    .asg-search-icon {
        position: absolute;
        left: 0.75rem;
        top: 50%;
        transform: translateY(-50%);
        color: var(--asg-gray-400);
        pointer-events: none;
    }

    .asg-search-input {
        padding-left: 2.5rem;
    }

    .asg-view-toggle {
        display: flex;
        border: 1px solid var(--asg-gray-300);
        border-radius: var(--asg-radius);
        overflow: hidden;
    }

    .asg-view-btn {
        padding: 0.75rem;
        border: none;
        background: white;
        color: var(--asg-gray-600);
        cursor: pointer;
        transition: all var(--asg-transition);
        border-right: 1px solid var(--asg-gray-300);
    }

    .asg-view-btn:last-child {
        border-right: none;
    }

    .asg-view-btn.active {
        background: var(--asg-primary);
        color: white;
    }

    .asg-courses-grid {
        display: grid;
        grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
        gap: 1.5rem;
        margin-bottom: 2rem;
    }

    .asg-courses-list {
        display: flex;
        flex-direction: column;
        gap: 1rem;
        margin-bottom: 2rem;
    }

    .asg-course-card {
        background: white;
        border: 1px solid var(--asg-gray-200);
        border-radius: var(--asg-radius);
        overflow: hidden;
        box-shadow: var(--asg-shadow);
        transition: all var(--asg-transition);
        cursor: pointer;
    }

    .asg-course-card:hover {
        box-shadow: 0 4px 6px -1px rgb(0 0 0 / 0.1);
        transform: translateY(-2px);
    }

    .asg-course-image {
        width: 100%;
        height: 200px;
        background: var(--asg-gray-200);
        background-size: cover;
        background-position: center;
        position: relative;
    }

    .asg-course-status {
        position: absolute;
        top: 1rem;
        right: 1rem;
    }

    .asg-course-info {
        padding: 1.5rem;
    }

    .asg-course-title {
        margin: 0 0 0.5rem 0;
        font-size: 1.125rem;
        font-weight: 600;
        color: var(--asg-gray-900);
        line-height: 1.4;
    }

    .asg-course-description {
        margin: 0 0 1rem 0;
        font-size: 0.875rem;
        color: var(--asg-gray-600);
        line-height: 1.5;
        display: -webkit-box;
        -webkit-line-clamp: 3;
        -webkit-box-orient: vertical;
        overflow: hidden;
    }

    .asg-course-meta {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 1rem;
        font-size: 0.875rem;
        color: var(--asg-gray-500);
    }

    .asg-course-price {
        font-size: 1.25rem;
        font-weight: 700;
        color: var(--asg-primary);
    }

    .asg-course-actions {
        display: flex;
        gap: 0.5rem;
        padding-top: 1rem;
        border-top: 1px solid var(--asg-gray-200);
    }

    .asg-btn-sm {
        padding: 0.5rem 1rem;
        font-size: 0.875rem;
    }

    .asg-btn-secondary {
        background: white;
        color: var(--asg-gray-700);
        border: 1px solid var(--asg-gray-300);
    }

    .asg-btn-secondary:hover {
        background: var(--asg-gray-50);
    }

    .asg-pagination {
        display: flex;
        justify-content: center;
        align-items: center;
        gap: 1rem;
        margin-top: 2rem;
    }

    .asg-pagination-info {
        font-size: 0.875rem;
        color: var(--asg-gray-600);
        margin-top: 1rem;
        text-align: center;
    }

    .asg-empty-state {
        text-align: center;
        padding: 4rem 2rem;
        color: var(--asg-gray-500);
    }

    .asg-empty-icon {
        font-size: 4rem;
        margin-bottom: 1rem;
    }

    .asg-loading {
        text-align: center;
        padding: 4rem 2rem;
        color: var(--asg-gray-600);
    }

    .asg-spinner {
        width: 3rem;
        height: 3rem;
        border: 3px solid var(--asg-gray-200);
        border-top: 3px solid var(--asg-primary);
        border-radius: 50%;
        animation: asg-spin 1s linear infinite;
        margin: 0 auto 1rem;
    }

    @keyframes asg-spin {
        0% { transform: rotate(0deg); }
        100% { transform: rotate(360deg); }
    }

    @media (max-width: 768px) {
        .asg-courses-manager {
            padding: 1rem 0.5rem;
        }

        .asg-manager-header {
            flex-direction: column;
            gap: 1rem;
            align-items: stretch;
        }

        .asg-filters-grid {
            grid-template-columns: 1fr;
            gap: 1rem;
        }

        .asg-courses-grid {
            grid-template-columns: 1fr;
        }

        .asg-manager-actions {
            justify-content: center;
        }
    }
    </style>

    <!-- HTML de Gestión de Cursos -->
    <div class="asg-courses-manager">
        <div class="asg-manager-header">
            <h1 class="asg-manager-title">
                📚 Gestión de Cursos
                <span class="asg-courses-count" id="coursesCount">0 cursos</span>
            </h1>
            <div class="asg-manager-actions">
                <button class="asg-btn asg-btn-secondary" onclick="asgRefreshCourses()">
                    🔄 Actualizar
                </button>
                <button class="asg-btn asg-btn-primary" onclick="asgCreateNewCourse()">
                    ➕ Nuevo Curso
                </button>
            </div>
        </div>

        <!-- Filtros -->
        <div class="asg-filters-card">
            <div class="asg-filters-grid">
                <div class="asg-form-group">
                    <label class="asg-label">Buscar cursos</label>
                    <div class="asg-search-container">
                        <span class="asg-search-icon">🔍</span>
                        <input type="text"
                               id="searchInput"
                               class="asg-input asg-search-input"
                               placeholder="Buscar por título, descripción o categoría...">
                    </div>
                </div>

                <div class="asg-form-group">
                    <label class="asg-label">Estado</label>
                    <select id="statusFilter" class="asg-input">
                        <option value="">Todos los estados</option>
                        <option value="published">Publicados</option>
                        <option value="draft">Borradores</option>
                        <option value="archived">Archivados</option>
                    </select>
                </div>

                <div class="asg-form-group">
                    <label class="asg-label">Categoría</label>
                    <select id="categoryFilter" class="asg-input">
                        <option value="">Todas las categorías</option>
                        <option value="finanzas">Finanzas</option>
                        <option value="marketing">Marketing</option>
                        <option value="desarrollo-personal">Desarrollo Personal</option>
                        <option value="tecnologia">Tecnología</option>
                        <option value="negocios">Negocios</option>
                    </select>
                </div>

                <div class="asg-form-group">
                    <label class="asg-label">Vista</label>
                    <div class="asg-view-toggle">
                        <button class="asg-view-btn active" data-view="grid">📊</button>
                        <button class="asg-view-btn" data-view="list">📋</button>
                    </div>
                </div>
            </div>
        </div>

        <!-- Contenedor de Cursos -->
        <div id="coursesContainer">
            <div class="asg-loading">
                <div class="asg-spinner"></div>
                <p>Cargando cursos...</p>
            </div>
        </div>

        <!-- Paginación -->
        <div id="pagination" class="asg-pagination" style="display: none;">
            <button id="prevPage" class="asg-btn asg-btn-secondary" disabled>
                ← Anterior
            </button>
            <div id="pageNumbers"></div>
            <button id="nextPage" class="asg-btn asg-btn-secondary">
                Siguiente →
            </button>
        </div>
        <div id="paginationInfo" class="asg-pagination-info"></div>
    </div>

    <!-- JavaScript de Gestión de Cursos -->
    <script>
    // Variables globales
    let asgCoursesData = {
        courses: [],
        filteredCourses: [],
        currentPage: 1,
        perPage: 12,
        totalPages: 1,
        currentView: 'grid',
        filters: {
            search: '',
            status: '',
            category: ''
        }
    };

    let asgSearchTimeout = null;

    // Inicializar gestión de cursos
    document.addEventListener('DOMContentLoaded', function() {
        if (typeof window.ASG_CONFIG !== 'undefined') {
            asgInitCoursesManager();
        } else {
            setTimeout(asgInitCoursesManager, 500);
        }
    });

    function asgInitCoursesManager() {
        asgSetupEventListeners();
        asgLoadCourses();
    }

    function asgSetupEventListeners() {
        // Búsqueda con debounce
        const searchInput = document.getElementById('searchInput');
        if (searchInput) {
            searchInput.addEventListener('input', function(e) {
                clearTimeout(asgSearchTimeout);
                asgSearchTimeout = setTimeout(() => {
                    asgCoursesData.filters.search = e.target.value;
                    asgFilterCourses();
                }, 300);
            });
        }

        // Filtros
        const statusFilter = document.getElementById('statusFilter');
        if (statusFilter) {
            statusFilter.addEventListener('change', function(e) {
                asgCoursesData.filters.status = e.target.value;
                asgFilterCourses();
            });
        }

        const categoryFilter = document.getElementById('categoryFilter');
        if (categoryFilter) {
            categoryFilter.addEventListener('change', function(e) {
                asgCoursesData.filters.category = e.target.value;
                asgFilterCourses();
            });
        }

        // Toggle de vista
        const viewButtons = document.querySelectorAll('.asg-view-btn');
        viewButtons.forEach(btn => {
            btn.addEventListener('click', function() {
                viewButtons.forEach(b => b.classList.remove('active'));
                this.classList.add('active');
                asgCoursesData.currentView = this.dataset.view;
                asgRenderCourses();
            });
        });

        // Paginación
        const prevBtn = document.getElementById('prevPage');
        const nextBtn = document.getElementById('nextPage');

        if (prevBtn) {
            prevBtn.addEventListener('click', () => asgChangePage(asgCoursesData.currentPage - 1));
        }

        if (nextBtn) {
            nextBtn.addEventListener('click', () => asgChangePage(asgCoursesData.currentPage + 1));
        }
    }

    async function asgLoadCourses() {
        try {
            const response = await fetch(window.ASG_CONFIG.apiUrl + '/admin/courses?per_page=100', {
                headers: {
                    'X-WP-Nonce': window.ASG_CONFIG.nonce
                }
            });

            if (response.ok) {
                const data = await response.json();
                asgCoursesData.courses = data.data || [];
            } else {
                throw new Error('API no disponible');
            }
        } catch (error) {
            console.warn('API no disponible, usando datos simulados:', error);
            asgCoursesData.courses = asgGetMockCourses();
        }

        asgFilterCourses();
    }

    function asgFilterCourses() {
        let filtered = [...asgCoursesData.courses];

        // Filtro de búsqueda
        if (asgCoursesData.filters.search) {
            const search = asgCoursesData.filters.search.toLowerCase();
            filtered = filtered.filter(course =>
                course.name_course.toLowerCase().includes(search) ||
                (course.description_course && course.description_course.toLowerCase().includes(search)) ||
                (course.category_course && course.category_course.toLowerCase().includes(search))
            );
        }

        // Filtro de estado
        if (asgCoursesData.filters.status) {
            filtered = filtered.filter(course => course.status_course === asgCoursesData.filters.status);
        }

        // Filtro de categoría
        if (asgCoursesData.filters.category) {
            filtered = filtered.filter(course => course.category_course === asgCoursesData.filters.category);
        }

        asgCoursesData.filteredCourses = filtered;
        asgCoursesData.currentPage = 1;
        asgCoursesData.totalPages = Math.ceil(filtered.length / asgCoursesData.perPage);

        asgUpdateCoursesCount();
        asgRenderCourses();
        asgUpdatePagination();
    }

    function asgUpdateCoursesCount() {
        const countEl = document.getElementById('coursesCount');
        if (countEl) {
            const total = asgCoursesData.filteredCourses.length;
            countEl.textContent = total === 1 ? '1 curso' : `${total} cursos`;
        }
    }

    function asgRenderCourses() {
        const container = document.getElementById('coursesContainer');
        if (!container) return;

        const startIndex = (asgCoursesData.currentPage - 1) * asgCoursesData.perPage;
        const endIndex = startIndex + asgCoursesData.perPage;
        const coursesToShow = asgCoursesData.filteredCourses.slice(startIndex, endIndex);

        if (coursesToShow.length === 0) {
            container.innerHTML = asgRenderEmptyState();
            return;
        }

        const containerClass = asgCoursesData.currentView === 'grid' ? 'asg-courses-grid' : 'asg-courses-list';
        container.innerHTML = `<div class="${containerClass}">${coursesToShow.map(asgRenderCourseCard).join('')}</div>`;
    }

    function asgRenderCourseCard(course) {
        const statusBadge = asgGetStatusBadge(course.status_course);
        const categoryLabel = asgGetCategoryLabel(course.category_course);
        const imageUrl = course.cover_img || 'https://images.unsplash.com/photo-1516321318423-f06f85e504b3?w=400&h=300&fit=crop';

        return `
            <div class="asg-course-card" onclick="asgViewCourse('${course.code_course}')">
                <div class="asg-course-image" style="background-image: url('${imageUrl}')">
                    <div class="asg-course-status">${statusBadge}</div>
                </div>
                <div class="asg-course-info">
                    <h3 class="asg-course-title">${course.name_course}</h3>
                    <p class="asg-course-description">${course.description_course || 'Sin descripción'}</p>
                    <div class="asg-course-meta">
                        <span>📂 ${categoryLabel}</span>
                        <span>📚 ${course.module_count || 0} módulos</span>
                    </div>
                    <div class="asg-course-price">${asgFormatCurrency(course.price_course)}</div>
                    <div class="asg-course-actions" onclick="event.stopPropagation()">
                        <button class="asg-btn asg-btn-sm asg-btn-secondary" onclick="asgEditCourse('${course.code_course}')">
                            ✏️ Editar
                        </button>
                        <button class="asg-btn asg-btn-sm asg-btn-secondary" onclick="asgDuplicateCourse('${course.code_course}')">
                            📋 Duplicar
                        </button>
                        <button class="asg-btn asg-btn-sm asg-btn-secondary" onclick="asgDeleteCourse('${course.code_course}')">
                            🗑️ Eliminar
                        </button>
                    </div>
                </div>
            </div>
        `;
    }

    function asgRenderEmptyState() {
        const hasFilters = asgCoursesData.filters.search || asgCoursesData.filters.status || asgCoursesData.filters.category;

        if (hasFilters) {
            return `
                <div class="asg-empty-state">
                    <div class="asg-empty-icon">🔍</div>
                    <h3>No se encontraron cursos</h3>
                    <p>Intenta ajustar los filtros de búsqueda</p>
                    <button class="asg-btn asg-btn-secondary" onclick="asgClearFilters()">
                        Limpiar filtros
                    </button>
                </div>
            `;
        } else {
            return `
                <div class="asg-empty-state">
                    <div class="asg-empty-icon">📚</div>
                    <h3>No hay cursos creados</h3>
                    <p>Comienza creando tu primer curso</p>
                    <button class="asg-btn asg-btn-primary" onclick="asgCreateNewCourse()">
                        ➕ Crear primer curso
                    </button>
                </div>
            `;
        }
    }

    function asgUpdatePagination() {
        const pagination = document.getElementById('pagination');
        const paginationInfo = document.getElementById('paginationInfo');
        const prevBtn = document.getElementById('prevPage');
        const nextBtn = document.getElementById('nextPage');

        if (asgCoursesData.totalPages <= 1) {
            pagination.style.display = 'none';
            paginationInfo.textContent = '';
            return;
        }

        pagination.style.display = 'flex';

        // Botones anterior/siguiente
        prevBtn.disabled = asgCoursesData.currentPage === 1;
        nextBtn.disabled = asgCoursesData.currentPage === asgCoursesData.totalPages;

        // Información de paginación
        const startItem = (asgCoursesData.currentPage - 1) * asgCoursesData.perPage + 1;
        const endItem = Math.min(asgCoursesData.currentPage * asgCoursesData.perPage, asgCoursesData.filteredCourses.length);
        paginationInfo.textContent = `Mostrando ${startItem}-${endItem} de ${asgCoursesData.filteredCourses.length} cursos`;
    }

    function asgChangePage(page) {
        if (page < 1 || page > asgCoursesData.totalPages) return;

        asgCoursesData.currentPage = page;
        asgRenderCourses();
        asgUpdatePagination();

        // Scroll al inicio
        document.querySelector('.asg-courses-manager').scrollIntoView({ behavior: 'smooth' });
    }

    // Utilidades
    function asgGetStatusBadge(status) {
        const badges = {
            published: '<span class="asg-badge asg-badge-success">Publicado</span>',
            draft: '<span class="asg-badge asg-badge-warning">Borrador</span>',
            archived: '<span class="asg-badge asg-badge-gray">Archivado</span>'
        };
        return badges[status] || badges.draft;
    }

    function asgGetCategoryLabel(category) {
        const categories = {
            finanzas: 'Finanzas',
            marketing: 'Marketing',
            'desarrollo-personal': 'Desarrollo Personal',
            tecnologia: 'Tecnología',
            negocios: 'Negocios'
        };
        return categories[category] || category || 'Sin categoría';
    }

    function asgFormatCurrency(amount) {
        return new Intl.NumberFormat('es-ES', {
            style: 'currency',
            currency: 'EUR'
        }).format(amount || 0);
    }

    function asgGetMockCourses() {
        return [
            {
                code_course: 'course_1',
                name_course: 'Como hacerte millonario?',
                description_course: 'Aprende las estrategias más efectivas para generar riqueza y alcanzar la libertad financiera.',
                cover_img: 'https://images.unsplash.com/photo-1579621970563-ebec7560ff3e?w=400&h=300&fit=crop',
                status_course: 'published',
                category_course: 'finanzas',
                price_course: 299.99,
                module_count: 8,
                created_at: '2024-12-15 10:30:00'
            },
            {
                code_course: 'course_2',
                name_course: 'Marketing Digital Avanzado',
                description_course: 'Domina las técnicas más avanzadas del marketing digital y multiplica tus ventas online.',
                cover_img: 'https://images.unsplash.com/photo-1460925895917-afdab827c52f?w=400&h=300&fit=crop',
                status_course: 'published',
                category_course: 'marketing',
                price_course: 199.99,
                module_count: 6,
                created_at: '2024-12-10 14:20:00'
            },
            {
                code_course: 'course_3',
                name_course: 'Desarrollo Personal Integral',
                description_course: 'Transforma tu vida con técnicas probadas de crecimiento personal y liderazgo.',
                cover_img: 'https://images.unsplash.com/photo-1516321318423-f06f85e504b3?w=400&h=300&fit=crop',
                status_course: 'draft',
                category_course: 'desarrollo-personal',
                price_course: 149.99,
                module_count: 4,
                created_at: '2024-12-05 09:15:00'
            }
        ];
    }

    // Acciones
    function asgRefreshCourses() {
        asgLoadCourses();
        alert('🔄 Lista de cursos actualizada');
    }

    function asgCreateNewCourse() {
        if (confirm('¿Quieres crear un nuevo curso?')) {
            window.location.href = '/wp-admin/admin.php?page=asg-new-course';
        }
    }

    function asgViewCourse(courseCode) {
        window.open(`/course?id=${courseCode}`, '_blank');
    }

    function asgEditCourse(courseCode) {
        window.location.href = `/wp-admin/admin.php?page=asg-edit-course&course=${courseCode}`;
    }

    function asgDuplicateCourse(courseCode) {
        if (confirm('¿Quieres duplicar este curso?')) {
            alert('🔄 Funcionalidad de duplicación en desarrollo');
        }
    }

    function asgDeleteCourse(courseCode) {
        const course = asgCoursesData.courses.find(c => c.code_course === courseCode);
        if (!course) return;

        if (confirm(`¿Estás seguro de que quieres eliminar el curso "${course.name_course}"?`)) {
            alert('🗑️ Funcionalidad de eliminación en desarrollo');
        }
    }

    function asgClearFilters() {
        document.getElementById('searchInput').value = '';
        document.getElementById('statusFilter').value = '';
        document.getElementById('categoryFilter').value = '';

        asgCoursesData.filters = {
            search: '',
            status: '',
            category: ''
        };

        asgFilterCourses();
    }
    </script>

    <?php
    return ob_get_clean();
}

/**
 * ========================================
 * PÁGINA DE ADMINISTRACIÓN
 * ========================================
 */
add_action('admin_menu', function() {
    add_submenu_page(
        'asg-dashboard',
        'Gestión de Cursos',
        'Todos los Cursos',
        'manage_options',
        'asg-all-courses',
        'asg_admin_courses_page'
    );
});

function asg_admin_courses_page() {
    echo '<div class="wrap">';
    echo do_shortcode('[asg_courses_manager]');
    echo '</div>';
}
?>