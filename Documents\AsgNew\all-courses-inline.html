<!DOCTYPE html>
<html lang="es">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Todos los Cursos - ASG</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.11.3/font/bootstrap-icons.css">
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background-color: #f8f9fa;
            overflow-x: hidden;
        }

        /* Navbar Styles */
        .navbar {
            background: linear-gradient(135deg, #1e3a5f 0%, #2563eb 100%);
            height: 70px;
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            z-index: 1000;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }

        .navbar-brand img {
            width: 130px;
            height: auto;
        }

        .navbar-nav .nav-link {
            color: white !important;
            font-weight: 500;
            padding: 0.5rem 1rem;
            border-radius: 6px;
            transition: all 0.3s ease;
        }

        .navbar-nav .nav-link:hover {
            background-color: rgba(255,255,255,0.1);
            transform: translateY(-1px);
        }

        .navbar-nav .nav-link.active {
            background-color: rgba(255,255,255,0.2);
        }

        /* Main Content */
        .main-content {
            margin-top: 70px;
            padding: 2rem;
            min-height: calc(100vh - 70px);
        }

        /* Page Header */
        .page-header {
            background: white;
            border-radius: 12px;
            padding: 2rem;
            margin-bottom: 2rem;
            box-shadow: 0 2px 10px rgba(0,0,0,0.05);
            border: 1px solid #e9ecef;
        }

        .page-title {
            font-size: 2rem;
            font-weight: 700;
            color: #1e3a5f;
            margin-bottom: 0.5rem;
        }

        .page-subtitle {
            color: #6c757d;
            font-size: 1.1rem;
        }

        /* Filters Section */
        .filters-section {
            background: white;
            border-radius: 12px;
            padding: 1.5rem;
            margin-bottom: 2rem;
            box-shadow: 0 2px 10px rgba(0,0,0,0.05);
            border: 1px solid #e9ecef;
        }

        .filter-row {
            display: grid;
            grid-template-columns: 2fr 1fr 1fr 1fr auto;
            gap: 1rem;
            align-items: end;
        }

        .filter-group label {
            font-weight: 500;
            color: #374151;
            margin-bottom: 0.5rem;
            display: block;
        }

        .filter-input {
            width: 100%;
            padding: 0.75rem;
            border: 1px solid #d1d5db;
            border-radius: 6px;
            font-size: 0.9rem;
            transition: border-color 0.3s ease;
        }

        .filter-input:focus {
            outline: none;
            border-color: #2563eb;
            box-shadow: 0 0 0 3px rgba(37, 99, 235, 0.1);
        }

        /* Stats Bar */
        .stats-bar {
            background: white;
            border-radius: 12px;
            padding: 1rem 1.5rem;
            margin-bottom: 2rem;
            box-shadow: 0 2px 10px rgba(0,0,0,0.05);
            border: 1px solid #e9ecef;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .stats-info {
            display: flex;
            gap: 2rem;
            align-items: center;
        }

        .stat-item {
            display: flex;
            align-items: center;
            gap: 0.5rem;
            font-size: 0.9rem;
            color: #6b7280;
        }

        .stat-number {
            font-weight: 600;
            color: #1f2937;
        }

        /* View Toggle */
        .view-toggle {
            display: flex;
            background: #f3f4f6;
            border-radius: 6px;
            padding: 0.25rem;
        }

        .view-btn {
            padding: 0.5rem 1rem;
            border: none;
            background: transparent;
            border-radius: 4px;
            cursor: pointer;
            transition: all 0.3s ease;
            color: #6b7280;
        }

        .view-btn.active {
            background: white;
            color: #2563eb;
            box-shadow: 0 1px 3px rgba(0,0,0,0.1);
        }

        /* Courses Grid */
        .courses-container {
            background: white;
            border-radius: 12px;
            padding: 1.5rem;
            box-shadow: 0 2px 10px rgba(0,0,0,0.05);
            border: 1px solid #e9ecef;
        }

        .courses-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(320px, 1fr));
            gap: 1.5rem;
        }

        .courses-list {
            display: flex;
            flex-direction: column;
            gap: 1rem;
        }

        /* Course Card */
        .course-card {
            border: 1px solid #e5e7eb;
            border-radius: 12px;
            overflow: hidden;
            transition: all 0.3s ease;
            background: white;
            cursor: pointer;
        }

        .course-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(0,0,0,0.1);
            border-color: #2563eb;
        }

        .course-image {
            width: 100%;
            height: 180px;
            background-size: cover;
            background-position: center;
            position: relative;
        }

        .course-status-badge {
            position: absolute;
            top: 0.75rem;
            right: 0.75rem;
            padding: 0.25rem 0.75rem;
            border-radius: 20px;
            font-size: 0.75rem;
            font-weight: 500;
            text-transform: uppercase;
        }

        .status-published {
            background-color: #d1fae5;
            color: #065f46;
        }

        .status-draft {
            background-color: #fef3c7;
            color: #92400e;
        }

        .status-archived {
            background-color: #f3f4f6;
            color: #374151;
        }

        .course-content {
            padding: 1.25rem;
        }

        .course-category {
            display: inline-flex;
            align-items: center;
            gap: 0.5rem;
            font-size: 0.8rem;
            color: #6b7280;
            margin-bottom: 0.75rem;
        }

        .course-title {
            font-size: 1.1rem;
            font-weight: 600;
            color: #1f2937;
            margin-bottom: 0.5rem;
            line-height: 1.4;
        }

        .course-description {
            font-size: 0.9rem;
            color: #6b7280;
            line-height: 1.5;
            margin-bottom: 1rem;
            display: -webkit-box;
            -webkit-line-clamp: 2;
            -webkit-box-orient: vertical;
            overflow: hidden;
        }

        .course-meta {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 1rem;
            font-size: 0.85rem;
            color: #6b7280;
        }

        .course-stats {
            display: flex;
            gap: 1rem;
        }

        .course-price {
            font-size: 1.1rem;
            font-weight: 700;
            color: #2563eb;
        }

        .course-actions {
            display: flex;
            gap: 0.5rem;
            padding-top: 1rem;
            border-top: 1px solid #f3f4f6;
        }

        .btn-action {
            flex: 1;
            padding: 0.5rem;
            border: 1px solid #d1d5db;
            background: white;
            border-radius: 6px;
            font-size: 0.85rem;
            cursor: pointer;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 0.25rem;
        }

        .btn-action:hover {
            background: #f9fafb;
            border-color: #2563eb;
            color: #2563eb;
        }

        .btn-action.primary {
            background: #2563eb;
            color: white;
            border-color: #2563eb;
        }

        .btn-action.primary:hover {
            background: #1d4ed8;
        }

        /* List View */
        .course-list-item {
            display: flex;
            align-items: center;
            gap: 1rem;
            padding: 1rem;
            border: 1px solid #e5e7eb;
            border-radius: 8px;
            background: white;
            transition: all 0.3s ease;
            cursor: pointer;
        }

        .course-list-item:hover {
            border-color: #2563eb;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }

        .course-list-image {
            width: 80px;
            height: 60px;
            border-radius: 6px;
            background-size: cover;
            background-position: center;
            flex-shrink: 0;
        }

        .course-list-content {
            flex: 1;
            min-width: 0;
        }

        .course-list-title {
            font-weight: 600;
            color: #1f2937;
            margin-bottom: 0.25rem;
        }

        .course-list-meta {
            font-size: 0.85rem;
            color: #6b7280;
        }

        .course-list-price {
            font-weight: 700;
            color: #2563eb;
            margin-right: 1rem;
        }

        .course-list-actions {
            display: flex;
            gap: 0.5rem;
        }

        /* Loading States */
        .loading-skeleton {
            background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
            background-size: 200% 100%;
            animation: loading 1.5s infinite;
            border-radius: 4px;
        }

        @keyframes loading {
            0% { background-position: 200% 0; }
            100% { background-position: -200% 0; }
        }

        /* Empty State */
        .empty-state {
            text-align: center;
            padding: 3rem 1rem;
            color: #6b7280;
        }

        .empty-state i {
            font-size: 4rem;
            margin-bottom: 1rem;
            color: #d1d5db;
        }

        /* Toast Animations */
        @keyframes slideIn {
            from { transform: translateX(100%); opacity: 0; }
            to { transform: translateX(0); opacity: 1; }
        }

        @keyframes slideOut {
            from { transform: translateX(0); opacity: 1; }
            to { transform: translateX(100%); opacity: 0; }
        }

        .spin {
            animation: spin 1s linear infinite;
        }

        @keyframes spin {
            from { transform: rotate(0deg); }
            to { transform: rotate(360deg); }
        }

        /* Responsive */
        @media (max-width: 768px) {
            .main-content {
                padding: 1rem;
            }
            
            .page-header {
                padding: 1.5rem;
            }
            
            .filter-row {
                grid-template-columns: 1fr;
                gap: 1rem;
            }
            
            .stats-bar {
                flex-direction: column;
                gap: 1rem;
                align-items: stretch;
            }
            
            .stats-info {
                justify-content: space-between;
            }
            
            .courses-grid {
                grid-template-columns: 1fr;
            }
            
            .course-list-item {
                flex-direction: column;
                align-items: stretch;
                gap: 1rem;
            }
            
            .course-list-image {
                width: 100%;
                height: 120px;
            }
        }
    </style>
</head>
<body>
    <!-- Navbar -->
    <nav class="navbar navbar-expand-lg">
        <div class="container-fluid">
            <a class="navbar-brand" href="dashboard-inline.html">
                <img src="https://abilityseminarsgroup.com/wp-content/uploads/2023/09/Asset-6-4-768x355.png" alt="ASG Logo">
            </a>
            
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav ms-auto">
                    <li class="nav-item">
                        <a class="nav-link" href="dashboard-inline.html">
                            <i class="bi bi-speedometer2"></i> Dashboard
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link active" href="all-courses-inline.html">
                            <i class="bi bi-collection"></i> Todos los Cursos
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="new-course-inline.html">
                            <i class="bi bi-plus-circle"></i> Nuevo Curso
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="#" onclick="showNotification('⚙️ Configuración próximamente', 'info')">
                            <i class="bi bi-gear"></i> Configuración
                        </a>
                    </li>
                </ul>
            </div>
        </div>
    </nav>

    <!-- Main Content -->
    <div class="main-content">
        <!-- Page Header -->
        <div class="page-header">
            <h1 class="page-title">📚 Todos los Cursos</h1>
            <p class="page-subtitle">Gestiona y organiza todos tus cursos desde un solo lugar</p>
            <div class="mt-3">
                <button class="btn btn-primary" onclick="refreshCourses()">
                    <i class="bi bi-arrow-clockwise"></i> Actualizar
                </button>
                <button class="btn btn-success ms-2" onclick="navigateToNewCourse()">
                    <i class="bi bi-plus-circle"></i> Nuevo Curso
                </button>
                <button class="btn btn-outline-secondary ms-2" onclick="exportCourses()">
                    <i class="bi bi-download"></i> Exportar
                </button>
            </div>
        </div>

        <!-- Filters Section -->
        <div class="filters-section">
            <div class="filter-row">
                <div class="filter-group">
                    <label for="searchInput">🔍 Buscar cursos</label>
                    <input type="text" id="searchInput" class="filter-input" placeholder="Buscar por título, descripción o categoría...">
                </div>
                <div class="filter-group">
                    <label for="statusFilter">Estado</label>
                    <select id="statusFilter" class="filter-input">
                        <option value="">Todos los estados</option>
                        <option value="published">✅ Publicados</option>
                        <option value="draft">✏️ Borradores</option>
                        <option value="archived">📦 Archivados</option>
                    </select>
                </div>
                <div class="filter-group">
                    <label for="categoryFilter">Categoría</label>
                    <select id="categoryFilter" class="filter-input">
                        <option value="">Todas las categorías</option>
                        <option value="finanzas">💰 Finanzas</option>
                        <option value="marketing">📱 Marketing</option>
                        <option value="desarrollo-personal">🧠 Desarrollo Personal</option>
                        <option value="tecnologia">💻 Tecnología</option>
                        <option value="negocios">💼 Negocios</option>
                        <option value="salud">🏥 Salud</option>
                    </select>
                </div>
                <div class="filter-group">
                    <label for="sortFilter">Ordenar por</label>
                    <select id="sortFilter" class="filter-input">
                        <option value="updated">Más recientes</option>
                        <option value="title">Título A-Z</option>
                        <option value="price-high">Precio mayor</option>
                        <option value="price-low">Precio menor</option>
                        <option value="students">Más estudiantes</option>
                        <option value="rating">Mejor valorados</option>
                    </select>
                </div>
                <div class="filter-group">
                    <button class="btn btn-outline-secondary" onclick="clearFilters()">
                        <i class="bi bi-x-circle"></i> Limpiar
                    </button>
                </div>
            </div>
        </div>

        <!-- Stats Bar -->
        <div class="stats-bar">
            <div class="stats-info">
                <div class="stat-item">
                    <i class="bi bi-collection"></i>
                    <span><span class="stat-number" id="totalCoursesCount">0</span> cursos en total</span>
                </div>
                <div class="stat-item">
                    <i class="bi bi-eye"></i>
                    <span><span class="stat-number" id="filteredCoursesCount">0</span> mostrados</span>
                </div>
                <div class="stat-item">
                    <i class="bi bi-currency-euro"></i>
                    <span><span class="stat-number" id="totalRevenueCount">€0</span> ingresos potenciales</span>
                </div>
            </div>
            <div class="view-toggle">
                <button class="view-btn active" id="gridViewBtn" onclick="switchView('grid')">
                    <i class="bi bi-grid-3x3-gap"></i> Tarjetas
                </button>
                <button class="view-btn" id="listViewBtn" onclick="switchView('list')">
                    <i class="bi bi-list"></i> Lista
                </button>
            </div>
        </div>

        <!-- Courses Container -->
        <div class="courses-container">
            <div id="coursesGrid" class="courses-grid">
                <!-- Loading skeleton -->
                <div class="course-card">
                    <div class="loading-skeleton" style="height: 180px;"></div>
                    <div class="course-content">
                        <div class="loading-skeleton" style="height: 1rem; margin-bottom: 0.5rem;"></div>
                        <div class="loading-skeleton" style="height: 1.5rem; margin-bottom: 0.5rem;"></div>
                        <div class="loading-skeleton" style="height: 3rem; margin-bottom: 1rem;"></div>
                        <div class="loading-skeleton" style="height: 1rem;"></div>
                    </div>
                </div>
            </div>
            
            <div id="coursesList" class="courses-list" style="display: none;">
                <!-- Lista se carga dinámicamente -->
            </div>
        </div>
    </div>

    <!-- Scripts -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.3/dist/js/bootstrap.bundle.min.js"></script>

    <!-- JavaScript Inline -->
    <script>
        // ========================================
        // ASG CONFIGURACIÓN INLINE
        // ========================================
        const ASG_CONFIG = {
            version: '2.0.0',
            name: 'AbilitySeminarsGroup Course Management',
            apiUrl: 'https://abilityseminarsgroup.com/wp-json/asg/v1',
            mockData: true,

            // Endpoints
            endpoints: {
                courses: '/courses',
                course: '/courses/{code}',
                categories: '/courses/categories',
                uploadImage: '/courses/upload-image'
            },

            // Categorías
            categories: {
                finanzas: { label: 'Finanzas', icon: '💰', color: '#10b981' },
                marketing: { label: 'Marketing', icon: '📱', color: '#f59e0b' },
                'desarrollo-personal': { label: 'Desarrollo Personal', icon: '🧠', color: '#8b5cf6' },
                tecnologia: { label: 'Tecnología', icon: '💻', color: '#06b6d4' },
                negocios: { label: 'Negocios', icon: '💼', color: '#ef4444' },
                salud: { label: 'Salud', icon: '🏥', color: '#84cc16' }
            },

            // Estados
            states: {
                draft: { label: 'Borrador', color: '#f59e0b', badge: 'status-draft' },
                published: { label: 'Publicado', color: '#10b981', badge: 'status-published' },
                archived: { label: 'Archivado', color: '#6b7280', badge: 'status-archived' }
            }
        };

        // ========================================
        // DATOS MOCK COMPLETOS
        // ========================================
        const MOCK_COURSES = [
            {
                id: 1,
                code: 'course_1',
                title: 'Como hacerte millonario?',
                description: 'Aprende las estrategias más efectivas para generar riqueza y alcanzar la libertad financiera. Descubre los secretos de los millonarios y aplica técnicas probadas.',
                image: 'https://images.unsplash.com/photo-1579621970563-ebec7560ff3e?w=400&h=300&fit=crop',
                price: 299.99,
                status: 'published',
                category: 'finanzas',
                modules: 8,
                lessons: 24,
                students: 156,
                rating: 4.8,
                created: '2024-12-15',
                updated: '2024-12-20'
            },
            {
                id: 2,
                code: 'course_2',
                title: 'Marketing Digital Avanzado',
                description: 'Domina las técnicas más avanzadas del marketing digital y haz crecer tu negocio online. Aprende SEO, SEM, redes sociales y automatización.',
                image: 'https://images.unsplash.com/photo-1460925895917-afdab827c52f?w=400&h=300&fit=crop',
                price: 199.99,
                status: 'published',
                category: 'marketing',
                modules: 6,
                lessons: 18,
                students: 89,
                rating: 4.6,
                created: '2024-12-10',
                updated: '2024-12-18'
            },
            {
                id: 3,
                code: 'course_3',
                title: 'Desarrollo Personal Integral',
                description: 'Transforma tu vida con técnicas probadas de crecimiento personal y liderazgo. Desarrolla habilidades de comunicación, liderazgo y productividad.',
                image: 'https://images.unsplash.com/photo-1516321318423-f06f85e504b3?w=400&h=300&fit=crop',
                price: 149.99,
                status: 'draft',
                category: 'desarrollo-personal',
                modules: 4,
                lessons: 12,
                students: 0,
                rating: 0,
                created: '2024-12-05',
                updated: '2024-12-22'
            },
            {
                id: 4,
                code: 'course_4',
                title: 'Programación Web Moderna',
                description: 'Aprende las tecnologías más demandadas del desarrollo web actual. HTML5, CSS3, JavaScript, React, Node.js y bases de datos.',
                image: 'https://images.unsplash.com/photo-1461749280684-dccba630e2f6?w=400&h=300&fit=crop',
                price: 249.99,
                status: 'published',
                category: 'tecnologia',
                modules: 10,
                lessons: 35,
                students: 67,
                rating: 4.9,
                created: '2024-11-28',
                updated: '2024-12-19'
            },
            {
                id: 5,
                code: 'course_5',
                title: 'Gestión de Negocios Exitosos',
                description: 'Estrategias comprobadas para crear y hacer crecer tu negocio. Aprende sobre planificación, finanzas, marketing y gestión de equipos.',
                image: 'https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=400&h=300&fit=crop',
                price: 179.99,
                status: 'published',
                category: 'negocios',
                modules: 7,
                lessons: 21,
                students: 134,
                rating: 4.7,
                created: '2024-11-20',
                updated: '2024-12-21'
            },
            {
                id: 6,
                code: 'course_6',
                title: 'Nutrición y Bienestar',
                description: 'Guía completa para una vida saludable y equilibrada. Aprende sobre nutrición, ejercicio, mindfulness y hábitos saludables.',
                image: 'https://images.unsplash.com/photo-1490645935967-10de6ba17061?w=400&h=300&fit=crop',
                price: 129.99,
                status: 'draft',
                category: 'salud',
                modules: 5,
                lessons: 15,
                students: 0,
                rating: 0,
                created: '2024-12-01',
                updated: '2024-12-23'
            },
            {
                id: 7,
                code: 'course_7',
                title: 'Fotografía Profesional',
                description: 'Domina el arte de la fotografía desde lo básico hasta técnicas avanzadas. Composición, iluminación, edición y fotografía comercial.',
                image: 'https://images.unsplash.com/photo-1502920917128-1aa500764cbd?w=400&h=300&fit=crop',
                price: 189.99,
                status: 'published',
                category: 'arte',
                modules: 6,
                lessons: 20,
                students: 45,
                rating: 4.5,
                created: '2024-11-15',
                updated: '2024-12-10'
            },
            {
                id: 8,
                code: 'course_8',
                title: 'Inversiones y Trading',
                description: 'Aprende a invertir en bolsa, criptomonedas y otros instrumentos financieros. Análisis técnico, fundamental y gestión de riesgos.',
                image: 'https://images.unsplash.com/photo-1611974789855-9c2a0a7236a3?w=400&h=300&fit=crop',
                price: 349.99,
                status: 'published',
                category: 'finanzas',
                modules: 12,
                lessons: 40,
                students: 203,
                rating: 4.9,
                created: '2024-10-20',
                updated: '2024-12-15'
            },
            {
                id: 9,
                code: 'course_9',
                title: 'Diseño UX/UI Completo',
                description: 'Conviértete en diseñador UX/UI profesional. Aprende investigación de usuarios, wireframing, prototipado y herramientas como Figma.',
                image: 'https://images.unsplash.com/photo-1561070791-2526d30994b5?w=400&h=300&fit=crop',
                price: 229.99,
                status: 'archived',
                category: 'tecnologia',
                modules: 8,
                lessons: 28,
                students: 78,
                rating: 4.4,
                created: '2024-09-10',
                updated: '2024-11-30'
            },
            {
                id: 10,
                code: 'course_10',
                title: 'Yoga y Meditación',
                description: 'Encuentra el equilibrio perfecto entre cuerpo y mente. Aprende diferentes estilos de yoga, técnicas de meditación y filosofía oriental.',
                image: 'https://images.unsplash.com/photo-1544367567-0f2fcb009e0b?w=400&h=300&fit=crop',
                price: 99.99,
                status: 'draft',
                category: 'salud',
                modules: 4,
                lessons: 16,
                students: 0,
                rating: 0,
                created: '2024-12-20',
                updated: '2024-12-23'
            }
        ];

        // ========================================
        // VARIABLES GLOBALES
        // ========================================
        let allCourses = [];
        let filteredCourses = [];
        let currentView = 'grid';
        let currentFilters = {
            search: '',
            status: '',
            category: '',
            sort: 'updated'
        };

        // ========================================
        // UTILIDADES
        // ========================================
        const ASG_UTILS = {
            formatCurrency: (amount) => {
                return new Intl.NumberFormat('es-ES', {
                    style: 'currency',
                    currency: 'EUR'
                }).format(amount || 0);
            },

            formatDate: (date) => {
                return new Intl.DateTimeFormat('es-ES', {
                    year: 'numeric',
                    month: 'short',
                    day: 'numeric'
                }).format(new Date(date));
            },

            getCategoryInfo: (categoryCode) => {
                return ASG_CONFIG.categories[categoryCode] || {
                    label: categoryCode,
                    icon: '📚',
                    color: '#6b7280'
                };
            },

            getStatusInfo: (statusCode) => {
                return ASG_CONFIG.states[statusCode] || {
                    label: statusCode,
                    color: '#6b7280',
                    badge: 'status-draft'
                };
            },

            truncateText: (text, maxLength = 100) => {
                if (!text || text.length <= maxLength) return text;
                return text.substring(0, maxLength) + '...';
            },

            debounce: (func, wait) => {
                let timeout;
                return function executedFunction(...args) {
                    const later = () => {
                        clearTimeout(timeout);
                        func(...args);
                    };
                    clearTimeout(timeout);
                    timeout = setTimeout(later, wait);
                };
            }
        };

        // ========================================
        // FUNCIONES DE INTERFAZ
        // ========================================
        function showNotification(message, type = 'info') {
            const toast = document.createElement('div');
            const colors = {
                success: '#10b981',
                error: '#ef4444',
                warning: '#f59e0b',
                info: '#06b6d4'
            };

            const icons = {
                success: '✅',
                error: '❌',
                warning: '⚠️',
                info: 'ℹ️'
            };

            toast.style.cssText = `
                position: fixed;
                top: 90px;
                right: 20px;
                background: ${colors[type] || colors.info};
                color: white;
                padding: 12px 16px;
                border-radius: 8px;
                box-shadow: 0 4px 12px rgba(0,0,0,0.15);
                z-index: 9999;
                animation: slideIn 0.3s ease-out;
                cursor: pointer;
                max-width: 400px;
            `;

            toast.innerHTML = `
                <div style="display: flex; align-items: center; gap: 8px;">
                    <span>${icons[type] || icons.info}</span>
                    <span>${message}</span>
                    <span style="opacity: 0.7; margin-left: auto;">×</span>
                </div>
            `;

            toast.addEventListener('click', () => {
                toast.style.animation = 'slideOut 0.3s ease-in';
                setTimeout(() => document.body.removeChild(toast), 300);
            });

            document.body.appendChild(toast);

            setTimeout(() => {
                if (toast.parentNode) {
                    toast.style.animation = 'slideOut 0.3s ease-in';
                    setTimeout(() => {
                        if (toast.parentNode) {
                            document.body.removeChild(toast);
                        }
                    }, 300);
                }
            }, 5000);
        }

        // ========================================
        // FUNCIONES DE DATOS
        // ========================================
        async function loadCourses() {
            try {
                // Simular carga de API
                await new Promise(resolve => setTimeout(resolve, 1000));

                // Usar datos mock
                allCourses = [...MOCK_COURSES];
                filteredCourses = [...allCourses];

                applyFilters();
                updateStats();
                renderCourses();

                console.log('✅ Courses loaded:', allCourses.length);

            } catch (error) {
                console.error('❌ Error loading courses:', error);
                showNotification('❌ Error al cargar los cursos', 'error');
            }
        }

        function applyFilters() {
            filteredCourses = allCourses.filter(course => {
                // Filtro de búsqueda
                if (currentFilters.search) {
                    const searchTerm = currentFilters.search.toLowerCase();
                    const matchesSearch =
                        course.title.toLowerCase().includes(searchTerm) ||
                        course.description.toLowerCase().includes(searchTerm) ||
                        ASG_UTILS.getCategoryInfo(course.category).label.toLowerCase().includes(searchTerm);

                    if (!matchesSearch) return false;
                }

                // Filtro de estado
                if (currentFilters.status && course.status !== currentFilters.status) {
                    return false;
                }

                // Filtro de categoría
                if (currentFilters.category && course.category !== currentFilters.category) {
                    return false;
                }

                return true;
            });

            // Aplicar ordenamiento
            applySorting();
        }

        function applySorting() {
            filteredCourses.sort((a, b) => {
                switch (currentFilters.sort) {
                    case 'title':
                        return a.title.localeCompare(b.title);
                    case 'price-high':
                        return b.price - a.price;
                    case 'price-low':
                        return a.price - b.price;
                    case 'students':
                        return b.students - a.students;
                    case 'rating':
                        return b.rating - a.rating;
                    case 'updated':
                    default:
                        return new Date(b.updated) - new Date(a.updated);
                }
            });
        }

        function updateStats() {
            document.getElementById('totalCoursesCount').textContent = allCourses.length;
            document.getElementById('filteredCoursesCount').textContent = filteredCourses.length;

            const totalRevenue = filteredCourses.reduce((sum, course) => sum + course.price, 0);
            document.getElementById('totalRevenueCount').textContent = ASG_UTILS.formatCurrency(totalRevenue);
        }

        // ========================================
        // FUNCIONES DE RENDERIZADO
        // ========================================
        function renderCourses() {
            if (currentView === 'grid') {
                renderGridView();
            } else {
                renderListView();
            }
        }

        function renderGridView() {
            const container = document.getElementById('coursesGrid');

            if (filteredCourses.length === 0) {
                container.innerHTML = `
                    <div class="empty-state" style="grid-column: 1 / -1;">
                        <i class="bi bi-search"></i>
                        <h3>No se encontraron cursos</h3>
                        <p>Intenta ajustar los filtros o crear un nuevo curso</p>
                        <button class="btn btn-primary" onclick="clearFilters()">
                            <i class="bi bi-x-circle"></i> Limpiar filtros
                        </button>
                    </div>
                `;
                return;
            }

            container.innerHTML = filteredCourses.map(course => {
                const category = ASG_UTILS.getCategoryInfo(course.category);
                const status = ASG_UTILS.getStatusInfo(course.status);

                return `
                    <div class="course-card" onclick="viewCourse('${course.code}')">
                        <div class="course-image" style="background-image: url('${course.image}')">
                            <div class="course-status-badge ${status.badge}">
                                ${status.label}
                            </div>
                        </div>
                        <div class="course-content">
                            <div class="course-category">
                                ${category.icon} ${category.label}
                            </div>
                            <h3 class="course-title">${course.title}</h3>
                            <p class="course-description">${ASG_UTILS.truncateText(course.description, 120)}</p>
                            <div class="course-meta">
                                <div class="course-stats">
                                    <span><i class="bi bi-collection"></i> ${course.modules} módulos</span>
                                    <span><i class="bi bi-play-circle"></i> ${course.lessons} lecciones</span>
                                    ${course.students > 0 ? `<span><i class="bi bi-people"></i> ${course.students}</span>` : ''}
                                </div>
                                <div class="course-price">${ASG_UTILS.formatCurrency(course.price)}</div>
                            </div>
                            <div class="course-actions">
                                <button class="btn-action primary" onclick="event.stopPropagation(); editCourse('${course.code}')">
                                    <i class="bi bi-pencil"></i> Editar
                                </button>
                                <button class="btn-action" onclick="event.stopPropagation(); viewCourse('${course.code}')">
                                    <i class="bi bi-eye"></i> Ver
                                </button>
                                <button class="btn-action" onclick="event.stopPropagation(); duplicateCourse('${course.code}')">
                                    <i class="bi bi-files"></i> Duplicar
                                </button>
                            </div>
                        </div>
                    </div>
                `;
            }).join('');
        }

        function renderListView() {
            const container = document.getElementById('coursesList');

            if (filteredCourses.length === 0) {
                container.innerHTML = `
                    <div class="empty-state">
                        <i class="bi bi-search"></i>
                        <h3>No se encontraron cursos</h3>
                        <p>Intenta ajustar los filtros o crear un nuevo curso</p>
                        <button class="btn btn-primary" onclick="clearFilters()">
                            <i class="bi bi-x-circle"></i> Limpiar filtros
                        </button>
                    </div>
                `;
                return;
            }

            container.innerHTML = filteredCourses.map(course => {
                const category = ASG_UTILS.getCategoryInfo(course.category);
                const status = ASG_UTILS.getStatusInfo(course.status);

                return `
                    <div class="course-list-item" onclick="viewCourse('${course.code}')">
                        <div class="course-list-image" style="background-image: url('${course.image}')"></div>
                        <div class="course-list-content">
                            <div class="course-list-title">${course.title}</div>
                            <div class="course-list-meta">
                                ${category.icon} ${category.label} •
                                ${course.modules} módulos •
                                ${course.lessons} lecciones •
                                <span class="${status.badge}">${status.label}</span>
                                ${course.students > 0 ? ` • ${course.students} estudiantes` : ''}
                            </div>
                        </div>
                        <div class="course-list-price">${ASG_UTILS.formatCurrency(course.price)}</div>
                        <div class="course-list-actions">
                            <button class="btn-action primary" onclick="event.stopPropagation(); editCourse('${course.code}')">
                                <i class="bi bi-pencil"></i>
                            </button>
                            <button class="btn-action" onclick="event.stopPropagation(); viewCourse('${course.code}')">
                                <i class="bi bi-eye"></i>
                            </button>
                            <button class="btn-action" onclick="event.stopPropagation(); duplicateCourse('${course.code}')">
                                <i class="bi bi-files"></i>
                            </button>
                        </div>
                    </div>
                `;
            }).join('');
        }

        // ========================================
        // FUNCIONES DE CONTROL
        // ========================================
        function switchView(view) {
            currentView = view;

            // Actualizar botones
            document.getElementById('gridViewBtn').classList.toggle('active', view === 'grid');
            document.getElementById('listViewBtn').classList.toggle('active', view === 'list');

            // Mostrar/ocultar contenedores
            document.getElementById('coursesGrid').style.display = view === 'grid' ? 'grid' : 'none';
            document.getElementById('coursesList').style.display = view === 'list' ? 'flex' : 'none';

            // Re-renderizar
            renderCourses();

            showNotification(`📋 Vista cambiada a ${view === 'grid' ? 'tarjetas' : 'lista'}`, 'info');
        }

        function clearFilters() {
            currentFilters = {
                search: '',
                status: '',
                category: '',
                sort: 'updated'
            };

            // Limpiar inputs
            document.getElementById('searchInput').value = '';
            document.getElementById('statusFilter').value = '';
            document.getElementById('categoryFilter').value = '';
            document.getElementById('sortFilter').value = 'updated';

            // Aplicar filtros
            applyFilters();
            updateStats();
            renderCourses();

            showNotification('🧹 Filtros limpiados', 'info');
        }

        async function refreshCourses() {
            const btn = event.target;
            const originalText = btn.innerHTML;

            btn.innerHTML = '<i class="bi bi-arrow-clockwise spin"></i> Actualizando...';
            btn.disabled = true;

            try {
                await loadCourses();
                showNotification('✅ Cursos actualizados correctamente', 'success');
            } catch (error) {
                showNotification('❌ Error al actualizar los cursos', 'error');
            } finally {
                btn.innerHTML = originalText;
                btn.disabled = false;
            }
        }

        function exportCourses() {
            try {
                const dataToExport = filteredCourses.map(course => ({
                    titulo: course.title,
                    descripcion: course.description,
                    categoria: ASG_UTILS.getCategoryInfo(course.category).label,
                    estado: ASG_UTILS.getStatusInfo(course.status).label,
                    precio: course.price,
                    modulos: course.modules,
                    lecciones: course.lessons,
                    estudiantes: course.students,
                    valoracion: course.rating,
                    creado: ASG_UTILS.formatDate(course.created),
                    actualizado: ASG_UTILS.formatDate(course.updated)
                }));

                const csv = convertToCSV(dataToExport);
                downloadCSV(csv, 'cursos-asg.csv');

                showNotification(`📥 ${filteredCourses.length} cursos exportados`, 'success');
            } catch (error) {
                showNotification('❌ Error al exportar los cursos', 'error');
            }
        }

        function convertToCSV(data) {
            if (data.length === 0) return '';

            const headers = Object.keys(data[0]);
            const csvHeaders = headers.join(',');

            const csvRows = data.map(row =>
                headers.map(header => {
                    const value = row[header];
                    return typeof value === 'string' && value.includes(',') ? `"${value}"` : value;
                }).join(',')
            );

            return [csvHeaders, ...csvRows].join('\n');
        }

        function downloadCSV(csv, filename) {
            const blob = new Blob([csv], { type: 'text/csv;charset=utf-8;' });
            const link = document.createElement('a');

            if (link.download !== undefined) {
                const url = URL.createObjectURL(blob);
                link.setAttribute('href', url);
                link.setAttribute('download', filename);
                link.style.visibility = 'hidden';
                document.body.appendChild(link);
                link.click();
                document.body.removeChild(link);
            }
        }

        // ========================================
        // FUNCIONES DE NAVEGACIÓN
        // ========================================
        function viewCourse(courseCode) {
            showNotification(`👁️ Viendo curso: ${courseCode}`, 'info');
            // Aquí se abriría la vista del curso
            console.log('View course:', courseCode);
        }

        function editCourse(courseCode) {
            showNotification(`✏️ Editando curso: ${courseCode}`, 'info');
            // Aquí se navegaría al editor
            window.open(`edit-course-inline.html?course=${courseCode}`, '_blank');
        }

        function duplicateCourse(courseCode) {
            const course = allCourses.find(c => c.code === courseCode);
            if (course) {
                showNotification(`📋 Duplicando curso: ${course.title}`, 'info');
                // Aquí se duplicaría el curso
                console.log('Duplicate course:', course);
            }
        }

        function navigateToNewCourse() {
            window.open('new-course-inline.html', '_blank');
        }

        // ========================================
        // EVENT LISTENERS
        // ========================================
        function setupEventListeners() {
            // Búsqueda con debounce
            const searchInput = document.getElementById('searchInput');
            const debouncedSearch = ASG_UTILS.debounce((value) => {
                currentFilters.search = value;
                applyFilters();
                updateStats();
                renderCourses();
            }, 300);

            searchInput.addEventListener('input', (e) => {
                debouncedSearch(e.target.value);
            });

            // Filtros
            document.getElementById('statusFilter').addEventListener('change', (e) => {
                currentFilters.status = e.target.value;
                applyFilters();
                updateStats();
                renderCourses();
            });

            document.getElementById('categoryFilter').addEventListener('change', (e) => {
                currentFilters.category = e.target.value;
                applyFilters();
                updateStats();
                renderCourses();
            });

            document.getElementById('sortFilter').addEventListener('change', (e) => {
                currentFilters.sort = e.target.value;
                applyFilters();
                updateStats();
                renderCourses();
            });

            // Atajos de teclado
            document.addEventListener('keydown', (e) => {
                if (e.ctrlKey || e.metaKey) {
                    switch(e.key) {
                        case 'f':
                            e.preventDefault();
                            document.getElementById('searchInput').focus();
                            break;
                        case 'n':
                            e.preventDefault();
                            navigateToNewCourse();
                            break;
                        case 'r':
                            e.preventDefault();
                            refreshCourses();
                            break;
                        case 'e':
                            e.preventDefault();
                            exportCourses();
                            break;
                    }
                }
            });
        }

        // ========================================
        // INICIALIZACIÓN
        // ========================================
        document.addEventListener('DOMContentLoaded', function() {
            console.log('🚀 All Courses ASG Inline v2.0.0 loaded');

            // Configurar event listeners
            setupEventListeners();

            // Mostrar notificación de bienvenida
            setTimeout(() => {
                showNotification('📚 Sistema de gestión de cursos cargado', 'success');
            }, 500);

            // Cargar cursos
            setTimeout(() => {
                loadCourses();
            }, 1000);
        });

        // ========================================
        // FUNCIONES GLOBALES
        // ========================================
        window.ASG_COURSES = {
            refresh: refreshCourses,
            loadData: loadCourses,
            showNotification: showNotification,
            switchView: switchView,
            clearFilters: clearFilters,
            exportCourses: exportCourses,
            config: ASG_CONFIG,
            utils: ASG_UTILS
        };

        // Log de información del sistema
        console.log('📚 ASG All Courses System Info:', {
            version: ASG_CONFIG.version,
            totalCourses: MOCK_COURSES.length,
            categories: Object.keys(ASG_CONFIG.categories).length,
            timestamp: new Date().toISOString()
        });
    </script>
