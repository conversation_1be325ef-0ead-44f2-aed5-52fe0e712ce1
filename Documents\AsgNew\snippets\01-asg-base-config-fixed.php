<?php
/**
 * ========================================
 * ASG BASE CONFIGURATION & API - SNIPPET 1 (CORREGIDO)
 * ========================================
 * 
 * Descripción: Configuración base del sistema ASG y API REST
 * Uso: Copiar y pegar en WP Code Snippets
 * Tipo: PHP Snippet
 * Ubicación: Ejecutar en todas partes
 * 
 * VERSIÓN: 2.0.1 - PERMISOS CORREGIDOS
 * 
 * Autor: ASG Team
 * Fecha: 2025-01-04
 */

// Prevenir ejecución directa
if (!defined('ABSPATH')) {
    exit;
}

/**
 * ========================================
 * CONFIGURACIÓN GLOBAL ASG
 * ========================================
 */
if (!defined('ASG_VERSION')) {
    define('ASG_VERSION', '2.0.1');
    define('ASG_API_NAMESPACE', 'asg/v2');
    define('ASG_DB_VERSION', '1.0');
}

/**
 * ========================================
 * REGISTRO DE API REST
 * ========================================
 */
add_action('rest_api_init', function () {
    
    // ===== ENDPOINTS PÚBLICOS =====
    
    // Obtener curso completo para frontend público
    register_rest_route(ASG_API_NAMESPACE, '/public/courses/(?P<code>[a-zA-Z0-9_-]+)', array(
        'methods' => 'GET',
        'callback' => 'asg_get_public_course',
        'permission_callback' => '__return_true',
        'args' => array(
            'code' => array(
                'required' => true,
                'validate_callback' => function($param) {
                    return preg_match('/^course_\d+$/', $param);
                }
            )
        )
    ));
    
    // Lista de cursos públicos
    register_rest_route(ASG_API_NAMESPACE, '/public/courses', array(
        'methods' => 'GET',
        'callback' => 'asg_get_public_courses',
        'permission_callback' => '__return_true'
    ));
    
    // ===== ENDPOINTS ADMINISTRATIVOS =====
    
    // CRUD completo de cursos
    register_rest_route(ASG_API_NAMESPACE, '/admin/courses', array(
        'methods' => 'GET',
        'callback' => 'asg_admin_get_courses',
        'permission_callback' => 'asg_check_admin_permissions'
    ));
    
    register_rest_route(ASG_API_NAMESPACE, '/admin/courses', array(
        'methods' => 'POST',
        'callback' => 'asg_admin_create_course',
        'permission_callback' => 'asg_check_admin_permissions'
    ));
    
    register_rest_route(ASG_API_NAMESPACE, '/admin/courses/(?P<code>[a-zA-Z0-9_-]+)', array(
        'methods' => array('GET', 'PUT', 'DELETE'),
        'callback' => 'asg_admin_handle_course',
        'permission_callback' => 'asg_check_admin_permissions'
    ));
    
    // Estadísticas del dashboard
    register_rest_route(ASG_API_NAMESPACE, '/admin/dashboard/stats', array(
        'methods' => 'GET',
        'callback' => 'asg_admin_get_dashboard_stats',
        'permission_callback' => 'asg_check_admin_permissions'
    ));
    
    // ===== ENDPOINT DE DEBUG (TEMPORAL) =====
    register_rest_route(ASG_API_NAMESPACE, '/debug/permissions', array(
        'methods' => 'GET',
        'callback' => 'asg_debug_permissions',
        'permission_callback' => '__return_true'
    ));
});

/**
 * ========================================
 * FUNCIONES DE PERMISOS (CORREGIDAS)
 * ========================================
 */

/**
 * Verificar permisos de administrador (VERSIÓN CORREGIDA)
 */
function asg_check_admin_permissions($request = null) {
    // Debug: Log información de permisos
    error_log('ASG Debug - Checking permissions...');
    error_log('User ID: ' . get_current_user_id());
    error_log('Is user logged in: ' . (is_user_logged_in() ? 'Yes' : 'No'));
    error_log('Can manage options: ' . (current_user_can('manage_options') ? 'Yes' : 'No'));
    error_log('Can edit posts: ' . (current_user_can('edit_posts') ? 'Yes' : 'No'));
    
    // Si no hay usuario logueado
    if (!is_user_logged_in()) {
        error_log('ASG Debug - User not logged in');
        return new WP_Error('not_logged_in', 'Debes estar logueado para acceder a esta función.', array('status' => 401));
    }
    
    // Verificar permisos específicos
    if (current_user_can('manage_options') || 
        current_user_can('edit_posts') || 
        current_user_can('administrator') ||
        current_user_can('editor')) {
        error_log('ASG Debug - Permission granted');
        return true;
    }
    
    error_log('ASG Debug - Permission denied');
    return new WP_Error('insufficient_permissions', 'No tienes permisos suficientes para realizar esta acción.', array('status' => 403));
}

/**
 * Endpoint de debug para verificar permisos
 */
function asg_debug_permissions($request) {
    $user = wp_get_current_user();
    
    return new WP_REST_Response(array(
        'success' => true,
        'debug_info' => array(
            'user_id' => get_current_user_id(),
            'user_login' => $user->user_login ?? 'No user',
            'user_roles' => $user->roles ?? array(),
            'is_logged_in' => is_user_logged_in(),
            'capabilities' => array(
                'manage_options' => current_user_can('manage_options'),
                'edit_posts' => current_user_can('edit_posts'),
                'administrator' => current_user_can('administrator'),
                'editor' => current_user_can('editor')
            ),
            'nonce_valid' => wp_verify_nonce($request->get_header('X-WP-Nonce'), 'wp_rest'),
            'request_headers' => array(
                'X-WP-Nonce' => $request->get_header('X-WP-Nonce'),
                'Authorization' => $request->get_header('Authorization')
            )
        )
    ), 200);
}

/**
 * ========================================
 * FUNCIONES DE API (SIN CAMBIOS)
 * ========================================
 */

/**
 * Obtener estadísticas del dashboard
 */
function asg_admin_get_dashboard_stats($request) {
    global $wpdb;
    
    try {
        // Verificar si las tablas existen
        $table_exists = $wpdb->get_var("SHOW TABLES LIKE '{$wpdb->prefix}courses'");
        
        if (!$table_exists) {
            // Devolver datos simulados si no hay tablas
            return new WP_REST_Response(array(
                'success' => true,
                'data' => array(
                    'totalCourses' => 6,
                    'publishedCourses' => 4,
                    'draftCourses' => 2,
                    'totalRevenue' => 1299.96,
                    'avgPrice' => 216.66,
                    'coursesChange' => 25
                ),
                'note' => 'Datos simulados - Tablas de base de datos no encontradas'
            ), 200);
        }
        
        // Estadísticas reales de la base de datos
        $total_courses = $wpdb->get_var("SELECT COUNT(*) FROM {$wpdb->prefix}courses WHERE is_deleted = 0");
        $published_courses = $wpdb->get_var("SELECT COUNT(*) FROM {$wpdb->prefix}courses WHERE status_course = 'published' AND is_deleted = 0");
        $draft_courses = $wpdb->get_var("SELECT COUNT(*) FROM {$wpdb->prefix}courses WHERE status_course = 'draft' AND is_deleted = 0");
        
        $total_revenue = $wpdb->get_var("SELECT SUM(price_course) FROM {$wpdb->prefix}courses WHERE status_course = 'published' AND is_deleted = 0");
        $avg_price = $wpdb->get_var("SELECT AVG(price_course) FROM {$wpdb->prefix}courses WHERE status_course = 'published' AND is_deleted = 0");
        
        return new WP_REST_Response(array(
            'success' => true,
            'data' => array(
                'totalCourses' => intval($total_courses),
                'publishedCourses' => intval($published_courses),
                'draftCourses' => intval($draft_courses),
                'totalRevenue' => floatval($total_revenue),
                'avgPrice' => floatval($avg_price),
                'coursesChange' => 15 // Simulado - calcular vs mes anterior
            )
        ), 200);
        
    } catch (Exception $e) {
        error_log('ASG Error in dashboard stats: ' . $e->getMessage());
        return new WP_Error('server_error', 'Error interno del servidor', array('status' => 500));
    }
}

/**
 * Obtener cursos para administración
 */
function asg_admin_get_courses($request) {
    global $wpdb;
    
    try {
        // Verificar si las tablas existen
        $table_exists = $wpdb->get_var("SHOW TABLES LIKE '{$wpdb->prefix}courses'");
        
        if (!$table_exists) {
            // Devolver datos simulados
            return new WP_REST_Response(array(
                'success' => true,
                'data' => array(
                    array(
                        'id_course' => 1,
                        'code_course' => 'course_1',
                        'name_course' => 'Como hacerte millonario?',
                        'description_course' => 'Aprende las estrategias más efectivas para generar riqueza.',
                        'price_course' => 299.99,
                        'status_course' => 'published',
                        'category_course' => 'finanzas',
                        'module_count' => 8,
                        'lesson_count' => 24,
                        'created_at' => '2024-12-15 10:30:00'
                    ),
                    array(
                        'id_course' => 2,
                        'code_course' => 'course_2',
                        'name_course' => 'Marketing Digital Avanzado',
                        'description_course' => 'Domina las técnicas más avanzadas del marketing digital.',
                        'price_course' => 199.99,
                        'status_course' => 'published',
                        'category_course' => 'marketing',
                        'module_count' => 6,
                        'lesson_count' => 18,
                        'created_at' => '2024-12-10 14:20:00'
                    ),
                    array(
                        'id_course' => 3,
                        'code_course' => 'course_3',
                        'name_course' => 'Desarrollo Personal Integral',
                        'description_course' => 'Transforma tu vida con técnicas probadas.',
                        'price_course' => 149.99,
                        'status_course' => 'draft',
                        'category_course' => 'desarrollo-personal',
                        'module_count' => 4,
                        'lesson_count' => 12,
                        'created_at' => '2024-12-05 09:15:00'
                    )
                ),
                'pagination' => array(
                    'page' => 1,
                    'per_page' => 20,
                    'total' => 3,
                    'total_pages' => 1
                ),
                'note' => 'Datos simulados - Tablas de base de datos no encontradas'
            ), 200);
        }
        
        // Consulta real a la base de datos
        $page = intval($request->get_param('page')) ?: 1;
        $per_page = intval($request->get_param('per_page')) ?: 20;
        $status = sanitize_text_field($request->get_param('status'));
        
        $offset = ($page - 1) * $per_page;
        $where_conditions = array("is_deleted = 0");
        $where_params = array();
        
        if ($status) {
            $where_conditions[] = "status_course = %s";
            $where_params[] = $status;
        }
        
        $where_clause = implode(' AND ', $where_conditions);
        
        $total_query = "SELECT COUNT(*) FROM {$wpdb->prefix}courses WHERE {$where_clause}";
        $total = $wpdb->get_var($wpdb->prepare($total_query, $where_params));
        
        $courses_query = "
            SELECT c.*, 
                   COUNT(DISTINCT m.id_modules) as module_count,
                   COUNT(DISTINCT l.id_lesson) as lesson_count
            FROM {$wpdb->prefix}courses c
            LEFT JOIN {$wpdb->prefix}modules m ON c.code_course = m.code_course AND m.is_deleted = 0
            LEFT JOIN {$wpdb->prefix}lessons l ON m.code_module = l.code_module AND l.is_deleted = 0
            WHERE {$where_clause}
            GROUP BY c.id_course
            ORDER BY c.updated_at DESC
            LIMIT %d OFFSET %d
        ";
        
        $query_params = array_merge($where_params, array($per_page, $offset));
        $courses = $wpdb->get_results($wpdb->prepare($courses_query, $query_params));
        
        return new WP_REST_Response(array(
            'success' => true,
            'data' => $courses,
            'pagination' => array(
                'page' => $page,
                'per_page' => $per_page,
                'total' => intval($total),
                'total_pages' => ceil($total / $per_page)
            )
        ), 200);
        
    } catch (Exception $e) {
        error_log('ASG Error in get courses: ' . $e->getMessage());
        return new WP_Error('server_error', 'Error interno del servidor', array('status' => 500));
    }
}

/**
 * ========================================
 * CONFIGURACIÓN JAVASCRIPT GLOBAL
 * ========================================
 */
add_action('wp_head', function() {
    ?>
    <script>
    window.ASG_CONFIG = {
        apiUrl: '<?php echo esc_url(rest_url(ASG_API_NAMESPACE)); ?>',
        nonce: '<?php echo wp_create_nonce('wp_rest'); ?>',
        logoUrl: '<?php echo esc_url(get_site_url()); ?>/wp-content/uploads/2023/09/Asset-6-4-768x355.png',
        version: '<?php echo ASG_VERSION; ?>',
        userId: <?php echo get_current_user_id(); ?>,
        isLoggedIn: <?php echo is_user_logged_in() ? 'true' : 'false'; ?>,
        userCan: {
            manageOptions: <?php echo current_user_can('manage_options') ? 'true' : 'false'; ?>,
            editPosts: <?php echo current_user_can('edit_posts') ? 'true' : 'false'; ?>
        },
        breakpoints: {
            mobile: 640,
            tablet: 768,
            desktop: 1024
        }
    };
    
    // Debug en consola
    console.log('ASG Config loaded:', window.ASG_CONFIG);
    </script>
    <?php
});

/**
 * ========================================
 * ESTILOS CSS GLOBALES
 * ========================================
 */
add_action('wp_head', function() {
    ?>
    <style>
    :root {
        --asg-primary: #2563eb;
        --asg-primary-dark: #1d4ed8;
        --asg-success: #10b981;
        --asg-warning: #f59e0b;
        --asg-error: #ef4444;
        --asg-gray-50: #f8fafc;
        --asg-gray-100: #f1f5f9;
        --asg-gray-200: #e2e8f0;
        --asg-gray-600: #475569;
        --asg-gray-900: #0f172a;
        --asg-space-2: 0.5rem;
        --asg-space-4: 1rem;
        --asg-space-6: 1.5rem;
        --asg-radius: 0.375rem;
        --asg-shadow: 0 1px 3px 0 rgb(0 0 0 / 0.1);
        --asg-transition: 200ms ease-in-out;
    }
    
    .asg-btn {
        display: inline-flex;
        align-items: center;
        gap: var(--asg-space-2);
        padding: var(--asg-space-2) var(--asg-space-4);
        border: 1px solid transparent;
        border-radius: var(--asg-radius);
        font-weight: 500;
        text-decoration: none;
        cursor: pointer;
        transition: all var(--asg-transition);
    }
    
    .asg-btn-primary {
        background: var(--asg-primary);
        color: white;
        border-color: var(--asg-primary);
    }
    
    .asg-btn-primary:hover {
        background: var(--asg-primary-dark);
        color: white;
    }
    
    .asg-btn-secondary {
        background: white;
        color: var(--asg-gray-700);
        border: 1px solid var(--asg-gray-300);
    }
    
    .asg-btn-secondary:hover {
        background: var(--asg-gray-50);
    }
    
    .asg-card {
        background: white;
        border: 1px solid var(--asg-gray-200);
        border-radius: var(--asg-radius);
        box-shadow: var(--asg-shadow);
    }
    
    .asg-badge {
        display: inline-flex;
        padding: 0.25rem 0.75rem;
        font-size: 0.75rem;
        font-weight: 500;
        border-radius: 9999px;
        text-transform: uppercase;
    }
    
    .asg-badge-success {
        background: #d1fae5;
        color: #065f46;
    }
    
    .asg-badge-warning {
        background: #fef3c7;
        color: #92400e;
    }
    
    .asg-badge-gray {
        background: var(--asg-gray-100);
        color: var(--asg-gray-600);
    }
    </style>
    <?php
});

/**
 * ========================================
 * ACTIVAR LOGS DE DEBUG
 * ========================================
 */
if (!defined('WP_DEBUG_LOG')) {
    define('WP_DEBUG_LOG', true);
}
?>
