<!DOCTYPE html>
<html lang="es">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Editar Curso - ASG</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.11.3/font/bootstrap-icons.css">
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background-color: #f8f9fa;
            overflow-x: hidden;
        }

        /* Navbar Styles */
        .navbar {
            background: linear-gradient(135deg, #1e3a5f 0%, #2563eb 100%);
            height: 70px;
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            z-index: 1000;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }

        .navbar-brand img {
            width: 130px;
            height: auto;
        }

        .navbar-nav .nav-link {
            color: white !important;
            font-weight: 500;
            padding: 0.5rem 1rem;
            border-radius: 6px;
            transition: all 0.3s ease;
        }

        .navbar-nav .nav-link:hover {
            background-color: rgba(255,255,255,0.1);
            transform: translateY(-1px);
        }

        /* Main Content */
        .main-content {
            margin-top: 70px;
            padding: 2rem;
            min-height: calc(100vh - 70px);
        }

        /* Page Header */
        .page-header {
            background: white;
            border-radius: 12px;
            padding: 2rem;
            margin-bottom: 2rem;
            box-shadow: 0 2px 10px rgba(0,0,0,0.05);
            border: 1px solid #e9ecef;
        }

        .page-title {
            font-size: 2rem;
            font-weight: 700;
            color: #1e3a5f;
            margin-bottom: 0.5rem;
        }

        .page-subtitle {
            color: #6c757d;
            font-size: 1.1rem;
        }

        .course-info {
            display: flex;
            align-items: center;
            gap: 1rem;
            margin-top: 1rem;
            padding: 1rem;
            background: #f8f9fa;
            border-radius: 8px;
        }

        .course-image-small {
            width: 60px;
            height: 45px;
            border-radius: 6px;
            background-size: cover;
            background-position: center;
        }

        .course-details h4 {
            margin: 0;
            color: #1e3a5f;
            font-size: 1.1rem;
        }

        .course-meta {
            font-size: 0.85rem;
            color: #6b7280;
            margin-top: 0.25rem;
        }

        /* Tabs */
        .nav-tabs {
            border-bottom: 2px solid #e9ecef;
            margin-bottom: 2rem;
        }

        .nav-tabs .nav-link {
            border: none;
            color: #6b7280;
            font-weight: 500;
            padding: 1rem 1.5rem;
            border-radius: 0;
            position: relative;
        }

        .nav-tabs .nav-link.active {
            color: #2563eb;
            background: none;
            border-bottom: 2px solid #2563eb;
        }

        .nav-tabs .nav-link:hover {
            color: #2563eb;
            background: #f8f9fa;
        }

        /* Tab Content */
        .tab-content {
            background: white;
            border-radius: 12px;
            padding: 2rem;
            box-shadow: 0 2px 10px rgba(0,0,0,0.05);
            border: 1px solid #e9ecef;
        }

        /* Form Styles */
        .form-group {
            margin-bottom: 1.5rem;
        }

        .form-group label {
            font-weight: 500;
            color: #374151;
            margin-bottom: 0.5rem;
            display: block;
        }

        .form-control {
            width: 100%;
            padding: 0.75rem;
            border: 1px solid #d1d5db;
            border-radius: 6px;
            font-size: 0.9rem;
            transition: all 0.3s ease;
            background: white;
        }

        .form-control:focus {
            outline: none;
            border-color: #2563eb;
            box-shadow: 0 0 0 3px rgba(37, 99, 235, 0.1);
        }

        .form-text {
            font-size: 0.8rem;
            color: #6b7280;
            margin-top: 0.25rem;
        }

        .form-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 1.5rem;
        }

        .form-grid-3 {
            display: grid;
            grid-template-columns: 1fr 1fr 1fr;
            gap: 1rem;
        }

        /* Image Upload */
        .image-upload {
            border: 2px dashed #d1d5db;
            border-radius: 8px;
            padding: 2rem;
            text-align: center;
            cursor: pointer;
            transition: all 0.3s ease;
            background: #fafbfc;
        }

        .image-upload:hover {
            border-color: #2563eb;
            background: #f0f9ff;
        }

        .image-preview {
            max-width: 100%;
            max-height: 200px;
            border-radius: 8px;
            margin-top: 1rem;
        }

        /* Category Pills */
        .category-pills {
            display: flex;
            flex-wrap: wrap;
            gap: 0.5rem;
        }

        .category-pill {
            padding: 0.5rem 1rem;
            border: 1px solid #d1d5db;
            border-radius: 20px;
            cursor: pointer;
            transition: all 0.3s ease;
            font-size: 0.85rem;
            background: white;
        }

        .category-pill:hover {
            border-color: #2563eb;
            background: #f0f9ff;
        }

        .category-pill.selected {
            background: #2563eb;
            color: white;
            border-color: #2563eb;
        }

        /* Status Badge */
        .status-badge {
            padding: 0.25rem 0.75rem;
            border-radius: 20px;
            font-size: 0.75rem;
            font-weight: 500;
            text-transform: uppercase;
        }

        .status-published {
            background-color: #d1fae5;
            color: #065f46;
        }

        .status-draft {
            background-color: #fef3c7;
            color: #92400e;
        }

        .status-archived {
            background-color: #f3f4f6;
            color: #374151;
        }

        /* Buttons */
        .btn-group {
            display: flex;
            gap: 1rem;
            justify-content: flex-end;
            margin-top: 2rem;
            padding-top: 2rem;
            border-top: 1px solid #f1f3f4;
        }

        .btn {
            padding: 0.75rem 1.5rem;
            border-radius: 6px;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.3s ease;
            border: none;
            display: inline-flex;
            align-items: center;
            gap: 0.5rem;
        }

        .btn-primary {
            background: #2563eb;
            color: white;
        }

        .btn-primary:hover {
            background: #1d4ed8;
            transform: translateY(-1px);
        }

        .btn-success {
            background: #10b981;
            color: white;
        }

        .btn-success:hover {
            background: #059669;
        }

        .btn-warning {
            background: #f59e0b;
            color: white;
        }

        .btn-warning:hover {
            background: #d97706;
        }

        .btn-danger {
            background: #ef4444;
            color: white;
        }

        .btn-danger:hover {
            background: #dc2626;
        }

        .btn-outline {
            background: white;
            color: #6b7280;
            border: 1px solid #d1d5db;
        }

        .btn-outline:hover {
            background: #f9fafb;
            border-color: #2563eb;
            color: #2563eb;
        }

        .btn:disabled {
            opacity: 0.5;
            cursor: not-allowed;
            transform: none !important;
        }

        /* Loading States */
        .loading-skeleton {
            background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
            background-size: 200% 100%;
            animation: loading 1.5s infinite;
            border-radius: 4px;
        }

        @keyframes loading {
            0% { background-position: 200% 0; }
            100% { background-position: -200% 0; }
        }

        /* Toast Animations */
        @keyframes slideIn {
            from { transform: translateX(100%); opacity: 0; }
            to { transform: translateX(0); opacity: 1; }
        }

        @keyframes slideOut {
            from { transform: translateX(0); opacity: 1; }
            to { transform: translateX(100%); opacity: 0; }
        }

        .spin {
            animation: spin 1s linear infinite;
        }

        @keyframes spin {
            from { transform: rotate(0deg); }
            to { transform: rotate(360deg); }
        }

        /* Responsive */
        @media (max-width: 768px) {
            .main-content {
                padding: 1rem;
            }
            
            .page-header {
                padding: 1.5rem;
            }
            
            .tab-content {
                padding: 1.5rem;
            }
            
            .form-grid {
                grid-template-columns: 1fr;
            }
            
            .form-grid-3 {
                grid-template-columns: 1fr;
            }
            
            .btn-group {
                flex-direction: column;
            }
            
            .course-info {
                flex-direction: column;
                text-align: center;
            }
        }
    </style>
</head>
<body>
    <!-- Navbar -->
    <nav class="navbar navbar-expand-lg">
        <div class="container-fluid">
            <a class="navbar-brand" href="dashboard-inline.html">
                <img src="https://abilityseminarsgroup.com/wp-content/uploads/2023/09/Asset-6-4-768x355.png" alt="ASG Logo">
            </a>
            
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav ms-auto">
                    <li class="nav-item">
                        <a class="nav-link" href="dashboard-inline.html">
                            <i class="bi bi-speedometer2"></i> Dashboard
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="all-courses-inline.html">
                            <i class="bi bi-collection"></i> Todos los Cursos
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="new-course-inline.html">
                            <i class="bi bi-plus-circle"></i> Nuevo Curso
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="#" onclick="showNotification('⚙️ Configuración próximamente', 'info')">
                            <i class="bi bi-gear"></i> Configuración
                        </a>
                    </li>
                </ul>
            </div>
        </div>
    </nav>

    <!-- Main Content -->
    <div class="main-content">
        <!-- Page Header -->
        <div class="page-header">
            <h1 class="page-title">✏️ Editar Curso</h1>
            <p class="page-subtitle">Modifica la información y configuración del curso</p>
            
            <!-- Course Info -->
            <div class="course-info" id="courseInfo">
                <div class="loading-skeleton" style="width: 60px; height: 45px; border-radius: 6px;"></div>
                <div>
                    <div class="loading-skeleton" style="height: 1.1rem; width: 200px; margin-bottom: 0.5rem;"></div>
                    <div class="loading-skeleton" style="height: 0.85rem; width: 150px;"></div>
                </div>
            </div>
        </div>

        <!-- Navigation Tabs -->
        <ul class="nav nav-tabs" id="editTabs" role="tablist">
            <li class="nav-item" role="presentation">
                <button class="nav-link active" id="basic-tab" data-bs-toggle="tab" data-bs-target="#basic" type="button" role="tab">
                    <i class="bi bi-info-circle"></i> Información Básica
                </button>
            </li>
            <li class="nav-item" role="presentation">
                <button class="nav-link" id="content-tab" data-bs-toggle="tab" data-bs-target="#content" type="button" role="tab">
                    <i class="bi bi-image"></i> Contenido
                </button>
            </li>
            <li class="nav-item" role="presentation">
                <button class="nav-link" id="settings-tab" data-bs-toggle="tab" data-bs-target="#settings" type="button" role="tab">
                    <i class="bi bi-gear"></i> Configuración
                </button>
            </li>
            <li class="nav-item" role="presentation">
                <button class="nav-link" id="advanced-tab" data-bs-toggle="tab" data-bs-target="#advanced" type="button" role="tab">
                    <i class="bi bi-tools"></i> Avanzado
                </button>
            </li>
        </ul>

        <!-- Tab Content -->
        <div class="tab-content" id="editTabContent">
            <!-- Información Básica -->
            <div class="tab-pane fade show active" id="basic" role="tabpanel">
                <form id="basicForm">
                    <div class="form-group">
                        <label for="courseTitle">Título del Curso</label>
                        <input type="text" id="courseTitle" class="form-control" placeholder="Cargando...">
                        <div class="form-text">Un título claro y atractivo que describa el contenido del curso</div>
                    </div>

                    <div class="form-group">
                        <label for="courseDescription">Descripción</label>
                        <textarea id="courseDescription" class="form-control" rows="4" placeholder="Cargando..."></textarea>
                        <div class="form-text">Una descripción detallada que motive a los estudiantes a inscribirse</div>
                    </div>

                    <div class="form-grid">
                        <div class="form-group">
                            <label>Categoría</label>
                            <div class="category-pills" id="categoryPills">
                                <!-- Se cargan dinámicamente -->
                            </div>
                            <input type="hidden" id="courseCategory">
                        </div>

                        <div class="form-group">
                            <label for="coursePrice">Precio (€)</label>
                            <input type="number" id="coursePrice" class="form-control" placeholder="0.00" step="0.01" min="0">
                            <div class="form-text">Precio en euros (usar punto para decimales)</div>
                        </div>
                    </div>
                </form>
            </div>

            <!-- Contenido -->
            <div class="tab-pane fade" id="content" role="tabpanel">
                <form id="contentForm">
                    <div class="form-group">
                        <label>Imagen Principal</label>
                        <div class="image-upload" id="imageUpload">
                            <div style="font-size: 2rem; color: #9ca3af; margin-bottom: 0.5rem;">
                                <i class="bi bi-cloud-upload"></i>
                            </div>
                            <div>
                                <strong>Haz clic para cambiar</strong> o arrastra una nueva imagen aquí
                            </div>
                            <div style="font-size: 0.8rem; color: #6b7280; margin-top: 0.25rem;">
                                Formatos: JPG, PNG, WebP (máx. 2MB)
                            </div>
                            <img id="imagePreview" class="image-preview" style="display: none;">
                        </div>
                        <input type="file" id="imageInput" accept="image/*" style="display: none;">
                    </div>

                    <div class="form-grid-3">
                        <div class="form-group">
                            <label for="courseModules">Número de Módulos</label>
                            <input type="number" id="courseModules" class="form-control" min="1" max="50">
                            <div class="form-text">Cantidad de módulos del curso</div>
                        </div>

                        <div class="form-group">
                            <label for="courseLessons">Número de Lecciones</label>
                            <input type="number" id="courseLessons" class="form-control" min="1" max="200">
                            <div class="form-text">Total de lecciones</div>
                        </div>

                        <div class="form-group">
                            <label for="courseDuration">Duración (horas)</label>
                            <input type="number" id="courseDuration" class="form-control" min="1" max="500">
                            <div class="form-text">Duración estimada en horas</div>
                        </div>
                    </div>
                </form>
            </div>

            <!-- Configuración -->
            <div class="tab-pane fade" id="settings" role="tabpanel">
                <form id="settingsForm">
                    <div class="form-grid">
                        <div class="form-group">
                            <label for="courseStatus">Estado del Curso</label>
                            <select id="courseStatus" class="form-control">
                                <option value="draft">✏️ Borrador</option>
                                <option value="published">✅ Publicado</option>
                                <option value="archived">📦 Archivado</option>
                            </select>
                            <div class="form-text">Los borradores no son visibles para los estudiantes</div>
                        </div>

                        <div class="form-group">
                            <label for="courseCode">Código del Curso</label>
                            <input type="text" id="courseCode" class="form-control" readonly>
                            <div class="form-text">Identificador único del curso (no editable)</div>
                        </div>
                    </div>

                    <div class="form-group">
                        <label for="courseNotes">Notas Internas</label>
                        <textarea id="courseNotes" class="form-control" rows="3" placeholder="Notas para uso interno del equipo..."></textarea>
                        <div class="form-text">Información adicional para el equipo (no visible para estudiantes)</div>
                    </div>
                </form>
            </div>

            <!-- Avanzado -->
            <div class="tab-pane fade" id="advanced" role="tabpanel">
                <div class="form-group">
                    <label>Estadísticas del Curso</label>
                    <div class="form-grid-3">
                        <div style="text-align: center; padding: 1rem; background: #f8f9fa; border-radius: 8px;">
                            <div style="font-size: 1.5rem; font-weight: 600; color: #2563eb;" id="courseStudents">0</div>
                            <div style="font-size: 0.8rem; color: #6b7280;">Estudiantes</div>
                        </div>
                        <div style="text-align: center; padding: 1rem; background: #f8f9fa; border-radius: 8px;">
                            <div style="font-size: 1.5rem; font-weight: 600; color: #10b981;" id="courseRating">0.0</div>
                            <div style="font-size: 0.8rem; color: #6b7280;">Valoración</div>
                        </div>
                        <div style="text-align: center; padding: 1rem; background: #f8f9fa; border-radius: 8px;">
                            <div style="font-size: 1.5rem; font-weight: 600; color: #f59e0b;" id="courseRevenue">€0</div>
                            <div style="font-size: 0.8rem; color: #6b7280;">Ingresos</div>
                        </div>
                    </div>
                </div>

                <div class="form-group">
                    <label>Fechas</label>
                    <div class="form-grid">
                        <div>
                            <label for="courseCreated" style="font-size: 0.8rem; color: #6b7280;">Fecha de Creación</label>
                            <input type="text" id="courseCreated" class="form-control" readonly>
                        </div>
                        <div>
                            <label for="courseUpdated" style="font-size: 0.8rem; color: #6b7280;">Última Actualización</label>
                            <input type="text" id="courseUpdated" class="form-control" readonly>
                        </div>
                    </div>
                </div>

                <div class="form-group">
                    <label>Acciones Peligrosas</label>
                    <div style="padding: 1rem; background: #fef2f2; border: 1px solid #fecaca; border-radius: 8px;">
                        <p style="color: #991b1b; margin-bottom: 1rem; font-size: 0.9rem;">
                            <i class="bi bi-exclamation-triangle"></i>
                            Estas acciones son irreversibles. Úsalas con precaución.
                        </p>
                        <div style="display: flex; gap: 1rem;">
                            <button type="button" class="btn btn-warning" onclick="duplicateCourse()">
                                <i class="bi bi-files"></i> Duplicar Curso
                            </button>
                            <button type="button" class="btn btn-danger" onclick="deleteCourse()">
                                <i class="bi bi-trash"></i> Eliminar Curso
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Action Buttons -->
        <div class="btn-group">
            <button type="button" class="btn btn-outline" onclick="cancelEdit()">
                <i class="bi bi-x-circle"></i> Cancelar
            </button>
            <button type="button" class="btn btn-warning" onclick="saveDraft()">
                <i class="bi bi-file-earmark"></i> Guardar Borrador
            </button>
            <button type="button" class="btn btn-success" onclick="publishCourse()">
                <i class="bi bi-check-circle"></i> Publicar
            </button>
            <button type="button" class="btn btn-primary" onclick="saveChanges()">
                <i class="bi bi-save"></i> Guardar Cambios
            </button>
        </div>
    </div>

    <!-- Scripts -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.3/dist/js/bootstrap.bundle.min.js"></script>

    <!-- JavaScript Inline -->
    <script>
        // ========================================
        // ASG CONFIGURACIÓN INLINE
        // ========================================
        const ASG_CONFIG = {
            version: '2.0.0',
            name: 'AbilitySeminarsGroup Course Management',
            apiUrl: 'https://abilityseminarsgroup.com/wp-json/asg/v1',
            mockData: true,

            // Endpoints
            endpoints: {
                courses: '/courses',
                course: '/courses/{code}',
                uploadImage: '/courses/upload-image',
                updateCourse: '/courses/{code}'
            },

            // Categorías
            categories: {
                finanzas: { label: 'Finanzas', icon: '💰', color: '#10b981' },
                marketing: { label: 'Marketing', icon: '📱', color: '#f59e0b' },
                'desarrollo-personal': { label: 'Desarrollo Personal', icon: '🧠', color: '#8b5cf6' },
                tecnologia: { label: 'Tecnología', icon: '💻', color: '#06b6d4' },
                negocios: { label: 'Negocios', icon: '💼', color: '#ef4444' },
                salud: { label: 'Salud', icon: '🏥', color: '#84cc16' }
            },

            // Estados
            states: {
                draft: { label: 'Borrador', color: '#f59e0b', badge: 'status-draft' },
                published: { label: 'Publicado', color: '#10b981', badge: 'status-published' },
                archived: { label: 'Archivado', color: '#6b7280', badge: 'status-archived' }
            }
        };

        // ========================================
        // DATOS MOCK
        // ========================================
        const MOCK_COURSES = {
            'course_1': {
                id: 1,
                code: 'course_1',
                title: 'Como hacerte millonario?',
                description: 'Aprende las estrategias más efectivas para generar riqueza y alcanzar la libertad financiera. Descubre los secretos de los millonarios y aplica técnicas probadas.',
                image: 'https://images.unsplash.com/photo-1579621970563-ebec7560ff3e?w=400&h=300&fit=crop',
                price: 299.99,
                status: 'published',
                category: 'finanzas',
                modules: 8,
                lessons: 24,
                duration: 12,
                students: 156,
                rating: 4.8,
                revenue: 46799.44,
                created: '2024-12-15',
                updated: '2024-12-20',
                notes: 'Curso muy popular, considerar crear versión avanzada'
            },
            'course_2': {
                id: 2,
                code: 'course_2',
                title: 'Marketing Digital Avanzado',
                description: 'Domina las técnicas más avanzadas del marketing digital y haz crecer tu negocio online. Aprende SEO, SEM, redes sociales y automatización.',
                image: 'https://images.unsplash.com/photo-1460925895917-afdab827c52f?w=400&h=300&fit=crop',
                price: 199.99,
                status: 'published',
                category: 'marketing',
                modules: 6,
                lessons: 18,
                duration: 8,
                students: 89,
                rating: 4.6,
                revenue: 17799.11,
                created: '2024-12-10',
                updated: '2024-12-18',
                notes: 'Actualizar contenido de redes sociales'
            },
            'course_3': {
                id: 3,
                code: 'course_3',
                title: 'Desarrollo Personal Integral',
                description: 'Transforma tu vida con técnicas probadas de crecimiento personal y liderazgo. Desarrolla habilidades de comunicación, liderazgo y productividad.',
                image: 'https://images.unsplash.com/photo-1516321318423-f06f85e504b3?w=400&h=300&fit=crop',
                price: 149.99,
                status: 'draft',
                category: 'desarrollo-personal',
                modules: 4,
                lessons: 12,
                duration: 6,
                students: 0,
                rating: 0,
                revenue: 0,
                created: '2024-12-05',
                updated: '2024-12-22',
                notes: 'Pendiente de revisión final antes de publicar'
            }
        };

        // ========================================
        // VARIABLES GLOBALES
        // ========================================
        let currentCourse = null;
        let originalCourseData = null;
        let hasUnsavedChanges = false;

        // ========================================
        // UTILIDADES
        // ========================================
        const ASG_UTILS = {
            formatCurrency: (amount) => {
                return new Intl.NumberFormat('es-ES', {
                    style: 'currency',
                    currency: 'EUR'
                }).format(amount || 0);
            },

            formatDate: (date) => {
                return new Intl.DateTimeFormat('es-ES', {
                    year: 'numeric',
                    month: 'long',
                    day: 'numeric',
                    hour: '2-digit',
                    minute: '2-digit'
                }).format(new Date(date));
            },

            getCategoryInfo: (categoryCode) => {
                return ASG_CONFIG.categories[categoryCode] || {
                    label: categoryCode,
                    icon: '📚',
                    color: '#6b7280'
                };
            },

            getStatusInfo: (statusCode) => {
                return ASG_CONFIG.states[statusCode] || {
                    label: statusCode,
                    color: '#6b7280',
                    badge: 'status-draft'
                };
            },

            getUrlParameter: (name) => {
                const urlParams = new URLSearchParams(window.location.search);
                return urlParams.get(name);
            },

            debounce: (func, wait) => {
                let timeout;
                return function executedFunction(...args) {
                    const later = () => {
                        clearTimeout(timeout);
                        func(...args);
                    };
                    clearTimeout(timeout);
                    timeout = setTimeout(later, wait);
                };
            }
        };

        // ========================================
        // FUNCIONES DE INTERFAZ
        // ========================================
        function showNotification(message, type = 'info') {
            const toast = document.createElement('div');
            const colors = {
                success: '#10b981',
                error: '#ef4444',
                warning: '#f59e0b',
                info: '#06b6d4'
            };

            const icons = {
                success: '✅',
                error: '❌',
                warning: '⚠️',
                info: 'ℹ️'
            };

            toast.style.cssText = `
                position: fixed;
                top: 90px;
                right: 20px;
                background: ${colors[type] || colors.info};
                color: white;
                padding: 12px 16px;
                border-radius: 8px;
                box-shadow: 0 4px 12px rgba(0,0,0,0.15);
                z-index: 9999;
                animation: slideIn 0.3s ease-out;
                cursor: pointer;
                max-width: 400px;
            `;

            toast.innerHTML = `
                <div style="display: flex; align-items: center; gap: 8px;">
                    <span>${icons[type] || icons.info}</span>
                    <span>${message}</span>
                    <span style="opacity: 0.7; margin-left: auto;">×</span>
                </div>
            `;

            toast.addEventListener('click', () => {
                toast.style.animation = 'slideOut 0.3s ease-in';
                setTimeout(() => document.body.removeChild(toast), 300);
            });

            document.body.appendChild(toast);

            setTimeout(() => {
                if (toast.parentNode) {
                    toast.style.animation = 'slideOut 0.3s ease-in';
                    setTimeout(() => {
                        if (toast.parentNode) {
                            document.body.removeChild(toast);
                        }
                    }, 300);
                }
            }, 5000);
        }

        // ========================================
        // FUNCIONES DE DATOS
        // ========================================
        async function loadCourse(courseCode) {
            try {
                showNotification('📚 Cargando datos del curso...', 'info');

                // Simular carga de API
                await new Promise(resolve => setTimeout(resolve, 1000));

                // Usar datos mock
                const course = MOCK_COURSES[courseCode];
                if (!course) {
                    throw new Error('Curso no encontrado');
                }

                currentCourse = { ...course };
                originalCourseData = { ...course };

                renderCourseInfo();
                populateForm();

                showNotification('✅ Curso cargado correctamente', 'success');
                console.log('Course loaded:', currentCourse);

            } catch (error) {
                showNotification('❌ Error al cargar el curso', 'error');
                console.error('Load course error:', error);

                // Redirigir a la lista de cursos
                setTimeout(() => {
                    window.location.href = 'all-courses-inline.html';
                }, 2000);
            }
        }

        function renderCourseInfo() {
            if (!currentCourse) return;

            const category = ASG_UTILS.getCategoryInfo(currentCourse.category);
            const status = ASG_UTILS.getStatusInfo(currentCourse.status);

            document.getElementById('courseInfo').innerHTML = `
                <div class="course-image-small" style="background-image: url('${currentCourse.image}')"></div>
                <div class="course-details">
                    <h4>${currentCourse.title}</h4>
                    <div class="course-meta">
                        ${category.icon} ${category.label} •
                        <span class="status-badge ${status.badge}">${status.label}</span> •
                        ${ASG_UTILS.formatCurrency(currentCourse.price)} •
                        ${currentCourse.students} estudiantes
                    </div>
                </div>
            `;
        }

        function populateForm() {
            if (!currentCourse) return;

            // Información básica
            document.getElementById('courseTitle').value = currentCourse.title;
            document.getElementById('courseDescription').value = currentCourse.description;
            document.getElementById('coursePrice').value = currentCourse.price;
            document.getElementById('courseCategory').value = currentCourse.category;

            // Contenido
            document.getElementById('courseModules').value = currentCourse.modules;
            document.getElementById('courseLessons').value = currentCourse.lessons;
            document.getElementById('courseDuration').value = currentCourse.duration;

            // Configuración
            document.getElementById('courseStatus').value = currentCourse.status;
            document.getElementById('courseCode').value = currentCourse.code;
            document.getElementById('courseNotes').value = currentCourse.notes || '';

            // Estadísticas avanzadas
            document.getElementById('courseStudents').textContent = currentCourse.students;
            document.getElementById('courseRating').textContent = currentCourse.rating.toFixed(1);
            document.getElementById('courseRevenue').textContent = ASG_UTILS.formatCurrency(currentCourse.revenue);
            document.getElementById('courseCreated').value = ASG_UTILS.formatDate(currentCourse.created);
            document.getElementById('courseUpdated').value = ASG_UTILS.formatDate(currentCourse.updated);

            // Imagen
            if (currentCourse.image) {
                const preview = document.getElementById('imagePreview');
                preview.src = currentCourse.image;
                preview.style.display = 'block';
            }

            // Categorías
            renderCategories();
            selectCategory(currentCourse.category);
        }

        function renderCategories() {
            const container = document.getElementById('categoryPills');

            container.innerHTML = Object.entries(ASG_CONFIG.categories).map(([code, info]) => `
                <div class="category-pill" data-category="${code}" onclick="selectCategory('${code}')">
                    ${info.icon} ${info.label}
                </div>
            `).join('');
        }

        function selectCategory(categoryCode) {
            // Limpiar selección anterior
            document.querySelectorAll('.category-pill').forEach(pill => {
                pill.classList.remove('selected');
            });

            // Seleccionar nueva categoría
            const selectedPill = document.querySelector(`[data-category="${categoryCode}"]`);
            if (selectedPill) {
                selectedPill.classList.add('selected');
                currentCourse.category = categoryCode;
                document.getElementById('courseCategory').value = categoryCode;
                markAsChanged();
            }
        }

        // ========================================
        // FUNCIONES DE CONTROL DE CAMBIOS
        // ========================================
        function markAsChanged() {
            hasUnsavedChanges = true;
            updatePageTitle();
        }

        function updatePageTitle() {
            const title = document.querySelector('.page-title');
            if (hasUnsavedChanges && !title.textContent.includes('*')) {
                title.textContent = '✏️ Editar Curso *';
            } else if (!hasUnsavedChanges && title.textContent.includes('*')) {
                title.textContent = '✏️ Editar Curso';
            }
        }

        function setupChangeTracking() {
            const inputs = document.querySelectorAll('input, textarea, select');

            inputs.forEach(input => {
                if (input.id !== 'courseCode' && input.id !== 'courseCreated' && input.id !== 'courseUpdated') {
                    input.addEventListener('input', markAsChanged);
                    input.addEventListener('change', markAsChanged);
                }
            });
        }

        // ========================================
        // FUNCIONES DE GUARDADO
        // ========================================
        function updateCourseData() {
            if (!currentCourse) return;

            currentCourse.title = document.getElementById('courseTitle').value;
            currentCourse.description = document.getElementById('courseDescription').value;
            currentCourse.price = parseFloat(document.getElementById('coursePrice').value) || 0;
            currentCourse.modules = parseInt(document.getElementById('courseModules').value) || 1;
            currentCourse.lessons = parseInt(document.getElementById('courseLessons').value) || 1;
            currentCourse.duration = parseInt(document.getElementById('courseDuration').value) || 1;
            currentCourse.status = document.getElementById('courseStatus').value;
            currentCourse.notes = document.getElementById('courseNotes').value;
            currentCourse.updated = new Date().toISOString();
        }

        async function saveChanges() {
            if (!currentCourse) return;

            try {
                const btn = event.target;
                const originalText = btn.innerHTML;

                btn.innerHTML = '<i class="bi bi-arrow-clockwise spin"></i> Guardando...';
                btn.disabled = true;

                updateCourseData();

                showNotification('💾 Guardando cambios...', 'info');

                // Simular guardado
                await new Promise(resolve => setTimeout(resolve, 1500));

                // Actualizar datos originales
                originalCourseData = { ...currentCourse };
                hasUnsavedChanges = false;
                updatePageTitle();

                // Actualizar info del curso
                renderCourseInfo();

                showNotification('✅ Cambios guardados correctamente', 'success');
                console.log('Course updated:', currentCourse);

            } catch (error) {
                showNotification('❌ Error al guardar los cambios', 'error');
                console.error('Save error:', error);
            } finally {
                const btn = document.querySelector('.btn-primary');
                btn.innerHTML = '<i class="bi bi-save"></i> Guardar Cambios';
                btn.disabled = false;
            }
        }

        async function saveDraft() {
            if (!currentCourse) return;

            try {
                updateCourseData();
                currentCourse.status = 'draft';
                document.getElementById('courseStatus').value = 'draft';

                showNotification('💾 Guardando como borrador...', 'info');

                // Simular guardado
                await new Promise(resolve => setTimeout(resolve, 1000));

                hasUnsavedChanges = false;
                updatePageTitle();
                renderCourseInfo();

                showNotification('✅ Borrador guardado correctamente', 'success');

            } catch (error) {
                showNotification('❌ Error al guardar el borrador', 'error');
            }
        }

        async function publishCourse() {
            if (!currentCourse) return;

            if (!confirm('¿Estás seguro de que quieres publicar este curso? Será visible para todos los estudiantes.')) {
                return;
            }

            try {
                updateCourseData();
                currentCourse.status = 'published';
                document.getElementById('courseStatus').value = 'published';

                showNotification('🚀 Publicando curso...', 'info');

                // Simular publicación
                await new Promise(resolve => setTimeout(resolve, 2000));

                hasUnsavedChanges = false;
                updatePageTitle();
                renderCourseInfo();

                showNotification('✅ Curso publicado exitosamente', 'success');

            } catch (error) {
                showNotification('❌ Error al publicar el curso', 'error');
            }
        }

        // ========================================
        // FUNCIONES DE ACCIONES AVANZADAS
        // ========================================
        async function duplicateCourse() {
            if (!currentCourse) return;

            if (!confirm('¿Quieres duplicar este curso? Se creará una copia con el estado "Borrador".')) {
                return;
            }

            try {
                showNotification('📋 Duplicando curso...', 'info');

                // Simular duplicación
                await new Promise(resolve => setTimeout(resolve, 1500));

                const newCode = `${currentCourse.code}_copy_${Date.now()}`;

                showNotification(`✅ Curso duplicado como: ${newCode}`, 'success');
                console.log('Course duplicated:', newCode);

            } catch (error) {
                showNotification('❌ Error al duplicar el curso', 'error');
            }
        }

        async function deleteCourse() {
            if (!currentCourse) return;

            const confirmText = `ELIMINAR ${currentCourse.title.toUpperCase()}`;
            const userInput = prompt(`⚠️ ACCIÓN IRREVERSIBLE ⚠️\n\nEsto eliminará permanentemente el curso "${currentCourse.title}" y todos sus datos.\n\nPara confirmar, escribe exactamente: ${confirmText}`);

            if (userInput !== confirmText) {
                showNotification('❌ Eliminación cancelada - texto no coincide', 'warning');
                return;
            }

            try {
                showNotification('🗑️ Eliminando curso...', 'warning');

                // Simular eliminación
                await new Promise(resolve => setTimeout(resolve, 2000));

                showNotification('✅ Curso eliminado correctamente', 'success');

                // Redirigir después de eliminar
                setTimeout(() => {
                    window.location.href = 'all-courses-inline.html';
                }, 1500);

            } catch (error) {
                showNotification('❌ Error al eliminar el curso', 'error');
            }
        }

        function cancelEdit() {
            if (hasUnsavedChanges) {
                if (!confirm('Tienes cambios sin guardar. ¿Estás seguro de que quieres salir?')) {
                    return;
                }
            }

            window.location.href = 'all-courses-inline.html';
        }

        // ========================================
        // FUNCIONES DE IMAGEN
        // ========================================
        function setupImageUpload() {
            const uploadArea = document.getElementById('imageUpload');
            const fileInput = document.getElementById('imageInput');

            uploadArea.addEventListener('click', () => {
                fileInput.click();
            });

            uploadArea.addEventListener('dragover', (e) => {
                e.preventDefault();
                uploadArea.style.borderColor = '#2563eb';
                uploadArea.style.background = '#dbeafe';
            });

            uploadArea.addEventListener('dragleave', () => {
                uploadArea.style.borderColor = '#d1d5db';
                uploadArea.style.background = '#fafbfc';
            });

            uploadArea.addEventListener('drop', (e) => {
                e.preventDefault();
                uploadArea.style.borderColor = '#d1d5db';
                uploadArea.style.background = '#fafbfc';

                const files = e.dataTransfer.files;
                if (files.length > 0) {
                    handleImageFile(files[0]);
                }
            });

            fileInput.addEventListener('change', (e) => {
                if (e.target.files.length > 0) {
                    handleImageFile(e.target.files[0]);
                }
            });
        }

        function handleImageFile(file) {
            const validTypes = ['image/jpeg', 'image/png', 'image/webp'];
            if (!validTypes.includes(file.type)) {
                showNotification('❌ Formato no válido. Usa JPG, PNG o WebP', 'error');
                return;
            }

            if (file.size > 2 * 1024 * 1024) {
                showNotification('❌ Imagen muy grande. Máximo 2MB', 'error');
                return;
            }

            const reader = new FileReader();
            reader.onload = (e) => {
                const preview = document.getElementById('imagePreview');
                preview.src = e.target.result;
                preview.style.display = 'block';

                if (currentCourse) {
                    currentCourse.image = e.target.result;
                    markAsChanged();
                }

                showNotification(`✅ Nueva imagen: ${file.name}`, 'success');
            };

            reader.readAsDataURL(file);
        }

        // ========================================
        // INICIALIZACIÓN
        // ========================================
        document.addEventListener('DOMContentLoaded', function() {
            console.log('🚀 Edit Course ASG Inline v2.0.0 loaded');

            // Obtener código del curso de la URL
            const courseCode = ASG_UTILS.getUrlParameter('course') || 'course_1';

            // Configurar upload de imagen
            setupImageUpload();

            // Configurar seguimiento de cambios
            setupChangeTracking();

            // Cargar curso
            setTimeout(() => {
                loadCourse(courseCode);
            }, 500);

            // Mostrar notificación de bienvenida
            setTimeout(() => {
                showNotification('✏️ Editor de curso cargado', 'success');
            }, 1000);

            // Configurar atajos de teclado
            document.addEventListener('keydown', (e) => {
                if (e.ctrlKey || e.metaKey) {
                    switch(e.key) {
                        case 's':
                            e.preventDefault();
                            saveChanges();
                            break;
                        case 'p':
                            e.preventDefault();
                            publishCourse();
                            break;
                        case 'd':
                            e.preventDefault();
                            saveDraft();
                            break;
                    }
                }
            });

            // Advertir sobre cambios no guardados
            window.addEventListener('beforeunload', (e) => {
                if (hasUnsavedChanges) {
                    e.preventDefault();
                    e.returnValue = 'Tienes cambios sin guardar. ¿Estás seguro de que quieres salir?';
                }
            });
        });

        // ========================================
        // FUNCIONES GLOBALES
        // ========================================
        window.ASG_EDIT_COURSE = {
            saveChanges: saveChanges,
            saveDraft: saveDraft,
            publishCourse: publishCourse,
            duplicateCourse: duplicateCourse,
            deleteCourse: deleteCourse,
            cancelEdit: cancelEdit,
            showNotification: showNotification,
            config: ASG_CONFIG,
            utils: ASG_UTILS,
            currentCourse: () => currentCourse
        };

        // Log de información del sistema
        console.log('✏️ ASG Edit Course System Info:', {
            version: ASG_CONFIG.version,
            courseCode: ASG_UTILS.getUrlParameter('course'),
            categories: Object.keys(ASG_CONFIG.categories).length,
            timestamp: new Date().toISOString()
        });
    </script>
</body>
</html>

        // ========================================
        // FUNCIONES DE DATOS
        // ========================================
        async function loadCourse(courseCode) {
            try {
                showNotification('📚 Cargando datos del curso...', 'info');

                // Simular carga de API
                await new Promise(resolve => setTimeout(resolve, 1000));

                // Usar datos mock
                const course = MOCK_COURSES[courseCode];
                if (!course) {
                    throw new Error('Curso no encontrado');
                }

                currentCourse = { ...course };
                originalCourseData = { ...course };

                renderCourseInfo();
                populateForm();

                showNotification('✅ Curso cargado correctamente', 'success');
                console.log('Course loaded:', currentCourse);

            } catch (error) {
                showNotification('❌ Error al cargar el curso', 'error');
                console.error('Load course error:', error);

                // Redirigir a la lista de cursos
                setTimeout(() => {
                    window.location.href = 'all-courses-inline.html';
                }, 2000);
            }
        }

        function renderCourseInfo() {
            if (!currentCourse) return;

            const category = ASG_UTILS.getCategoryInfo(currentCourse.category);
            const status = ASG_UTILS.getStatusInfo(currentCourse.status);

            document.getElementById('courseInfo').innerHTML = `
                <div class="course-image-small" style="background-image: url('${currentCourse.image}')"></div>
                <div class="course-details">
                    <h4>${currentCourse.title}</h4>
                    <div class="course-meta">
                        ${category.icon} ${category.label} •
                        <span class="status-badge ${status.badge}">${status.label}</span> •
                        ${ASG_UTILS.formatCurrency(currentCourse.price)} •
                        ${currentCourse.students} estudiantes
                    </div>
                </div>
            `;
        }

        function populateForm() {
            if (!currentCourse) return;

            // Información básica
            document.getElementById('courseTitle').value = currentCourse.title;
            document.getElementById('courseDescription').value = currentCourse.description;
            document.getElementById('coursePrice').value = currentCourse.price;
            document.getElementById('courseCategory').value = currentCourse.category;

            // Contenido
            document.getElementById('courseModules').value = currentCourse.modules;
            document.getElementById('courseLessons').value = currentCourse.lessons;
            document.getElementById('courseDuration').value = currentCourse.duration;

            // Configuración
            document.getElementById('courseStatus').value = currentCourse.status;
            document.getElementById('courseCode').value = currentCourse.code;
            document.getElementById('courseNotes').value = currentCourse.notes || '';

            // Estadísticas avanzadas
            document.getElementById('courseStudents').textContent = currentCourse.students;
            document.getElementById('courseRating').textContent = currentCourse.rating.toFixed(1);
            document.getElementById('courseRevenue').textContent = ASG_UTILS.formatCurrency(currentCourse.revenue);
            document.getElementById('courseCreated').value = ASG_UTILS.formatDate(currentCourse.created);
            document.getElementById('courseUpdated').value = ASG_UTILS.formatDate(currentCourse.updated);

            // Imagen
            if (currentCourse.image) {
                const preview = document.getElementById('imagePreview');
                preview.src = currentCourse.image;
                preview.style.display = 'block';
            }

            // Categorías
            renderCategories();
            selectCategory(currentCourse.category);
        }

        function renderCategories() {
            const container = document.getElementById('categoryPills');

            container.innerHTML = Object.entries(ASG_CONFIG.categories).map(([code, info]) => `
                <div class="category-pill" data-category="${code}" onclick="selectCategory('${code}')">
                    ${info.icon} ${info.label}
                </div>
            `).join('');
        }

        function selectCategory(categoryCode) {
            // Limpiar selección anterior
            document.querySelectorAll('.category-pill').forEach(pill => {
                pill.classList.remove('selected');
            });

            // Seleccionar nueva categoría
            const selectedPill = document.querySelector(`[data-category="${categoryCode}"]`);
            if (selectedPill) {
                selectedPill.classList.add('selected');
                currentCourse.category = categoryCode;
                document.getElementById('courseCategory').value = categoryCode;
                markAsChanged();
            }
        }

        // ========================================
        // FUNCIONES DE IMAGEN
        // ========================================
        function setupImageUpload() {
            const uploadArea = document.getElementById('imageUpload');
            const fileInput = document.getElementById('imageInput');
            const preview = document.getElementById('imagePreview');

            // Click para seleccionar archivo
            uploadArea.addEventListener('click', () => {
                fileInput.click();
            });

            // Drag and drop
            uploadArea.addEventListener('dragover', (e) => {
                e.preventDefault();
                uploadArea.style.borderColor = '#2563eb';
                uploadArea.style.background = '#dbeafe';
            });

            uploadArea.addEventListener('dragleave', () => {
                uploadArea.style.borderColor = '#d1d5db';
                uploadArea.style.background = '#fafbfc';
            });

            uploadArea.addEventListener('drop', (e) => {
                e.preventDefault();
                uploadArea.style.borderColor = '#d1d5db';
                uploadArea.style.background = '#fafbfc';

                const files = e.dataTransfer.files;
                if (files.length > 0) {
                    handleImageFile(files[0]);
                }
            });

            // Cambio de archivo
            fileInput.addEventListener('change', (e) => {
                if (e.target.files.length > 0) {
                    handleImageFile(e.target.files[0]);
                }
            });
        }

        function handleImageFile(file) {
            // Validar tipo de archivo
            const validTypes = ['image/jpeg', 'image/png', 'image/webp'];
            if (!validTypes.includes(file.type)) {
                showNotification('❌ Formato de imagen no válido. Usa JPG, PNG o WebP', 'error');
                return;
            }

            // Validar tamaño (2MB)
            if (file.size > 2 * 1024 * 1024) {
                showNotification('❌ La imagen es muy grande. Máximo 2MB', 'error');
                return;
            }

            // Mostrar preview
            const reader = new FileReader();
            reader.onload = (e) => {
                const preview = document.getElementById('imagePreview');
                preview.src = e.target.result;
                preview.style.display = 'block';

                currentCourse.image = e.target.result;
                markAsChanged();

                showNotification(`✅ Nueva imagen cargada: ${file.name}`, 'success');
            };

            reader.readAsDataURL(file);
        }
