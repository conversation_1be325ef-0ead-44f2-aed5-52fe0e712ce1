/**
 * ========================================
 * ASG COMPONENTS v2.0 - SISTEMA MODULAR
 * ========================================
 * 
 * Descripción: Componentes JavaScript reutilizables para el sistema ASG
 * Incluye: Sidebar, Modales, Notificaciones, Formularios, Cards
 * 
 * Autor: ASG Team
 * Fecha: 2025-01-04
 * Versión: 2.0.0 - COMPONENTES MODERNOS
 */

/**
 * ========================================
 * CONFIGURACIÓN GLOBAL
 * ========================================
 */
const ASG_CONFIG = {
    apiBaseUrl: 'https://abilityseminarsgroup.com/wp-json/asg/v2',
    logoUrl: 'https://abilityseminarsgroup.com/wp-content/uploads/2023/09/Asset-6-4-768x355.png',
    breakpoints: {
        mobile: 640,
        tablet: 768,
        desktop: 1024,
        xl: 1280
    },
    animations: {
        fast: 150,
        normal: 200,
        slow: 300
    }
};

/**
 * ========================================
 * COMPONENTE: SIDEBAR MANAGER
 * ========================================
 */
class ASGSidebar {
    constructor(options = {}) {
        this.options = {
            selector: '.asg-sidebar',
            toggleSelector: '.asg-sidebar-toggle',
            overlaySelector: '.asg-sidebar-overlay',
            breakpoint: ASG_CONFIG.breakpoints.desktop,
            ...options
        };
        
        this.sidebar = document.querySelector(this.options.selector);
        this.toggle = document.querySelector(this.options.toggleSelector);
        this.overlay = document.querySelector(this.options.overlaySelector);
        this.isOpen = false;
        this.isMobile = window.innerWidth < this.options.breakpoint;
        
        this.init();
    }
    
    init() {
        if (!this.sidebar) return;
        
        this.setupEventListeners();
        this.handleResize();
        
        console.log('🎯 ASG Sidebar inicializado');
    }
    
    setupEventListeners() {
        // Toggle button
        if (this.toggle) {
            this.toggle.addEventListener('click', (e) => {
                e.preventDefault();
                this.toggle();
            });
        }
        
        // Overlay click (mobile)
        if (this.overlay) {
            this.overlay.addEventListener('click', () => {
                this.close();
            });
        }
        
        // Escape key
        document.addEventListener('keydown', (e) => {
            if (e.key === 'Escape' && this.isOpen && this.isMobile) {
                this.close();
            }
        });
        
        // Resize handler
        window.addEventListener('resize', () => {
            this.handleResize();
        });
        
        // Navigation items
        this.setupNavigation();
    }
    
    setupNavigation() {
        const navItems = this.sidebar.querySelectorAll('.asg-sidebar-item');
        
        navItems.forEach(item => {
            item.addEventListener('click', (e) => {
                // Remover active de todos los items
                navItems.forEach(nav => nav.classList.remove('active'));
                
                // Agregar active al item clickeado
                item.classList.add('active');
                
                // Cerrar sidebar en mobile después de navegación
                if (this.isMobile) {
                    setTimeout(() => this.close(), 150);
                }
            });
        });
    }
    
    toggle() {
        if (this.isOpen) {
            this.close();
        } else {
            this.open();
        }
    }
    
    open() {
        this.sidebar.classList.add('open');
        if (this.overlay) this.overlay.classList.add('active');
        document.body.classList.add('sidebar-open');
        this.isOpen = true;
        
        this.sidebar.dispatchEvent(new CustomEvent('sidebar:opened'));
    }
    
    close() {
        this.sidebar.classList.remove('open');
        if (this.overlay) this.overlay.classList.remove('active');
        document.body.classList.remove('sidebar-open');
        this.isOpen = false;
        
        this.sidebar.dispatchEvent(new CustomEvent('sidebar:closed'));
    }
    
    handleResize() {
        const wasMobile = this.isMobile;
        this.isMobile = window.innerWidth < this.options.breakpoint;
        
        // Si cambió de mobile a desktop, cerrar sidebar
        if (wasMobile && !this.isMobile && this.isOpen) {
            this.close();
        }
    }
}

/**
 * ========================================
 * COMPONENTE: NOTIFICATION SYSTEM
 * ========================================
 */
class ASGNotifications {
    constructor(options = {}) {
        this.options = {
            container: 'body',
            position: 'top-right',
            duration: 5000,
            maxNotifications: 5,
            ...options
        };
        
        this.notifications = [];
        this.container = null;
        
        this.init();
    }
    
    init() {
        this.createContainer();
        console.log('🔔 ASG Notifications inicializado');
    }
    
    createContainer() {
        this.container = document.createElement('div');
        this.container.className = `asg-notifications asg-notifications--${this.options.position}`;
        this.container.style.cssText = `
            position: fixed;
            z-index: 9999;
            pointer-events: none;
            ${this.getPositionStyles()}
        `;
        
        document.body.appendChild(this.container);
    }
    
    getPositionStyles() {
        const positions = {
            'top-right': 'top: 1rem; right: 1rem;',
            'top-left': 'top: 1rem; left: 1rem;',
            'bottom-right': 'bottom: 1rem; right: 1rem;',
            'bottom-left': 'bottom: 1rem; left: 1rem;',
            'top-center': 'top: 1rem; left: 50%; transform: translateX(-50%);',
            'bottom-center': 'bottom: 1rem; left: 50%; transform: translateX(-50%);'
        };
        
        return positions[this.options.position] || positions['top-right'];
    }
    
    show(message, type = 'info', options = {}) {
        const notification = this.createNotification(message, type, options);
        
        // Limitar número de notificaciones
        if (this.notifications.length >= this.options.maxNotifications) {
            this.remove(this.notifications[0]);
        }
        
        this.notifications.push(notification);
        this.container.appendChild(notification.element);
        
        // Animación de entrada
        requestAnimationFrame(() => {
            notification.element.classList.add('show');
        });
        
        // Auto-remove
        if (options.duration !== false) {
            const duration = options.duration || this.options.duration;
            notification.timeout = setTimeout(() => {
                this.remove(notification);
            }, duration);
        }
        
        return notification;
    }
    
    createNotification(message, type, options) {
        const id = 'notification-' + Date.now() + Math.random().toString(36).substr(2, 9);
        
        const element = document.createElement('div');
        element.className = `asg-notification asg-notification--${type}`;
        element.style.cssText = `
            pointer-events: auto;
            margin-bottom: 0.5rem;
            padding: 1rem 1.5rem;
            background: white;
            border-radius: 0.5rem;
            box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1);
            border-left: 4px solid ${this.getTypeColor(type)};
            transform: translateX(100%);
            opacity: 0;
            transition: all 0.3s ease;
            max-width: 400px;
            word-wrap: break-word;
        `;
        
        const icon = this.getTypeIcon(type);
        const closeButton = options.closable !== false ? `
            <button class="asg-notification-close" style="
                position: absolute;
                top: 0.5rem;
                right: 0.5rem;
                background: none;
                border: none;
                font-size: 1.2rem;
                cursor: pointer;
                color: #6b7280;
                padding: 0;
                width: 1.5rem;
                height: 1.5rem;
                display: flex;
                align-items: center;
                justify-content: center;
            ">&times;</button>
        ` : '';
        
        element.innerHTML = `
            <div style="display: flex; align-items: flex-start; gap: 0.75rem; position: relative;">
                <div style="color: ${this.getTypeColor(type)}; font-size: 1.25rem; flex-shrink: 0; margin-top: 0.125rem;">
                    ${icon}
                </div>
                <div style="flex: 1; font-size: 0.875rem; line-height: 1.5; color: #374151;">
                    ${message}
                </div>
                ${closeButton}
            </div>
        `;
        
        // Agregar clase para animación
        element.classList.add('asg-notification-enter');
        
        // Event listener para cerrar
        const closeBtn = element.querySelector('.asg-notification-close');
        if (closeBtn) {
            closeBtn.addEventListener('click', () => {
                this.remove({ element, id });
            });
        }
        
        return { element, id, type, timeout: null };
    }
    
    getTypeColor(type) {
        const colors = {
            success: '#10b981',
            error: '#ef4444',
            warning: '#f59e0b',
            info: '#3b82f6'
        };
        return colors[type] || colors.info;
    }
    
    getTypeIcon(type) {
        const icons = {
            success: '✓',
            error: '✕',
            warning: '⚠',
            info: 'ℹ'
        };
        return icons[type] || icons.info;
    }
    
    remove(notification) {
        if (!notification || !notification.element) return;
        
        // Limpiar timeout
        if (notification.timeout) {
            clearTimeout(notification.timeout);
        }
        
        // Animación de salida
        notification.element.style.transform = 'translateX(100%)';
        notification.element.style.opacity = '0';
        
        setTimeout(() => {
            if (notification.element.parentNode) {
                notification.element.parentNode.removeChild(notification.element);
            }
            
            // Remover de array
            const index = this.notifications.findIndex(n => n.id === notification.id);
            if (index > -1) {
                this.notifications.splice(index, 1);
            }
        }, 300);
    }
    
    // Métodos de conveniencia
    success(message, options = {}) {
        return this.show(message, 'success', options);
    }
    
    error(message, options = {}) {
        return this.show(message, 'error', options);
    }
    
    warning(message, options = {}) {
        return this.show(message, 'warning', options);
    }
    
    info(message, options = {}) {
        return this.show(message, 'info', options);
    }
    
    clear() {
        this.notifications.forEach(notification => {
            this.remove(notification);
        });
    }
}

/**
 * ========================================
 * COMPONENTE: LOADING MANAGER
 * ========================================
 */
class ASGLoading {
    constructor() {
        this.activeLoaders = new Set();
        this.overlay = null;
        this.init();
    }
    
    init() {
        this.createOverlay();
        console.log('⏳ ASG Loading Manager inicializado');
    }
    
    createOverlay() {
        this.overlay = document.createElement('div');
        this.overlay.className = 'asg-loading-overlay';
        this.overlay.style.cssText = `
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.5);
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 9998;
            opacity: 0;
            visibility: hidden;
            transition: all 0.3s ease;
        `;
        
        this.overlay.innerHTML = `
            <div class="asg-loading-spinner" style="
                width: 3rem;
                height: 3rem;
                border: 3px solid rgba(255, 255, 255, 0.3);
                border-top: 3px solid white;
                border-radius: 50%;
                animation: asg-spin 1s linear infinite;
            "></div>
        `;
        
        // Agregar keyframes para animación
        if (!document.querySelector('#asg-loading-styles')) {
            const style = document.createElement('style');
            style.id = 'asg-loading-styles';
            style.textContent = `
                @keyframes asg-spin {
                    0% { transform: rotate(0deg); }
                    100% { transform: rotate(360deg); }
                }
            `;
            document.head.appendChild(style);
        }
        
        document.body.appendChild(this.overlay);
    }
    
    show(id = 'default') {
        this.activeLoaders.add(id);
        this.updateVisibility();
        return id;
    }
    
    hide(id = 'default') {
        this.activeLoaders.delete(id);
        this.updateVisibility();
    }
    
    updateVisibility() {
        if (this.activeLoaders.size > 0) {
            this.overlay.style.opacity = '1';
            this.overlay.style.visibility = 'visible';
            document.body.style.overflow = 'hidden';
        } else {
            this.overlay.style.opacity = '0';
            this.overlay.style.visibility = 'hidden';
            document.body.style.overflow = '';
        }
    }
    
    hideAll() {
        this.activeLoaders.clear();
        this.updateVisibility();
    }
}

/**
 * ========================================
 * INSTANCIAS GLOBALES
 * ========================================
 */
let ASG_SIDEBAR = null;
let ASG_NOTIFICATIONS = null;
let ASG_LOADING = null;

/**
 * ========================================
 * INICIALIZACIÓN AUTOMÁTICA
 * ========================================
 */
document.addEventListener('DOMContentLoaded', function() {
    // Inicializar componentes globales
    ASG_NOTIFICATIONS = new ASGNotifications();
    ASG_LOADING = new ASGLoading();
    
    // Inicializar sidebar si existe
    if (document.querySelector('.asg-sidebar')) {
        ASG_SIDEBAR = new ASGSidebar();
    }
    
    console.log('🚀 ASG Components v2.0 inicializados');
});

/**
 * ========================================
 * UTILIDADES GLOBALES
 * ========================================
 */
window.ASG = {
    // Referencias a componentes
    sidebar: () => ASG_SIDEBAR,
    notifications: () => ASG_NOTIFICATIONS,
    loading: () => ASG_LOADING,
    
    // Configuración
    config: ASG_CONFIG,
    
    // Utilidades
    utils: {
        formatCurrency: (amount) => {
            return new Intl.NumberFormat('es-ES', {
                style: 'currency',
                currency: 'EUR'
            }).format(amount);
        },
        
        formatDate: (date) => {
            return new Intl.DateTimeFormat('es-ES', {
                year: 'numeric',
                month: 'long',
                day: 'numeric'
            }).format(new Date(date));
        },
        
        debounce: (func, wait) => {
            let timeout;
            return function executedFunction(...args) {
                const later = () => {
                    clearTimeout(timeout);
                    func(...args);
                };
                clearTimeout(timeout);
                timeout = setTimeout(later, wait);
            };
        },
        
        throttle: (func, limit) => {
            let inThrottle;
            return function() {
                const args = arguments;
                const context = this;
                if (!inThrottle) {
                    func.apply(context, args);
                    inThrottle = true;
                    setTimeout(() => inThrottle = false, limit);
                }
            };
        },

        // Función para crear elementos DOM de forma segura
        createElement: (tag, attributes = {}, children = []) => {
            const element = document.createElement(tag);

            // Aplicar atributos
            Object.entries(attributes).forEach(([key, value]) => {
                if (key === 'className') {
                    element.className = value;
                } else if (key === 'innerHTML') {
                    element.innerHTML = value;
                } else if (key === 'textContent') {
                    element.textContent = value;
                } else if (key.startsWith('data-')) {
                    element.setAttribute(key, value);
                } else {
                    element[key] = value;
                }
            });

            // Agregar hijos
            children.forEach(child => {
                if (typeof child === 'string') {
                    element.appendChild(document.createTextNode(child));
                } else if (child instanceof Node) {
                    element.appendChild(child);
                }
            });

            return element;
        },

        // Función para sanitizar HTML
        sanitizeHtml: (html) => {
            const div = document.createElement('div');
            div.textContent = html;
            return div.innerHTML;
        }
    }
};
