<?php
/**
 * ========================================
 * ASG DASHBOARD COMPLETO - SNIPPET 2
 * ========================================
 * 
 * Descripción: Dashboard administrativo con métricas y gestión
 * Uso: Copiar y pegar en WP Code Snippets
 * Tipo: PHP Snippet
 * Ubicación: Solo en admin
 * 
 * Shortcode: [asg_dashboard]
 * 
 * Autor: ASG Team
 * Fecha: 2025-01-04
 * Versión: 2.0.0 - CODE SNIPPETS
 */

// Prevenir ejecución directa
if (!defined('ABSPATH')) {
    exit;
}

/**
 * ========================================
 * SHORTCODE DEL DASHBOARD
 * ========================================
 */
add_shortcode('asg_dashboard', 'asg_render_dashboard');

function asg_render_dashboard($atts) {
    // Solo para usuarios con permisos
    if (!current_user_can('manage_options') && !current_user_can('edit_posts')) {
        return '<p>No tienes permisos para acceder a esta sección.</p>';
    }
    
    ob_start();
    ?>
    
    <!-- CSS del Dashboard -->
    <style>
    .asg-dashboard {
        font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
        max-width: 1200px;
        margin: 0 auto;
        padding: 2rem 1rem;
    }
    
    .asg-dashboard-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 2rem;
        padding-bottom: 1rem;
        border-bottom: 1px solid var(--asg-gray-200);
    }
    
    .asg-dashboard-title {
        margin: 0;
        font-size: 2rem;
        font-weight: 600;
        color: var(--asg-gray-900);
    }
    
    .asg-dashboard-actions {
        display: flex;
        gap: 1rem;
    }
    
    .asg-stats-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
        gap: 1.5rem;
        margin-bottom: 2rem;
    }
    
    .asg-stat-card {
        background: white;
        border: 1px solid var(--asg-gray-200);
        border-radius: var(--asg-radius);
        padding: 1.5rem;
        box-shadow: var(--asg-shadow);
        transition: all var(--asg-transition);
    }
    
    .asg-stat-card:hover {
        box-shadow: 0 4px 6px -1px rgb(0 0 0 / 0.1);
        transform: translateY(-1px);
    }
    
    .asg-stat-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 1rem;
    }
    
    .asg-stat-icon {
        width: 3rem;
        height: 3rem;
        border-radius: 0.75rem;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 1.5rem;
    }
    
    .asg-stat-number {
        font-size: 2.5rem;
        font-weight: 700;
        color: var(--asg-gray-900);
        margin: 0.5rem 0;
    }
    
    .asg-stat-label {
        font-size: 0.875rem;
        color: var(--asg-gray-600);
        font-weight: 500;
        margin: 0;
    }
    
    .asg-stat-change {
        display: flex;
        align-items: center;
        gap: 0.5rem;
        margin-top: 1rem;
        font-size: 0.875rem;
    }
    
    .asg-recent-courses {
        background: white;
        border: 1px solid var(--asg-gray-200);
        border-radius: var(--asg-radius);
        box-shadow: var(--asg-shadow);
    }
    
    .asg-recent-header {
        padding: 1.5rem;
        border-bottom: 1px solid var(--asg-gray-200);
        display: flex;
        justify-content: space-between;
        align-items: center;
    }
    
    .asg-recent-title {
        margin: 0;
        font-size: 1.25rem;
        font-weight: 600;
        color: var(--asg-gray-900);
    }
    
    .asg-course-item {
        padding: 1rem 1.5rem;
        border-bottom: 1px solid var(--asg-gray-100);
        display: flex;
        align-items: center;
        gap: 1rem;
        transition: background-color var(--asg-transition);
    }
    
    .asg-course-item:hover {
        background-color: var(--asg-gray-50);
    }
    
    .asg-course-item:last-child {
        border-bottom: none;
    }
    
    .asg-course-image {
        width: 4rem;
        height: 3rem;
        background: var(--asg-gray-200);
        border-radius: var(--asg-radius);
        background-size: cover;
        background-position: center;
        flex-shrink: 0;
    }
    
    .asg-course-info {
        flex: 1;
        min-width: 0;
    }
    
    .asg-course-title {
        margin: 0 0 0.25rem 0;
        font-weight: 600;
        color: var(--asg-gray-900);
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
    }
    
    .asg-course-meta {
        margin: 0;
        font-size: 0.875rem;
        color: var(--asg-gray-600);
    }
    
    .asg-course-actions {
        display: flex;
        gap: 0.5rem;
    }
    
    .asg-loading {
        text-align: center;
        padding: 2rem;
        color: var(--asg-gray-600);
    }
    
    .asg-spinner {
        width: 2rem;
        height: 2rem;
        border: 2px solid var(--asg-gray-200);
        border-top: 2px solid var(--asg-primary);
        border-radius: 50%;
        animation: asg-spin 1s linear infinite;
        margin: 0 auto 1rem;
    }
    
    @keyframes asg-spin {
        0% { transform: rotate(0deg); }
        100% { transform: rotate(360deg); }
    }
    
    @media (max-width: 768px) {
        .asg-dashboard {
            padding: 1rem 0.5rem;
        }
        
        .asg-dashboard-header {
            flex-direction: column;
            gap: 1rem;
            align-items: stretch;
        }
        
        .asg-dashboard-actions {
            justify-content: center;
        }
        
        .asg-stats-grid {
            grid-template-columns: 1fr;
        }
        
        .asg-course-item {
            flex-direction: column;
            align-items: flex-start;
            gap: 0.75rem;
        }
        
        .asg-course-image {
            width: 100%;
            height: 8rem;
        }
    }
    </style>
    
    <!-- HTML del Dashboard -->
    <div class="asg-dashboard">
        <div class="asg-dashboard-header">
            <h1 class="asg-dashboard-title">📊 Dashboard ASG</h1>
            <div class="asg-dashboard-actions">
                <button class="asg-btn asg-btn-primary" onclick="asgRefreshDashboard()">
                    🔄 Actualizar
                </button>
                <a href="#" class="asg-btn asg-btn-primary" onclick="asgCreateCourse()">
                    ➕ Nuevo Curso
                </a>
            </div>
        </div>
        
        <!-- Estadísticas -->
        <div class="asg-stats-grid">
            <div class="asg-stat-card">
                <div class="asg-stat-header">
                    <div>
                        <p class="asg-stat-label">Total de Cursos</p>
                        <p class="asg-stat-number" id="totalCourses">--</p>
                    </div>
                    <div class="asg-stat-icon" style="background: #dbeafe; color: #2563eb;">
                        📚
                    </div>
                </div>
                <div class="asg-stat-change">
                    <span class="asg-badge asg-badge-success" id="coursesChange">+0%</span>
                    <span>vs mes anterior</span>
                </div>
            </div>
            
            <div class="asg-stat-card">
                <div class="asg-stat-header">
                    <div>
                        <p class="asg-stat-label">Cursos Publicados</p>
                        <p class="asg-stat-number" id="publishedCourses">--</p>
                    </div>
                    <div class="asg-stat-icon" style="background: #d1fae5; color: #10b981;">
                        ✅
                    </div>
                </div>
                <div class="asg-stat-change">
                    <span class="asg-badge asg-badge-success" id="publishedPercentage">0%</span>
                    <span>del total</span>
                </div>
            </div>
            
            <div class="asg-stat-card">
                <div class="asg-stat-header">
                    <div>
                        <p class="asg-stat-label">Borradores</p>
                        <p class="asg-stat-number" id="draftCourses">--</p>
                    </div>
                    <div class="asg-stat-icon" style="background: #fef3c7; color: #f59e0b;">
                        ✏️
                    </div>
                </div>
                <div class="asg-stat-change">
                    <span class="asg-badge asg-badge-warning" id="draftPercentage">0%</span>
                    <span>pendientes</span>
                </div>
            </div>
            
            <div class="asg-stat-card">
                <div class="asg-stat-header">
                    <div>
                        <p class="asg-stat-label">Ingresos Potenciales</p>
                        <p class="asg-stat-number" id="totalRevenue">--</p>
                    </div>
                    <div class="asg-stat-icon" style="background: #cffafe; color: #06b6d4;">
                        💰
                    </div>
                </div>
                <div class="asg-stat-change">
                    <span class="asg-badge asg-badge-gray" id="avgPrice">€0</span>
                    <span>precio promedio</span>
                </div>
            </div>
        </div>
        
        <!-- Cursos Recientes -->
        <div class="asg-recent-courses">
            <div class="asg-recent-header">
                <h2 class="asg-recent-title">Cursos Recientes</h2>
                <a href="#" class="asg-btn asg-btn-primary" onclick="asgViewAllCourses()">
                    Ver todos
                </a>
            </div>
            <div id="recentCourses">
                <div class="asg-loading">
                    <div class="asg-spinner"></div>
                    <p>Cargando cursos...</p>
                </div>
            </div>
        </div>
    </div>
    
    <!-- JavaScript del Dashboard -->
    <script>
    // Variables globales
    let asgDashboardData = {
        stats: {},
        courses: []
    };
    
    // Inicializar dashboard
    document.addEventListener('DOMContentLoaded', function() {
        if (typeof window.ASG_CONFIG !== 'undefined') {
            asgLoadDashboardData();
        } else {
            setTimeout(asgLoadDashboardData, 500);
        }
    });
    
    // Cargar datos del dashboard
    async function asgLoadDashboardData() {
        try {
            // Cargar estadísticas
            const statsResponse = await fetch(window.ASG_CONFIG.apiUrl + '/admin/dashboard/stats', {
                headers: {
                    'X-WP-Nonce': window.ASG_CONFIG.nonce
                }
            });
            
            if (statsResponse.ok) {
                const statsData = await statsResponse.json();
                asgUpdateStats(statsData.data);
            } else {
                asgUseMockStats();
            }
            
            // Cargar cursos recientes
            const coursesResponse = await fetch(window.ASG_CONFIG.apiUrl + '/admin/courses?per_page=5', {
                headers: {
                    'X-WP-Nonce': window.ASG_CONFIG.nonce
                }
            });
            
            if (coursesResponse.ok) {
                const coursesData = await coursesResponse.json();
                asgUpdateRecentCourses(coursesData.data);
            } else {
                asgUseMockCourses();
            }
            
        } catch (error) {
            console.warn('API no disponible, usando datos simulados:', error);
            asgUseMockStats();
            asgUseMockCourses();
        }
    }
    
    // Actualizar estadísticas
    function asgUpdateStats(stats) {
        asgDashboardData.stats = stats;
        
        // Animar números
        asgAnimateNumber('totalCourses', stats.totalCourses);
        asgAnimateNumber('publishedCourses', stats.publishedCourses);
        asgAnimateNumber('draftCourses', stats.draftCourses);
        
        // Formatear ingresos
        document.getElementById('totalRevenue').textContent = asgFormatCurrency(stats.totalRevenue);
        document.getElementById('avgPrice').textContent = asgFormatCurrency(stats.avgPrice);
        
        // Calcular porcentajes
        const publishedPercentage = stats.totalCourses > 0 ? 
            Math.round((stats.publishedCourses / stats.totalCourses) * 100) : 0;
        const draftPercentage = stats.totalCourses > 0 ? 
            Math.round((stats.draftCourses / stats.totalCourses) * 100) : 0;
        
        document.getElementById('publishedPercentage').textContent = publishedPercentage + '%';
        document.getElementById('draftPercentage').textContent = draftPercentage + '%';
        
        // Cambio vs mes anterior
        const changeEl = document.getElementById('coursesChange');
        const change = stats.coursesChange || 0;
        changeEl.textContent = (change > 0 ? '+' : '') + change + '%';
        changeEl.className = change > 0 ? 'asg-badge asg-badge-success' : 
                           change < 0 ? 'asg-badge asg-badge-error' : 'asg-badge asg-badge-gray';
    }
    
    // Actualizar cursos recientes
    function asgUpdateRecentCourses(courses) {
        asgDashboardData.courses = courses;
        const container = document.getElementById('recentCourses');
        
        if (!courses || courses.length === 0) {
            container.innerHTML = `
                <div class="asg-loading">
                    <p>📚 No hay cursos recientes</p>
                    <button class="asg-btn asg-btn-primary" onclick="asgCreateCourse()">
                        Crear primer curso
                    </button>
                </div>
            `;
            return;
        }
        
        container.innerHTML = courses.map(course => `
            <div class="asg-course-item">
                <div class="asg-course-image" style="background-image: url('${course.cover_img || ''}')"></div>
                <div class="asg-course-info">
                    <h3 class="asg-course-title">${course.name_course}</h3>
                    <p class="asg-course-meta">
                        ${asgGetStatusBadge(course.status_course)} • 
                        ${course.module_count || 0} módulos • 
                        ${asgFormatCurrency(course.price_course)}
                    </p>
                </div>
                <div class="asg-course-actions">
                    <button class="asg-btn asg-btn-primary" onclick="asgEditCourse('${course.code_course}')">
                        ✏️ Editar
                    </button>
                </div>
            </div>
        `).join('');
    }
    
    // Datos simulados para desarrollo
    function asgUseMockStats() {
        asgUpdateStats({
            totalCourses: 12,
            publishedCourses: 8,
            draftCourses: 4,
            totalRevenue: 2399.92,
            avgPrice: 199.99,
            coursesChange: 25
        });
    }
    
    function asgUseMockCourses() {
        asgUpdateRecentCourses([
            {
                code_course: 'course_1',
                name_course: 'Como hacerte millonario?',
                cover_img: 'https://images.unsplash.com/photo-1579621970563-ebec7560ff3e?w=400&h=300&fit=crop',
                status_course: 'published',
                price_course: 299.99,
                module_count: 8
            },
            {
                code_course: 'course_2',
                name_course: 'Marketing Digital Avanzado',
                cover_img: 'https://images.unsplash.com/photo-1460925895917-afdab827c52f?w=400&h=300&fit=crop',
                status_course: 'published',
                price_course: 199.99,
                module_count: 6
            }
        ]);
    }
    
    // Utilidades
    function asgAnimateNumber(elementId, targetValue) {
        const element = document.getElementById(elementId);
        if (!element) return;
        
        const startValue = 0;
        const duration = 1000;
        const startTime = performance.now();
        
        function animate(currentTime) {
            const elapsed = currentTime - startTime;
            const progress = Math.min(elapsed / duration, 1);
            const currentValue = startValue + (targetValue - startValue) * progress;
            
            element.textContent = Math.round(currentValue);
            
            if (progress < 1) {
                requestAnimationFrame(animate);
            }
        }
        
        requestAnimationFrame(animate);
    }
    
    function asgFormatCurrency(amount) {
        return new Intl.NumberFormat('es-ES', {
            style: 'currency',
            currency: 'EUR'
        }).format(amount || 0);
    }
    
    function asgGetStatusBadge(status) {
        const badges = {
            published: '<span class="asg-badge asg-badge-success">Publicado</span>',
            draft: '<span class="asg-badge asg-badge-warning">Borrador</span>',
            archived: '<span class="asg-badge asg-badge-gray">Archivado</span>'
        };
        return badges[status] || badges.draft;
    }
    
    // Acciones del dashboard
    function asgRefreshDashboard() {
        asgLoadDashboardData();
        alert('🔄 Dashboard actualizado');
    }
    
    function asgCreateCourse() {
        // Redirigir a página de creación o mostrar modal
        if (confirm('¿Quieres crear un nuevo curso?')) {
            window.location.href = '/wp-admin/admin.php?page=asg-new-course';
        }
    }
    
    function asgViewAllCourses() {
        window.location.href = '/wp-admin/admin.php?page=asg-all-courses';
    }
    
    function asgEditCourse(courseCode) {
        window.location.href = '/wp-admin/admin.php?page=asg-edit-course&course=' + courseCode;
    }
    </script>
    
    <?php
    return ob_get_clean();
}

/**
 * ========================================
 * PÁGINA DE ADMINISTRACIÓN
 * ========================================
 */
add_action('admin_menu', function() {
    add_menu_page(
        'ASG Cursos',
        'ASG Cursos',
        'manage_options',
        'asg-dashboard',
        'asg_admin_dashboard_page',
        'dashicons-book-alt',
        30
    );
});

function asg_admin_dashboard_page() {
    echo '<div class="wrap">';
    echo do_shortcode('[asg_dashboard]');
    echo '</div>';
}
?>
