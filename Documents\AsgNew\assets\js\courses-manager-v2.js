/**
 * ========================================
 * COURSES MANAGER v2.0 - GESTIÓN COMPLETA
 * ========================================
 * 
 * Descripción: Gestión avanzada de cursos con filtros, búsqueda y vistas
 * Incluye: Grid/List view, filtros en tiempo real, paginación, CRUD
 * 
 * Autor: ASG Team
 * Fecha: 2025-01-04
 * Versión: 2.0.0 - GESTIÓN MODERNA
 */

/**
 * ========================================
 * CLASE PRINCIPAL DE GESTIÓN DE CURSOS
 * ========================================
 */
class ASGCoursesManager {
    constructor() {
        this.apiUrl = ASG.config.apiBaseUrl;
        this.courses = [];
        this.filteredCourses = [];
        this.currentView = 'grid'; // 'grid' o 'list'
        this.currentPage = 1;
        this.coursesPerPage = 12;
        this.totalPages = 1;
        
        // Filtros
        this.filters = {
            search: '',
            status: '',
            category: ''
        };
        
        // Elementos DOM
        this.elements = {};
        
        this.init();
    }
    
    init() {
        console.log('📚 Inicializando Courses Manager v2.0...');
        
        this.cacheElements();
        this.setupEventListeners();
        this.loadCourses();
        
        console.log('✅ Courses Manager inicializado correctamente');
    }
    
    cacheElements() {
        this.elements = {
            // Containers
            coursesContainer: document.getElementById('coursesContainer'),
            coursesGrid: document.getElementById('coursesGrid'),
            coursesList: document.getElementById('coursesList'),
            loadingState: document.getElementById('loadingState'),
            emptyState: document.getElementById('emptyState'),
            errorState: document.getElementById('errorState'),
            
            // Filters
            searchInput: document.getElementById('searchInput'),
            statusFilter: document.getElementById('statusFilter'),
            categoryFilter: document.getElementById('categoryFilter'),
            activeFilters: document.getElementById('activeFilters'),
            filterTags: document.getElementById('filterTags'),
            clearFilters: document.getElementById('clearFilters'),
            
            // View controls
            viewButtons: document.querySelectorAll('.view-btn'),
            
            // Pagination
            pagination: document.getElementById('pagination'),
            prevPage: document.getElementById('prevPage'),
            nextPage: document.getElementById('nextPage'),
            pageNumbers: document.getElementById('pageNumbers'),
            paginationInfo: document.getElementById('paginationInfo'),
            
            // Stats
            coursesCount: document.getElementById('coursesCount'),
            refreshBtn: document.getElementById('refreshBtn')
        };
    }
    
    setupEventListeners() {
        // Búsqueda en tiempo real con debounce
        if (this.elements.searchInput) {
            this.elements.searchInput.addEventListener('input', 
                ASG.utils.debounce((e) => {
                    this.filters.search = e.target.value.trim();
                    this.applyFilters();
                }, 300)
            );
        }
        
        // Filtros de estado y categoría
        if (this.elements.statusFilter) {
            this.elements.statusFilter.addEventListener('change', (e) => {
                this.filters.status = e.target.value;
                this.applyFilters();
            });
        }
        
        if (this.elements.categoryFilter) {
            this.elements.categoryFilter.addEventListener('change', (e) => {
                this.filters.category = e.target.value;
                this.applyFilters();
            });
        }
        
        // Limpiar filtros
        if (this.elements.clearFilters) {
            this.elements.clearFilters.addEventListener('click', () => {
                this.clearAllFilters();
            });
        }
        
        // Cambio de vista
        this.elements.viewButtons.forEach(btn => {
            btn.addEventListener('click', (e) => {
                const view = e.currentTarget.dataset.view;
                this.changeView(view);
            });
        });
        
        // Paginación
        if (this.elements.prevPage) {
            this.elements.prevPage.addEventListener('click', () => {
                if (this.currentPage > 1) {
                    this.goToPage(this.currentPage - 1);
                }
            });
        }
        
        if (this.elements.nextPage) {
            this.elements.nextPage.addEventListener('click', () => {
                if (this.currentPage < this.totalPages) {
                    this.goToPage(this.currentPage + 1);
                }
            });
        }
        
        // Actualizar
        if (this.elements.refreshBtn) {
            this.elements.refreshBtn.addEventListener('click', () => {
                this.refreshCourses();
            });
        }
    }
    
    async loadCourses() {
        try {
            this.showLoadingState();
            
            // Intentar cargar desde API real
            const response = await fetch(`${this.apiUrl}/admin/courses?per_page=100`);
            
            let coursesData;
            if (response.ok) {
                const data = await response.json();
                coursesData = data.data || [];
            } else {
                throw new Error('API no disponible');
            }
            
            this.courses = coursesData;
            this.applyFilters();
            
            ASG.notifications().success(`${this.courses.length} cursos cargados correctamente`);
            
        } catch (error) {
            console.warn('API no disponible, usando datos simulados:', error);
            this.courses = this.generateMockCourses();
            this.applyFilters();
            
            ASG.notifications().info('Mostrando datos de ejemplo (API no disponible)');
        }
    }
    
    generateMockCourses() {
        return [
            {
                id_course: 1,
                code_course: 'course_1',
                name_course: 'Como hacerte millonario?',
                description_course: 'Aprende las estrategias más efectivas para generar riqueza y alcanzar la libertad financiera. Este curso te enseñará desde los fundamentos básicos hasta técnicas avanzadas.',
                cover_img: 'https://images.unsplash.com/photo-1579621970563-ebec7560ff3e?w=400&h=300&fit=crop',
                price_course: 299.99,
                status_course: 'published',
                category_course: 'finanzas',
                created_at: '2024-12-15 10:30:00',
                updated_at: '2024-12-20 15:45:00',
                module_count: 8,
                lesson_count: 24,
                enrollment_count: 156,
                rating_average: 4.8
            },
            {
                id_course: 2,
                code_course: 'course_2',
                name_course: 'Marketing Digital Avanzado',
                description_course: 'Domina las técnicas más avanzadas del marketing digital y haz crecer tu negocio online. Incluye SEO, SEM, redes sociales y email marketing.',
                cover_img: 'https://images.unsplash.com/photo-1460925895917-afdab827c52f?w=400&h=300&fit=crop',
                price_course: 199.99,
                status_course: 'published',
                category_course: 'marketing',
                created_at: '2024-12-10 14:20:00',
                updated_at: '2024-12-18 09:30:00',
                module_count: 6,
                lesson_count: 18,
                enrollment_count: 89,
                rating_average: 4.6
            },
            {
                id_course: 3,
                code_course: 'course_3',
                name_course: 'Desarrollo Personal y Liderazgo',
                description_course: 'Desarrolla tus habilidades de liderazgo y crecimiento personal para alcanzar el éxito profesional y personal.',
                cover_img: 'https://images.unsplash.com/photo-1552664730-d307ca884978?w=400&h=300&fit=crop',
                price_course: 149.99,
                status_course: 'draft',
                category_course: 'desarrollo-personal',
                created_at: '2024-12-08 09:15:00',
                updated_at: '2024-12-08 09:15:00',
                module_count: 5,
                lesson_count: 15,
                enrollment_count: 0,
                rating_average: 0
            },
            {
                id_course: 4,
                code_course: 'course_4',
                name_course: 'Programación Web Completa',
                description_course: 'Aprende desarrollo web desde cero: HTML, CSS, JavaScript, React y Node.js. Conviértete en desarrollador full-stack.',
                cover_img: 'https://images.unsplash.com/photo-1516321318423-f06f85e504b3?w=400&h=300&fit=crop',
                price_course: 399.99,
                status_course: 'published',
                category_course: 'tecnologia',
                created_at: '2024-12-05 16:45:00',
                updated_at: '2024-12-22 11:20:00',
                module_count: 12,
                lesson_count: 45,
                enrollment_count: 234,
                rating_average: 4.9
            },
            {
                id_course: 5,
                code_course: 'course_5',
                name_course: 'Gestión de Negocios Modernos',
                description_course: 'Estrategias modernas para gestionar y hacer crecer tu negocio en la era digital.',
                cover_img: 'https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=400&h=300&fit=crop',
                price_course: 249.99,
                status_course: 'draft',
                category_course: 'negocios',
                created_at: '2024-12-01 12:00:00',
                updated_at: '2024-12-15 14:30:00',
                module_count: 7,
                lesson_count: 21,
                enrollment_count: 0,
                rating_average: 0
            },
            {
                id_course: 6,
                code_course: 'course_6',
                name_course: 'Inversiones y Trading',
                description_course: 'Aprende a invertir en bolsa y hacer trading de forma profesional y segura.',
                cover_img: 'https://images.unsplash.com/photo-1611974789855-9c2a0a7236a3?w=400&h=300&fit=crop',
                price_course: 349.99,
                status_course: 'published',
                category_course: 'finanzas',
                created_at: '2024-11-28 08:30:00',
                updated_at: '2024-12-10 16:45:00',
                module_count: 10,
                lesson_count: 32,
                enrollment_count: 78,
                rating_average: 4.7
            }
        ];
    }
    
    applyFilters() {
        let filtered = [...this.courses];
        
        // Filtro de búsqueda
        if (this.filters.search) {
            const searchTerm = this.filters.search.toLowerCase();
            filtered = filtered.filter(course => 
                course.name_course.toLowerCase().includes(searchTerm) ||
                course.description_course.toLowerCase().includes(searchTerm) ||
                course.category_course.toLowerCase().includes(searchTerm)
            );
        }
        
        // Filtro de estado
        if (this.filters.status) {
            filtered = filtered.filter(course => course.status_course === this.filters.status);
        }
        
        // Filtro de categoría
        if (this.filters.category) {
            filtered = filtered.filter(course => course.category_course === this.filters.category);
        }
        
        this.filteredCourses = filtered;
        this.updateFilterTags();
        this.updatePagination();
        this.renderCourses();
        this.updateStats();
    }
    
    updateFilterTags() {
        const tags = [];
        
        if (this.filters.search) {
            tags.push({ type: 'search', label: `Búsqueda: "${this.filters.search}"`, value: this.filters.search });
        }
        
        if (this.filters.status) {
            const statusLabels = {
                published: 'Publicados',
                draft: 'Borradores',
                archived: 'Archivados'
            };
            tags.push({ type: 'status', label: `Estado: ${statusLabels[this.filters.status]}`, value: this.filters.status });
        }
        
        if (this.filters.category) {
            const categoryLabels = {
                finanzas: 'Finanzas',
                marketing: 'Marketing',
                'desarrollo-personal': 'Desarrollo Personal',
                tecnologia: 'Tecnología',
                negocios: 'Negocios'
            };
            tags.push({ type: 'category', label: `Categoría: ${categoryLabels[this.filters.category]}`, value: this.filters.category });
        }
        
        if (tags.length > 0) {
            this.elements.activeFilters.style.display = 'block';
            this.elements.filterTags.innerHTML = tags.map(tag => `
                <span class="filter-tag" style="
                    display: inline-flex;
                    align-items: center;
                    gap: var(--asg-space-1);
                    padding: var(--asg-space-1) var(--asg-space-2);
                    background: var(--asg-primary-100);
                    color: var(--asg-primary-700);
                    border-radius: var(--asg-radius);
                    font-size: var(--asg-text-xs);
                    font-weight: var(--asg-font-medium);
                ">
                    ${tag.label}
                    <button onclick="coursesManager.removeFilter('${tag.type}')" style="
                        background: none;
                        border: none;
                        color: var(--asg-primary-700);
                        cursor: pointer;
                        padding: 0;
                        margin-left: var(--asg-space-1);
                        font-size: 1rem;
                        line-height: 1;
                    ">&times;</button>
                </span>
            `).join('');
        } else {
            this.elements.activeFilters.style.display = 'none';
        }
    }
    
    removeFilter(type) {
        switch (type) {
            case 'search':
                this.filters.search = '';
                this.elements.searchInput.value = '';
                break;
            case 'status':
                this.filters.status = '';
                this.elements.statusFilter.value = '';
                break;
            case 'category':
                this.filters.category = '';
                this.elements.categoryFilter.value = '';
                break;
        }
        
        this.applyFilters();
    }
    
    clearAllFilters() {
        this.filters = { search: '', status: '', category: '' };
        this.elements.searchInput.value = '';
        this.elements.statusFilter.value = '';
        this.elements.categoryFilter.value = '';
        this.applyFilters();
    }
    
    updatePagination() {
        this.totalPages = Math.ceil(this.filteredCourses.length / this.coursesPerPage);
        
        if (this.currentPage > this.totalPages) {
            this.currentPage = Math.max(1, this.totalPages);
        }
    }
    
    renderCourses() {
        this.hideAllStates();
        
        if (this.filteredCourses.length === 0) {
            this.showEmptyState();
            return;
        }
        
        const startIndex = (this.currentPage - 1) * this.coursesPerPage;
        const endIndex = startIndex + this.coursesPerPage;
        const coursesToShow = this.filteredCourses.slice(startIndex, endIndex);
        
        if (this.currentView === 'grid') {
            this.renderGridView(coursesToShow);
        } else {
            this.renderListView(coursesToShow);
        }
        
        this.renderPagination();
    }
    
    renderGridView(courses) {
        this.elements.coursesGrid.style.display = 'grid';
        this.elements.coursesList.style.display = 'none';
        
        this.elements.coursesGrid.innerHTML = courses.map(course => this.createCourseCard(course)).join('');
    }
    
    renderListView(courses) {
        this.elements.coursesGrid.style.display = 'none';
        this.elements.coursesList.style.display = 'flex';

        this.elements.coursesList.innerHTML = courses.map(course => this.createCourseListItem(course)).join('');
    }

    createCourseCard(course) {
        const statusBadge = this.getStatusBadge(course.status_course);
        const formattedDate = ASG.utils.formatDate(course.created_at);
        const editUrl = `https://abilityseminarsgroup.com/edit-course/?id=${course.code_course}`;
        const rating = this.renderRating(course.rating_average);

        return `
            <div class="asg-card course-card" onclick="window.location.href='${editUrl}'" style="cursor: pointer;">
                <div style="
                    height: 200px;
                    background-image: url('${course.cover_img || 'https://images.unsplash.com/photo-1516321318423-f06f85e504b3?w=400&h=300&fit=crop'}');
                    background-size: cover;
                    background-position: center;
                    position: relative;
                ">
                    <div style="
                        position: absolute;
                        top: var(--asg-space-3);
                        right: var(--asg-space-3);
                    ">
                        ${statusBadge}
                    </div>

                    <div style="
                        position: absolute;
                        bottom: var(--asg-space-3);
                        left: var(--asg-space-3);
                        background: rgba(0, 0, 0, 0.7);
                        color: white;
                        padding: var(--asg-space-1) var(--asg-space-2);
                        border-radius: var(--asg-radius);
                        font-size: var(--asg-text-xs);
                        font-weight: var(--asg-font-medium);
                    ">
                        ${course.module_count || 0} módulos • ${course.lesson_count || 0} lecciones
                    </div>
                </div>

                <div class="asg-card-body">
                    <div style="margin-bottom: var(--asg-space-3);">
                        <h3 style="
                            margin: 0 0 var(--asg-space-2) 0;
                            font-size: var(--asg-text-lg);
                            font-weight: var(--asg-font-semibold);
                            color: var(--asg-gray-900);
                            line-height: 1.4;
                            display: -webkit-box;
                            -webkit-line-clamp: 2;
                            -webkit-box-orient: vertical;
                            overflow: hidden;
                        ">${course.name_course}</h3>

                        <p style="
                            margin: 0;
                            font-size: var(--asg-text-sm);
                            color: var(--asg-gray-600);
                            line-height: 1.5;
                            display: -webkit-box;
                            -webkit-line-clamp: 3;
                            -webkit-box-orient: vertical;
                            overflow: hidden;
                        ">${course.description_course}</p>
                    </div>

                    <div style="
                        display: flex;
                        align-items: center;
                        justify-content: space-between;
                        margin-bottom: var(--asg-space-3);
                    ">
                        <div style="
                            display: flex;
                            align-items: center;
                            gap: var(--asg-space-2);
                            font-size: var(--asg-text-xs);
                            color: var(--asg-gray-500);
                        ">
                            <span><i class="bi bi-calendar"></i> ${formattedDate}</span>
                        </div>

                        <div style="
                            font-size: var(--asg-text-xl);
                            font-weight: var(--asg-font-bold);
                            color: var(--asg-primary-600);
                        ">
                            ${ASG.utils.formatCurrency(course.price_course)}
                        </div>
                    </div>

                    ${course.status_course === 'published' ? `
                        <div style="
                            display: flex;
                            align-items: center;
                            justify-content: space-between;
                            padding-top: var(--asg-space-3);
                            border-top: 1px solid var(--asg-gray-200);
                            font-size: var(--asg-text-sm);
                        ">
                            <div style="display: flex; align-items: center; gap: var(--asg-space-2);">
                                ${rating}
                                <span style="color: var(--asg-gray-600);">(${course.rating_count || 0})</span>
                            </div>

                            <div style="color: var(--asg-gray-600);">
                                <i class="bi bi-people"></i> ${course.enrollment_count || 0} estudiantes
                            </div>
                        </div>
                    ` : ''}
                </div>

                <div class="asg-card-footer">
                    <div style="display: flex; gap: var(--asg-space-2);">
                        <button class="asg-btn asg-btn-primary asg-btn-sm" onclick="event.stopPropagation(); window.location.href='${editUrl}'" style="flex: 1;">
                            <i class="bi bi-pencil"></i>
                            Editar
                        </button>

                        <button class="asg-btn asg-btn-secondary asg-btn-sm" onclick="event.stopPropagation(); coursesManager.duplicateCourse('${course.code_course}')">
                            <i class="bi bi-files"></i>
                            Duplicar
                        </button>

                        <button class="asg-btn asg-btn-secondary asg-btn-sm" onclick="event.stopPropagation(); coursesManager.showCourseActions('${course.code_course}')" style="padding: var(--asg-space-2);">
                            <i class="bi bi-three-dots-vertical"></i>
                        </button>
                    </div>
                </div>
            </div>
        `;
    }

    createCourseListItem(course) {
        const statusBadge = this.getStatusBadge(course.status_course);
        const formattedDate = ASG.utils.formatDate(course.created_at);
        const editUrl = `https://abilityseminarsgroup.com/edit-course/?id=${course.code_course}`;
        const rating = this.renderRating(course.rating_average);

        return `
            <div class="asg-card course-card" onclick="window.location.href='${editUrl}'" style="cursor: pointer;">
                <div class="asg-card-body">
                    <div style="display: flex; align-items: center; gap: var(--asg-space-4);">
                        <div style="
                            width: 120px;
                            height: 80px;
                            background-image: url('${course.cover_img || 'https://images.unsplash.com/photo-1516321318423-f06f85e504b3?w=400&h=300&fit=crop'}');
                            background-size: cover;
                            background-position: center;
                            border-radius: var(--asg-radius);
                            flex-shrink: 0;
                        "></div>

                        <div style="flex: 1; min-width: 0;">
                            <div style="display: flex; align-items: center; gap: var(--asg-space-2); margin-bottom: var(--asg-space-2);">
                                <h3 style="
                                    margin: 0;
                                    font-size: var(--asg-text-lg);
                                    font-weight: var(--asg-font-semibold);
                                    color: var(--asg-gray-900);
                                    overflow: hidden;
                                    text-overflow: ellipsis;
                                    white-space: nowrap;
                                ">${course.name_course}</h3>
                                ${statusBadge}
                            </div>

                            <p style="
                                margin: 0 0 var(--asg-space-2) 0;
                                font-size: var(--asg-text-sm);
                                color: var(--asg-gray-600);
                                line-height: 1.5;
                                display: -webkit-box;
                                -webkit-line-clamp: 2;
                                -webkit-box-orient: vertical;
                                overflow: hidden;
                            ">${course.description_course}</p>

                            <div style="
                                display: flex;
                                align-items: center;
                                gap: var(--asg-space-4);
                                font-size: var(--asg-text-xs);
                                color: var(--asg-gray-500);
                            ">
                                <span><i class="bi bi-calendar"></i> ${formattedDate}</span>
                                <span><i class="bi bi-collection"></i> ${course.module_count || 0} módulos</span>
                                <span><i class="bi bi-play-circle"></i> ${course.lesson_count || 0} lecciones</span>
                                ${course.status_course === 'published' ? `
                                    <span><i class="bi bi-people"></i> ${course.enrollment_count || 0} estudiantes</span>
                                ` : ''}
                            </div>
                        </div>

                        <div style="display: flex; flex-direction: column; align-items: flex-end; gap: var(--asg-space-2); flex-shrink: 0;">
                            <div style="
                                font-size: var(--asg-text-xl);
                                font-weight: var(--asg-font-bold);
                                color: var(--asg-primary-600);
                            ">
                                ${ASG.utils.formatCurrency(course.price_course)}
                            </div>

                            ${course.status_course === 'published' ? `
                                <div style="display: flex; align-items: center; gap: var(--asg-space-1);">
                                    ${rating}
                                    <span style="font-size: var(--asg-text-xs); color: var(--asg-gray-600);">(${course.rating_count || 0})</span>
                                </div>
                            ` : ''}

                            <div style="display: flex; gap: var(--asg-space-2);">
                                <button class="asg-btn asg-btn-primary asg-btn-sm" onclick="event.stopPropagation(); window.location.href='${editUrl}'">
                                    <i class="bi bi-pencil"></i>
                                    Editar
                                </button>

                                <button class="asg-btn asg-btn-secondary asg-btn-sm" onclick="event.stopPropagation(); coursesManager.showCourseActions('${course.code_course}')" style="padding: var(--asg-space-2);">
                                    <i class="bi bi-three-dots-vertical"></i>
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        `;
    }

    getStatusBadge(status) {
        const badges = {
            published: '<span class="asg-badge asg-badge-success">Publicado</span>',
            draft: '<span class="asg-badge asg-badge-warning">Borrador</span>',
            archived: '<span class="asg-badge asg-badge-gray">Archivado</span>'
        };

        return badges[status] || badges.draft;
    }

    renderRating(rating) {
        if (!rating || rating === 0) {
            return '<span style="color: var(--asg-gray-400);">Sin calificar</span>';
        }

        const fullStars = Math.floor(rating);
        const hasHalfStar = rating % 1 >= 0.5;
        const emptyStars = 5 - fullStars - (hasHalfStar ? 1 : 0);

        let stars = '';

        // Estrellas llenas
        for (let i = 0; i < fullStars; i++) {
            stars += '<i class="bi bi-star-fill" style="color: var(--asg-warning);"></i>';
        }

        // Media estrella
        if (hasHalfStar) {
            stars += '<i class="bi bi-star-half" style="color: var(--asg-warning);"></i>';
        }

        // Estrellas vacías
        for (let i = 0; i < emptyStars; i++) {
            stars += '<i class="bi bi-star" style="color: var(--asg-gray-300);"></i>';
        }

        return `<div style="display: flex; align-items: center; gap: var(--asg-space-1);">${stars} <span style="font-size: var(--asg-text-sm); color: var(--asg-gray-600);">${rating.toFixed(1)}</span></div>`;
    }

    renderPagination() {
        if (this.totalPages <= 1) {
            this.elements.pagination.style.display = 'none';
            return;
        }

        this.elements.pagination.style.display = 'block';

        // Actualizar botones prev/next
        this.elements.prevPage.disabled = this.currentPage === 1;
        this.elements.nextPage.disabled = this.currentPage === this.totalPages;

        // Generar números de página
        const pageNumbers = [];
        const maxVisiblePages = 5;
        let startPage = Math.max(1, this.currentPage - Math.floor(maxVisiblePages / 2));
        let endPage = Math.min(this.totalPages, startPage + maxVisiblePages - 1);

        // Ajustar si estamos cerca del final
        if (endPage - startPage + 1 < maxVisiblePages) {
            startPage = Math.max(1, endPage - maxVisiblePages + 1);
        }

        // Botón primera página
        if (startPage > 1) {
            pageNumbers.push(`
                <button class="asg-btn asg-btn-sm asg-btn-secondary" onclick="coursesManager.goToPage(1)">1</button>
            `);
            if (startPage > 2) {
                pageNumbers.push('<span style="padding: 0 var(--asg-space-2);">...</span>');
            }
        }

        // Páginas visibles
        for (let i = startPage; i <= endPage; i++) {
            const isActive = i === this.currentPage;
            pageNumbers.push(`
                <button class="asg-btn asg-btn-sm ${isActive ? 'asg-btn-primary' : 'asg-btn-secondary'}"
                        onclick="coursesManager.goToPage(${i})"
                        ${isActive ? 'disabled' : ''}>
                    ${i}
                </button>
            `);
        }

        // Botón última página
        if (endPage < this.totalPages) {
            if (endPage < this.totalPages - 1) {
                pageNumbers.push('<span style="padding: 0 var(--asg-space-2);">...</span>');
            }
            pageNumbers.push(`
                <button class="asg-btn asg-btn-sm asg-btn-secondary" onclick="coursesManager.goToPage(${this.totalPages})">${this.totalPages}</button>
            `);
        }

        this.elements.pageNumbers.innerHTML = pageNumbers.join('');

        // Actualizar información de paginación
        const startItem = (this.currentPage - 1) * this.coursesPerPage + 1;
        const endItem = Math.min(this.currentPage * this.coursesPerPage, this.filteredCourses.length);
        this.elements.paginationInfo.textContent = `Mostrando ${startItem}-${endItem} de ${this.filteredCourses.length} cursos`;
    }

    goToPage(page) {
        if (page >= 1 && page <= this.totalPages) {
            this.currentPage = page;
            this.renderCourses();

            // Scroll to top
            window.scrollTo({ top: 0, behavior: 'smooth' });
        }
    }

    changeView(view) {
        if (view === this.currentView) return;

        this.currentView = view;

        // Actualizar botones de vista
        this.elements.viewButtons.forEach(btn => {
            if (btn.dataset.view === view) {
                btn.classList.add('active');
            } else {
                btn.classList.remove('active');
            }
        });

        this.renderCourses();
    }

    updateStats() {
        const total = this.filteredCourses.length;
        this.elements.coursesCount.textContent = `${total} curso${total !== 1 ? 's' : ''}`;
    }

    showLoadingState() {
        this.hideAllStates();
        this.elements.loadingState.style.display = 'grid';
    }

    showEmptyState() {
        this.hideAllStates();
        this.elements.emptyState.style.display = 'block';
    }

    showErrorState() {
        this.hideAllStates();
        this.elements.errorState.style.display = 'block';
    }

    hideAllStates() {
        this.elements.loadingState.style.display = 'none';
        this.elements.coursesGrid.style.display = 'none';
        this.elements.coursesList.style.display = 'none';
        this.elements.emptyState.style.display = 'none';
        this.elements.errorState.style.display = 'none';
        this.elements.pagination.style.display = 'none';
    }

    refreshCourses() {
        ASG.notifications().info('Actualizando cursos...');
        this.loadCourses();
    }

    duplicateCourse(courseCode) {
        ASG.notifications().info(`Duplicando curso ${courseCode}...`);
        // TODO: Implementar duplicación de curso
        setTimeout(() => {
            ASG.notifications().success('Curso duplicado correctamente');
        }, 1000);
    }

    showCourseActions(courseCode) {
        const course = this.courses.find(c => c.code_course === courseCode);
        if (!course) return;

        // Crear menú contextual simple
        const actions = [
            { label: 'Ver curso', icon: 'bi-eye', action: () => window.open(`/course?id=${courseCode}`, '_blank') },
            { label: 'Editar', icon: 'bi-pencil', action: () => window.location.href = `https://abilityseminarsgroup.com/edit-course/?id=${courseCode}` },
            { label: 'Duplicar', icon: 'bi-files', action: () => this.duplicateCourse(courseCode) },
            { label: 'Cambiar estado', icon: 'bi-toggle-on', action: () => this.toggleCourseStatus(courseCode) },
            { label: 'Eliminar', icon: 'bi-trash', action: () => this.deleteCourse(courseCode), danger: true }
        ];

        // Mostrar menú (implementación simple con confirm por ahora)
        const actionLabels = actions.map(a => a.label).join('\n');
        const choice = prompt(`Acciones para "${course.name_course}":\n\n${actionLabels}\n\nEscribe el número de la acción (1-${actions.length}):`);

        const actionIndex = parseInt(choice) - 1;
        if (actionIndex >= 0 && actionIndex < actions.length) {
            actions[actionIndex].action();
        }
    }

    toggleCourseStatus(courseCode) {
        const course = this.courses.find(c => c.code_course === courseCode);
        if (!course) return;

        const newStatus = course.status_course === 'published' ? 'draft' : 'published';
        const statusText = newStatus === 'published' ? 'publicar' : 'despublicar';

        if (confirm(`¿Estás seguro de que quieres ${statusText} este curso?`)) {
            course.status_course = newStatus;
            this.applyFilters();
            ASG.notifications().success(`Curso ${statusText === 'publicar' ? 'publicado' : 'despublicado'} correctamente`);
        }
    }

    deleteCourse(courseCode) {
        const course = this.courses.find(c => c.code_course === courseCode);
        if (!course) return;

        if (confirm(`¿Estás seguro de que quieres eliminar "${course.name_course}"?\n\nEsta acción no se puede deshacer.`)) {
            this.courses = this.courses.filter(c => c.code_course !== courseCode);
            this.applyFilters();
            ASG.notifications().success('Curso eliminado correctamente');
        }
    }
}

/**
 * ========================================
 * INICIALIZACIÓN
 * ========================================
 */
let coursesManager = null;

document.addEventListener('DOMContentLoaded', function() {
    // Esperar a que los componentes base estén listos
    setTimeout(() => {
        coursesManager = new ASGCoursesManager();

        // Hacer disponible globalmente para debugging
        window.ASGCoursesManager = coursesManager;
    }, 100);
});
