<?php
/**
 * Plugin Name: ASG Course Manager
 * Description: Sistema de gestión de cursos para AbilitySeminarsGroup
 * Version: 1.0.0
 * Author: ASG Team
 */

// Prevenir acceso directo
if (!defined('ABSPATH')) {
    exit;
}

// Definir constantes del plugin
define('ASG_PLUGIN_PATH', plugin_dir_path(__FILE__));
define('ASG_PLUGIN_URL', plugin_dir_url(__FILE__));
define('ASG_PLUGIN_VERSION', '1.0.0');

/**
 * Clase principal del plugin ASG Course Manager
 */
class ASG_Course_Manager {
    
    public function __construct() {
        add_action('init', [$this, 'init']);
        add_action('rest_api_init', [$this, 'register_rest_routes']);
        
        // Hooks de activación/desactivación
        register_activation_hook(__FILE__, [$this, 'activate']);
        register_deactivation_hook(__FILE__, [$this, 'deactivate']);
    }
    
    /**
     * Inicializar el plugin
     */
    public function init() {
        // Cargar archivos necesarios
        $this->load_includes();
        
        // Verificar permisos
        if (!current_user_can('manage_options')) {
            return;
        }
        
        // Log de inicialización
        error_log('✅ ASG Course Manager: Plugin inicializado correctamente');
    }
    
    /**
     * Cargar archivos de inclusión
     */
    private function load_includes() {
        require_once ASG_PLUGIN_PATH . 'includes/class-course-api.php';
        require_once ASG_PLUGIN_PATH . 'includes/class-course-model.php';
        require_once ASG_PLUGIN_PATH . 'includes/class-image-handler.php';
    }
    
    /**
     * Registrar rutas de la API REST
     */
    public function register_rest_routes() {
        $course_api = new ASG_Course_API();
        $course_api->register_routes();
    }
    
    /**
     * Activar plugin
     */
    public function activate() {
        // Crear tablas si no existen
        $this->create_database_tables();
        
        // Flush rewrite rules
        flush_rewrite_rules();
        
        error_log('✅ ASG Course Manager: Plugin activado');
    }
    
    /**
     * Desactivar plugin
     */
    public function deactivate() {
        flush_rewrite_rules();
        error_log('⚠️ ASG Course Manager: Plugin desactivado');
    }
    
    /**
     * Crear tablas de base de datos
     */
    private function create_database_tables() {
        global $wpdb;
        
        $charset_collate = $wpdb->get_charset_collate();
        $table_prefix = $wpdb->prefix;
        
        // Tabla principal de cursos
        $courses_table = $table_prefix . 'asg_courses';
        $courses_sql = "CREATE TABLE $courses_table (
            id bigint(20) unsigned NOT NULL AUTO_INCREMENT,
            post_id bigint(20) unsigned NOT NULL,
            price decimal(10,2) DEFAULT 0.00,
            category varchar(100) DEFAULT 'business',
            level enum('beginner','intermediate','advanced') DEFAULT 'beginner',
            duration decimal(5,2) DEFAULT 0.00,
            language varchar(10) DEFAULT 'es',
            status enum('draft','published','archived') DEFAULT 'draft',
            created_at datetime DEFAULT CURRENT_TIMESTAMP,
            updated_at datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            PRIMARY KEY (id),
            KEY post_id (post_id)
        ) $charset_collate;";
        
        // Tabla de módulos
        $modules_table = $table_prefix . 'asg_course_modules';
        $modules_sql = "CREATE TABLE $modules_table (
            id bigint(20) unsigned NOT NULL AUTO_INCREMENT,
            course_id bigint(20) unsigned NOT NULL,
            title varchar(255) NOT NULL,
            description text,
            duration int(11) DEFAULT 0,
            order_index int(11) DEFAULT 0,
            image_url varchar(500) DEFAULT NULL,
            created_at datetime DEFAULT CURRENT_TIMESTAMP,
            PRIMARY KEY (id),
            KEY course_id (course_id)
        ) $charset_collate;";
        
        // Tabla de objetivos de aprendizaje
        $objectives_table = $table_prefix . 'asg_course_learn_objectives';
        $objectives_sql = "CREATE TABLE $objectives_table (
            id bigint(20) unsigned NOT NULL AUTO_INCREMENT,
            course_id bigint(20) unsigned NOT NULL,
            objective text NOT NULL,
            order_index int(11) DEFAULT 0,
            PRIMARY KEY (id),
            KEY course_id (course_id)
        ) $charset_collate;";
        
        // Tabla de beneficios
        $benefits_table = $table_prefix . 'asg_course_benefits';
        $benefits_sql = "CREATE TABLE $benefits_table (
            id bigint(20) unsigned NOT NULL AUTO_INCREMENT,
            course_id bigint(20) unsigned NOT NULL,
            benefit text NOT NULL,
            order_index int(11) DEFAULT 0,
            PRIMARY KEY (id),
            KEY course_id (course_id)
        ) $charset_collate;";
        
        require_once(ABSPATH . 'wp-admin/includes/upgrade.php');
        dbDelta($courses_sql);
        dbDelta($modules_sql);
        dbDelta($objectives_sql);
        dbDelta($benefits_sql);
        
        // Actualizar versión de BD
        update_option('asg_db_version', '1.0.0');
    }
}

// Inicializar el plugin
new ASG_Course_Manager();
