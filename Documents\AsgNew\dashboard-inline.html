<!DOCTYPE html>
<html lang="es">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Dashboard ASG - Inline JS</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.11.3/font/bootstrap-icons.css">
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background-color: #f8f9fa;
            overflow-x: hidden;
        }

        /* Navbar Styles */
        .navbar {
            background: linear-gradient(135deg, #1e3a5f 0%, #2563eb 100%);
            height: 70px;
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            z-index: 1000;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }

        .navbar-brand img {
            width: 130px;
            height: auto;
        }

        .navbar-nav .nav-link {
            color: white !important;
            font-weight: 500;
            padding: 0.5rem 1rem;
            border-radius: 6px;
            transition: all 0.3s ease;
        }

        .navbar-nav .nav-link:hover {
            background-color: rgba(255,255,255,0.1);
            transform: translateY(-1px);
        }

        /* Main Content */
        .main-content {
            margin-top: 70px;
            padding: 2rem;
            min-height: calc(100vh - 70px);
        }

        /* Dashboard Header */
        .dashboard-header {
            background: white;
            border-radius: 12px;
            padding: 2rem;
            margin-bottom: 2rem;
            box-shadow: 0 2px 10px rgba(0,0,0,0.05);
            border: 1px solid #e9ecef;
        }

        .dashboard-title {
            font-size: 2rem;
            font-weight: 700;
            color: #1e3a5f;
            margin-bottom: 0.5rem;
        }

        .dashboard-subtitle {
            color: #6c757d;
            font-size: 1.1rem;
        }

        /* Stats Cards */
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
            gap: 1.5rem;
            margin-bottom: 2rem;
        }

        .stat-card {
            background: white;
            border-radius: 12px;
            padding: 1.5rem;
            box-shadow: 0 2px 10px rgba(0,0,0,0.05);
            border: 1px solid #e9ecef;
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
        }

        .stat-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 20px rgba(0,0,0,0.1);
        }

        .stat-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 4px;
            background: var(--card-color, #2563eb);
        }

        .stat-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 1rem;
        }

        .stat-icon {
            width: 3rem;
            height: 3rem;
            border-radius: 10px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 1.5rem;
            background: var(--icon-bg, #e3f2fd);
            color: var(--icon-color, #2563eb);
        }

        .stat-number {
            font-size: 2.5rem;
            font-weight: 700;
            color: #1e3a5f;
            margin: 0.5rem 0;
            line-height: 1;
        }

        .stat-label {
            font-size: 0.9rem;
            color: #6c757d;
            font-weight: 500;
            margin: 0;
        }

        .stat-change {
            display: flex;
            align-items: center;
            gap: 0.5rem;
            margin-top: 1rem;
            font-size: 0.85rem;
        }

        .stat-change.positive {
            color: #10b981;
        }

        .stat-change.negative {
            color: #ef4444;
        }

        /* Recent Courses */
        .recent-courses {
            background: white;
            border-radius: 12px;
            padding: 1.5rem;
            box-shadow: 0 2px 10px rgba(0,0,0,0.05);
            border: 1px solid #e9ecef;
        }

        .section-title {
            font-size: 1.25rem;
            font-weight: 600;
            color: #1e3a5f;
            margin-bottom: 1.5rem;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .course-item {
            display: flex;
            align-items: center;
            gap: 1rem;
            padding: 1rem;
            border-radius: 8px;
            transition: all 0.3s ease;
            border-bottom: 1px solid #f1f3f4;
        }

        .course-item:hover {
            background-color: #f8f9fa;
        }

        .course-item:last-child {
            border-bottom: none;
        }

        .course-image {
            width: 60px;
            height: 45px;
            border-radius: 6px;
            background-size: cover;
            background-position: center;
            flex-shrink: 0;
        }

        .course-info {
            flex: 1;
            min-width: 0;
        }

        .course-title {
            font-weight: 600;
            color: #1e3a5f;
            margin-bottom: 0.25rem;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
        }

        .course-meta {
            font-size: 0.85rem;
            color: #6b7280;
        }

        .course-status {
            padding: 0.25rem 0.75rem;
            border-radius: 20px;
            font-size: 0.75rem;
            font-weight: 500;
            text-transform: uppercase;
        }

        .status-published {
            background-color: #d1fae5;
            color: #065f46;
        }

        .status-draft {
            background-color: #fef3c7;
            color: #92400e;
        }

        .course-price {
            font-weight: 600;
            color: #2563eb;
            margin-left: 1rem;
        }

        /* Loading States */
        .loading-skeleton {
            background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
            background-size: 200% 100%;
            animation: loading 1.5s infinite;
            border-radius: 4px;
        }

        @keyframes loading {
            0% { background-position: 200% 0; }
            100% { background-position: -200% 0; }
        }

        .loading-number {
            height: 2.5rem;
            width: 100px;
            margin: 0.5rem 0;
        }

        .loading-text {
            height: 1rem;
            width: 150px;
        }

        /* API Status Indicator */
        .api-status {
            position: fixed;
            bottom: 20px;
            right: 20px;
            padding: 0.5rem 1rem;
            border-radius: 20px;
            font-size: 0.8rem;
            font-weight: 500;
            z-index: 1000;
            transition: all 0.3s ease;
        }

        .api-status.online {
            background-color: #d1fae5;
            color: #065f46;
        }

        .api-status.offline {
            background-color: #fef2f2;
            color: #991b1b;
        }

        /* Toast Animations */
        @keyframes slideIn {
            from {
                transform: translateX(100%);
                opacity: 0;
            }
            to {
                transform: translateX(0);
                opacity: 1;
            }
        }

        @keyframes slideOut {
            from {
                transform: translateX(0);
                opacity: 1;
            }
            to {
                transform: translateX(100%);
                opacity: 0;
            }
        }

        .spin {
            animation: spin 1s linear infinite;
        }

        @keyframes spin {
            from { transform: rotate(0deg); }
            to { transform: rotate(360deg); }
        }

        /* Responsive */
        @media (max-width: 768px) {
            .main-content {
                padding: 1rem;
            }
            
            .dashboard-header {
                padding: 1.5rem;
            }
            
            .stats-grid {
                grid-template-columns: 1fr;
            }
            
            .course-item {
                flex-direction: column;
                align-items: flex-start;
                gap: 0.75rem;
            }
            
            .course-image {
                width: 100%;
                height: 120px;
            }
        }
    </style>
</head>
<body>
    <!-- Navbar -->
    <nav class="navbar navbar-expand-lg">
        <div class="container-fluid">
            <a class="navbar-brand" href="#">
                <img src="https://abilityseminarsgroup.com/wp-content/uploads/2023/09/Asset-6-4-768x355.png" alt="ASG Logo">
            </a>
            
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav ms-auto">
                    <li class="nav-item">
                        <a class="nav-link active" href="#">
                            <i class="bi bi-speedometer2"></i> Dashboard
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="https://abilityseminarsgroup.com/all-courses/">
                            <i class="bi bi-collection"></i> Todos los Cursos
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="https://abilityseminarsgroup.com/new-course/">
                            <i class="bi bi-plus-circle"></i> Nuevo Curso
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="#" onclick="testApiConnection()">
                            <i class="bi bi-gear"></i> API Test
                        </a>
                    </li>
                </ul>
            </div>
        </div>
    </nav>

    <!-- Main Content -->
    <div class="main-content">
        <!-- Dashboard Header -->
        <div class="dashboard-header">
            <h1 class="dashboard-title">📊 Dashboard ASG</h1>
            <p class="dashboard-subtitle">Panel de control y estadísticas del sistema de cursos</p>
            <div class="mt-3">
                <button class="btn btn-primary" onclick="refreshDashboard()">
                    <i class="bi bi-arrow-clockwise"></i> Actualizar
                </button>
                <button class="btn btn-outline-primary ms-2" onclick="showNotification('✅ Sistema funcionando correctamente', 'success')">
                    <i class="bi bi-check-circle"></i> Test Notificación
                </button>
            </div>
        </div>

        <!-- Stats Grid -->
        <div class="stats-grid">
            <!-- Total Courses -->
            <div class="stat-card" style="--card-color: #2563eb; --icon-bg: #dbeafe; --icon-color: #2563eb;">
                <div class="stat-header">
                    <div>
                        <p class="stat-label">Total de Cursos</p>
                        <div class="stat-number" id="totalCourses">6</div>
                    </div>
                    <div class="stat-icon">📚</div>
                </div>
                <div class="stat-change positive" id="coursesChange">
                    <i class="bi bi-arrow-up"></i>
                    <span>Cargando...</span>
                </div>
            </div>

            <!-- Published Courses -->
            <div class="stat-card" style="--card-color: #10b981; --icon-bg: #d1fae5; --icon-color: #10b981;">
                <div class="stat-header">
                    <div>
                        <p class="stat-label">Cursos Publicados</p>
                        <div class="stat-number" id="publishedCourses">
                            <div class="loading-skeleton loading-number"></div>
                        </div>
                    </div>
                    <div class="stat-icon">✅</div>
                </div>
                <div class="stat-change positive" id="publishedPercentage">
                    <i class="bi bi-check-circle"></i>
                    <span>Cargando...</span>
                </div>
            </div>

            <!-- Draft Courses -->
            <div class="stat-card" style="--card-color: #f59e0b; --icon-bg: #fef3c7; --icon-color: #f59e0b;">
                <div class="stat-header">
                    <div>
                        <p class="stat-label">Borradores</p>
                        <div class="stat-number" id="draftCourses">
                            <div class="loading-skeleton loading-number"></div>
                        </div>
                    </div>
                    <div class="stat-icon">✏️</div>
                </div>
                <div class="stat-change" id="draftPercentage">
                    <i class="bi bi-pencil"></i>
                    <span>Cargando...</span>
                </div>
            </div>

            <!-- Revenue -->
            <div class="stat-card" style="--card-color: #06b6d4; --icon-bg: #cffafe; --icon-color: #06b6d4;">
                <div class="stat-header">
                    <div>
                        <p class="stat-label">Ingresos Potenciales</p>
                        <div class="stat-number" id="totalRevenue">
                            <div class="loading-skeleton loading-number"></div>
                        </div>
                    </div>
                    <div class="stat-icon">💰</div>
                </div>
                <div class="stat-change" id="avgPrice">
                    <i class="bi bi-currency-euro"></i>
                    <span>Cargando...</span>
                </div>
            </div>
        </div>

        <!-- Recent Courses -->
        <div class="recent-courses">
            <div class="section-title">
                <span>📚 Cursos Recientes</span>
                <button class="btn btn-outline-primary btn-sm" onclick="showNotification('ℹ️ Función de ver todos los cursos próximamente', 'info')">
                    Ver todos <i class="bi bi-arrow-right"></i>
                </button>
            </div>
            <div id="recentCourses">
                <!-- Loading skeleton -->
                <div class="course-item">
                    <div class="loading-skeleton" style="width: 60px; height: 45px; border-radius: 6px;"></div>
                    <div style="flex: 1;">
                        <div class="loading-skeleton loading-text" style="margin-bottom: 0.5rem;"></div>
                        <div class="loading-skeleton" style="height: 0.8rem; width: 200px;"></div>
                    </div>
                </div>
                <div class="course-item">
                    <div class="loading-skeleton" style="width: 60px; height: 45px; border-radius: 6px;"></div>
                    <div style="flex: 1;">
                        <div class="loading-skeleton loading-text" style="margin-bottom: 0.5rem;"></div>
                        <div class="loading-skeleton" style="height: 0.8rem; width: 180px;"></div>
                    </div>
                </div>
                <div class="course-item">
                    <div class="loading-skeleton" style="width: 60px; height: 45px; border-radius: 6px;"></div>
                    <div style="flex: 1;">
                        <div class="loading-skeleton loading-text" style="margin-bottom: 0.5rem;"></div>
                        <div class="loading-skeleton" style="height: 0.8rem; width: 160px;"></div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- API Status Indicator -->
    <div class="api-status offline" id="apiStatus">
        <i class="bi bi-wifi-off"></i> Modo Demo
    </div>

    <!-- Scripts -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.3/dist/js/bootstrap.bundle.min.js"></script>
    
    <!-- JavaScript Inline -->
    <script>
        // ========================================
        // ASG CONFIGURACIÓN INLINE
        // ========================================
        const ASG_CONFIG = {
            version: '2.0.0',
            name: 'AbilitySeminarsGroup Course Management',
            apiUrl: 'https://abilityseminarsgroup.com/wp-json/asg/v1',
            mockData: true,

            // Endpoints
            endpoints: {
                courses: '/courses',
                dashboardStats: '/dashboard/stats',
                categories: '/courses/categories',
                uploadImage: '/courses/upload-image'
            },

            // Configuración UI
            ui: {
                notifications: {
                    duration: 5000,
                    position: 'top-right'
                },
                animations: {
                    fast: 150,
                    normal: 300,
                    slow: 500
                }
            },

            // Categorías
            categories: {
                finanzas: { label: 'Finanzas', icon: '💰', color: '#10b981' },
                marketing: { label: 'Marketing', icon: '📱', color: '#f59e0b' },
                'desarrollo-personal': { label: 'Desarrollo Personal', icon: '🧠', color: '#8b5cf6' },
                tecnologia: { label: 'Tecnología', icon: '💻', color: '#06b6d4' },
                negocios: { label: 'Negocios', icon: '💼', color: '#ef4444' },
                salud: { label: 'Salud', icon: '🏥', color: '#84cc16' }
            },

            // Estados
            states: {
                draft: { label: 'Borrador', color: '#f59e0b', badge: 'status-draft' },
                published: { label: 'Publicado', color: '#10b981', badge: 'status-published' },
                archived: { label: 'Archivado', color: '#6b7280', badge: 'status-archived' }
            }
        };

        // ========================================
        // ASG API CLIENT INLINE
        // ========================================
        class ASGApiClient {
            constructor() {
                this.baseUrl = ASG_CONFIG.apiUrl;
                this.timeout = 30000;
                this.retries = 3;
                console.log('🚀 ASG API Client (Inline) initialized');
            }

            async request(endpoint, options = {}) {
                const url = `${this.baseUrl}${endpoint}`;
                const defaultOptions = {
                    method: 'GET',
                    headers: {
                        'Content-Type': 'application/json',
                        'Accept': 'application/json'
                    },
                    timeout: this.timeout
                };

                const finalOptions = { ...defaultOptions, ...options };

                try {
                    console.log(`📡 API Request: ${finalOptions.method} ${url}`);

                    const controller = new AbortController();
                    const timeoutId = setTimeout(() => controller.abort(), this.timeout);

                    const response = await fetch(url, {
                        ...finalOptions,
                        signal: controller.signal
                    });

                    clearTimeout(timeoutId);

                    if (!response.ok) {
                        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                    }

                    const data = await response.json();
                    console.log(`✅ API Success: ${url}`, data);
                    return data;

                } catch (error) {
                    console.warn(`⚠️ API Error: ${error.message}`);
                    throw error;
                }
            }

            async getDashboardStats() {
                try {
                    const response = await this.request(ASG_CONFIG.endpoints.dashboardStats);
                    return response.data || response;
                } catch (error) {
                    console.warn('📊 Using mock dashboard stats');
                    return this.getMockDashboardStats();
                }
            }

            async getCourses(params = {}) {
                try {
                    const queryParams = new URLSearchParams(params).toString();
                    const endpoint = queryParams ?
                        `${ASG_CONFIG.endpoints.courses}?${queryParams}` :
                        ASG_CONFIG.endpoints.courses;

                    const response = await this.request(endpoint);
                    return response.data || response;
                } catch (error) {
                    console.warn('📚 Using mock courses');
                    return this.getMockCourses();
                }
            }

            getMockDashboardStats() {
                return {
                    totalCourses: 6,
                    publishedCourses: 4,
                    draftCourses: 2,
                    archivedCourses: 0,
                    totalRevenue: 1299.96,
                    avgPrice: 216.66,
                    coursesChange: 25,
                    revenueChange: 15,
                    studentsTotal: 342,
                    studentsChange: 18
                };
            }

            getMockCourses() {
                return [
                    {
                        id: 1,
                        code: 'course_1',
                        title: 'Como hacerte millonario?',
                        description: 'Aprende las estrategias más efectivas para generar riqueza y alcanzar la libertad financiera.',
                        image: 'https://images.unsplash.com/photo-1579621970563-ebec7560ff3e?w=400&h=300&fit=crop',
                        price: 299.99,
                        status: 'published',
                        category: 'finanzas',
                        modules: 8,
                        lessons: 24,
                        students: 156,
                        rating: 4.8,
                        created: '2024-12-15',
                        updated: '2024-12-20'
                    },
                    {
                        id: 2,
                        code: 'course_2',
                        title: 'Marketing Digital Avanzado',
                        description: 'Domina las técnicas más avanzadas del marketing digital y haz crecer tu negocio online.',
                        image: 'https://images.unsplash.com/photo-1460925895917-afdab827c52f?w=400&h=300&fit=crop',
                        price: 199.99,
                        status: 'published',
                        category: 'marketing',
                        modules: 6,
                        lessons: 18,
                        students: 89,
                        rating: 4.6,
                        created: '2024-12-10',
                        updated: '2024-12-18'
                    },
                    {
                        id: 3,
                        code: 'course_3',
                        title: 'Desarrollo Personal Integral',
                        description: 'Transforma tu vida con técnicas probadas de crecimiento personal y liderazgo.',
                        image: 'https://images.unsplash.com/photo-1516321318423-f06f85e504b3?w=400&h=300&fit=crop',
                        price: 149.99,
                        status: 'draft',
                        category: 'desarrollo-personal',
                        modules: 4,
                        lessons: 12,
                        students: 0,
                        rating: 0,
                        created: '2024-12-05',
                        updated: '2024-12-22'
                    },
                    {
                        id: 4,
                        code: 'course_4',
                        title: 'Programación Web Moderna',
                        description: 'Aprende las tecnologías más demandadas del desarrollo web actual.',
                        image: 'https://images.unsplash.com/photo-1461749280684-dccba630e2f6?w=400&h=300&fit=crop',
                        price: 249.99,
                        status: 'published',
                        category: 'tecnologia',
                        modules: 10,
                        lessons: 35,
                        students: 67,
                        rating: 4.9,
                        created: '2024-11-28',
                        updated: '2024-12-19'
                    },
                    {
                        id: 5,
                        code: 'course_5',
                        title: 'Gestión de Negocios Exitosos',
                        description: 'Estrategias comprobadas para crear y hacer crecer tu negocio.',
                        image: 'https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=400&h=300&fit=crop',
                        price: 179.99,
                        status: 'published',
                        category: 'negocios',
                        modules: 7,
                        lessons: 21,
                        students: 134,
                        rating: 4.7,
                        created: '2024-11-20',
                        updated: '2024-12-21'
                    },
                    {
                        id: 6,
                        code: 'course_6',
                        title: 'Nutrición y Bienestar',
                        description: 'Guía completa para una vida saludable y equilibrada.',
                        image: 'https://images.unsplash.com/photo-1490645935967-10de6ba17061?w=400&h=300&fit=crop',
                        price: 129.99,
                        status: 'draft',
                        category: 'salud',
                        modules: 5,
                        lessons: 15,
                        students: 0,
                        rating: 0,
                        created: '2024-12-01',
                        updated: '2024-12-23'
                    }
                ];
            }

            async checkApiStatus() {
                try {
                    const response = await this.request('/info');
                    return true;
                } catch (error) {
                    return false;
                }
            }
        }

        // Instancia global
        const ASG_API = new ASGApiClient();

        // ========================================
        // UTILIDADES INLINE
        // ========================================
        const ASG_UTILS = {
            formatCurrency: (amount) => {
                return new Intl.NumberFormat('es-ES', {
                    style: 'currency',
                    currency: 'EUR'
                }).format(amount || 0);
            },

            formatDate: (date) => {
                return new Intl.DateTimeFormat('es-ES', {
                    year: 'numeric',
                    month: 'long',
                    day: 'numeric'
                }).format(new Date(date));
            },

            formatNumber: (number) => {
                return new Intl.NumberFormat('es-ES').format(number || 0);
            },

            getCategoryInfo: (categoryCode) => {
                return ASG_CONFIG.categories[categoryCode] || {
                    label: categoryCode,
                    icon: '📚',
                    color: '#6b7280'
                };
            },

            getStatusInfo: (statusCode) => {
                return ASG_CONFIG.states[statusCode] || {
                    label: statusCode,
                    color: '#6b7280',
                    badge: 'status-draft'
                };
            },

            truncateText: (text, maxLength = 100) => {
                if (!text || text.length <= maxLength) return text;
                return text.substring(0, maxLength) + '...';
            },

            generateId: () => {
                return Date.now().toString(36) + Math.random().toString(36).substr(2);
            },

            debounce: (func, wait) => {
                let timeout;
                return function executedFunction(...args) {
                    const later = () => {
                        clearTimeout(timeout);
                        func(...args);
                    };
                    clearTimeout(timeout);
                    timeout = setTimeout(later, wait);
                };
            }
        };

        // ========================================
        // FUNCIONES DE INTERFAZ
        // ========================================
        function showNotification(message, type = 'info') {
            const toast = document.createElement('div');
            const colors = {
                success: '#10b981',
                error: '#ef4444',
                warning: '#f59e0b',
                info: '#06b6d4'
            };
            
            const icons = {
                success: '✅',
                error: '❌',
                warning: '⚠️',
                info: 'ℹ️'
            };
            
            toast.style.cssText = `
                position: fixed;
                top: 90px;
                right: 20px;
                background: ${colors[type] || colors.info};
                color: white;
                padding: 12px 16px;
                border-radius: 8px;
                box-shadow: 0 4px 12px rgba(0,0,0,0.15);
                z-index: 9999;
                animation: slideIn 0.3s ease-out;
                cursor: pointer;
                max-width: 400px;
            `;
            
            toast.innerHTML = `
                <div style="display: flex; align-items: center; gap: 8px;">
                    <span>${icons[type] || icons.info}</span>
                    <span>${message}</span>
                    <span style="opacity: 0.7; margin-left: auto;">×</span>
                </div>
            `;
            
            toast.addEventListener('click', () => {
                toast.style.animation = 'slideOut 0.3s ease-in';
                setTimeout(() => document.body.removeChild(toast), 300);
            });
            
            document.body.appendChild(toast);
            
            setTimeout(() => {
                if (toast.parentNode) {
                    toast.style.animation = 'slideOut 0.3s ease-in';
                    setTimeout(() => {
                        if (toast.parentNode) {
                            document.body.removeChild(toast);
                        }
                    }, 300);
                }
            }, 5000);
        }

        // ========================================
        // FUNCIONES DE DASHBOARD
        // ========================================
        async function loadDashboardData() {
            try {
                showLoadingState(true);

                // Cargar estadísticas
                const stats = await ASG_API.getDashboardStats();
                updateStatsDisplay(stats);

                // Cargar cursos recientes
                const courses = await ASG_API.getCourses({ per_page: 5 });
                updateRecentCourses(courses);

                showLoadingState(false);
                updateApiStatus(true);

            } catch (error) {
                console.error('Error loading dashboard data:', error);
                showLoadingState(false);
                updateApiStatus(false);
                showNotification('⚠️ Error al cargar datos, usando información demo', 'warning');
            }
        }

        function updateStatsDisplay(stats) {
            // Animar números
            animateNumber('totalCourses', stats.totalCourses || 0);
            animateNumber('publishedCourses', stats.publishedCourses || 0);
            animateNumber('draftCourses', stats.draftCourses || 0);

            // Actualizar ingresos
            document.getElementById('totalRevenue').textContent = ASG_UTILS.formatCurrency(stats.totalRevenue || 0);

            // Calcular y mostrar porcentajes
            const total = stats.totalCourses || 0;
            const publishedPercentage = total > 0 ? Math.round((stats.publishedCourses / total) * 100) : 0;
            const draftPercentage = total > 0 ? Math.round((stats.draftCourses / total) * 100) : 0;

            // Actualizar cambios y porcentajes
            updateStatChange('coursesChange', stats.coursesChange || 0, '% vs mes anterior');
            updateStatChange('publishedPercentage', publishedPercentage, '% del total');
            updateStatChange('draftPercentage', draftPercentage, '% pendientes');
            updateStatChange('avgPrice', ASG_UTILS.formatCurrency(stats.avgPrice || 0), 'promedio');
        }

        function updateStatChange(elementId, value, suffix) {
            const element = document.querySelector(`#${elementId}`);
            if (element) {
                const isPositive = typeof value === 'number' && value > 0;
                const icon = isPositive ? 'bi-arrow-up' : 'bi-dash';
                const className = isPositive ? 'positive' : '';

                element.className = `stat-change ${className}`;
                element.innerHTML = `
                    <i class="bi ${icon}"></i>
                    <span>${value}${suffix}</span>
                `;
            }
        }

        function updateRecentCourses(courses) {
            const container = document.getElementById('recentCourses');

            if (!courses || courses.length === 0) {
                container.innerHTML = `
                    <div class="text-center py-4">
                        <i class="bi bi-collection" style="font-size: 3rem; color: #6c757d;"></i>
                        <h5 class="mt-3 text-muted">No hay cursos recientes</h5>
                        <p class="text-muted">Crea tu primer curso para comenzar</p>
                        <button class="btn btn-primary" onclick="showNotification('🚀 Función de crear curso próximamente', 'info')">
                            <i class="bi bi-plus-circle"></i> Crear Curso
                        </button>
                    </div>
                `;
                return;
            }

            container.innerHTML = courses.map(course => {
                const category = ASG_UTILS.getCategoryInfo(course.category);
                const status = ASG_UTILS.getStatusInfo(course.status);

                return `
                    <div class="course-item" onclick="viewCourse('${course.code}')">
                        <div class="course-image" style="background-image: url('${course.image}')"></div>
                        <div class="course-info">
                            <div class="course-title">${course.title}</div>
                            <div class="course-meta">
                                ${category.icon} ${category.label} •
                                ${course.modules || 0} módulos •
                                ${course.lessons || 0} lecciones
                            </div>
                        </div>
                        <div class="course-status ${status.badge}">
                            ${status.label.toUpperCase()}
                        </div>
                        <div class="course-price">
                            ${ASG_UTILS.formatCurrency(course.price)}
                        </div>
                    </div>
                `;
            }).join('');
        }

        function animateNumber(elementId, targetValue) {
            const element = document.getElementById(elementId);
            if (!element) return;

            const startValue = 0;
            const duration = 1000;
            const startTime = performance.now();

            function animate(currentTime) {
                const elapsed = currentTime - startTime;
                const progress = Math.min(elapsed / duration, 1);
                const currentValue = startValue + (targetValue - startValue) * progress;

                element.textContent = Math.round(currentValue);

                if (progress < 1) {
                    requestAnimationFrame(animate);
                }
            }

            requestAnimationFrame(animate);
        }

        function showLoadingState(isLoading) {
            const elements = ['totalCourses', 'publishedCourses', 'draftCourses', 'totalRevenue'];

            elements.forEach(id => {
                const element = document.getElementById(id);
                if (element) {
                    if (isLoading) {
                        element.innerHTML = '<div class="loading-skeleton loading-number"></div>';
                    }
                }
            });

            if (isLoading) {
                document.getElementById('recentCourses').innerHTML = `
                    <div class="course-item">
                        <div class="loading-skeleton" style="width: 60px; height: 45px; border-radius: 6px;"></div>
                        <div style="flex: 1;">
                            <div class="loading-skeleton loading-text" style="margin-bottom: 0.5rem;"></div>
                            <div class="loading-skeleton" style="height: 0.8rem; width: 200px;"></div>
                        </div>
                    </div>
                `;
            }
        }

        async function refreshDashboard() {
            const btn = event.target;
            const originalText = btn.innerHTML;

            btn.innerHTML = '<i class="bi bi-arrow-clockwise spin"></i> Actualizando...';
            btn.disabled = true;

            try {
                await loadDashboardData();
                showNotification('✅ Dashboard actualizado correctamente', 'success');
            } catch (error) {
                showNotification('❌ Error al actualizar el dashboard', 'error');
            } finally {
                btn.innerHTML = originalText;
                btn.disabled = false;
            }
        }

        function testApiConnection() {
            showNotification('🔄 Probando conexión con la API...', 'info');
            
            setTimeout(() => {
                // Simular test de API
                fetch(ASG_CONFIG.apiUrl + '/dashboard/stats')
                    .then(response => {
                        if (response.ok) {
                            showNotification('✅ API conectada correctamente', 'success');
                            updateApiStatus(true);
                        } else {
                            throw new Error('API offline');
                        }
                    })
                    .catch(() => {
                        showNotification('⚠️ API offline, usando datos demo', 'warning');
                        updateApiStatus(false);
                    });
            }, 1000);
        }

        function updateApiStatus(isOnline) {
            const statusEl = document.getElementById('apiStatus');
            if (isOnline) {
                statusEl.className = 'api-status online';
                statusEl.innerHTML = '<i class="bi bi-wifi"></i> API Online';
            } else {
                statusEl.className = 'api-status offline';
                statusEl.innerHTML = '<i class="bi bi-wifi-off"></i> Modo Demo';
            }
        }

        function viewCourse(courseCode) {
            showNotification(`🔍 Viendo curso: ${courseCode}`, 'info');
            // Aquí se podría abrir el curso en una nueva ventana o modal
            console.log('View course:', courseCode);
        }

        function navigateToAllCourses() {
            window.location.href = 'https://abilityseminarsgroup.com/all-courses/';
        }

        function navigateToNewCourse() {
            window.location.href = 'https://abilityseminarsgroup.com/new-course/';
        }

        // ========================================
        // INICIALIZACIÓN
        // ========================================
        document.addEventListener('DOMContentLoaded', function() {
            console.log('🚀 Dashboard ASG Inline v2.0.0 loaded');

            // Mostrar notificación de bienvenida
            setTimeout(() => {
                showNotification('🎉 Dashboard cargado correctamente', 'success');
            }, 500);

            // Cargar datos del dashboard
            setTimeout(() => {
                loadDashboardData();
            }, 1000);

            // Test automático de API después de cargar datos
            setTimeout(() => {
                testApiConnection();
            }, 3000);

            // Agregar event listeners adicionales
            setupEventListeners();
        });

        function setupEventListeners() {
            // Event listener para navegación
            document.querySelectorAll('.nav-link').forEach(link => {
                link.addEventListener('click', function(e) {
                    if (this.textContent.includes('Todos los Cursos')) {
                        e.preventDefault();
                        navigateToAllCourses();
                    } else if (this.textContent.includes('Nuevo Curso')) {
                        e.preventDefault();
                        navigateToNewCourse();
                    }
                });
            });

            // Event listener para teclas de acceso rápido
            document.addEventListener('keydown', function(e) {
                if (e.ctrlKey || e.metaKey) {
                    switch(e.key) {
                        case 'r':
                            e.preventDefault();
                            refreshDashboard();
                            break;
                        case 'n':
                            e.preventDefault();
                            navigateToNewCourse();
                            break;
                        case 'a':
                            e.preventDefault();
                            navigateToAllCourses();
                            break;
                    }
                }
            });

            // Auto-refresh cada 5 minutos
            setInterval(() => {
                console.log('🔄 Auto-refresh dashboard data');
                loadDashboardData();
            }, 5 * 60 * 1000);
        }

        // ========================================
        // FUNCIONES GLOBALES ADICIONALES
        // ========================================
        window.ASG_DASHBOARD = {
            refresh: refreshDashboard,
            loadData: loadDashboardData,
            showNotification: showNotification,
            testApi: testApiConnection,
            config: ASG_CONFIG,
            api: ASG_API,
            utils: ASG_UTILS
        };

        // Log de información del sistema
        console.log('📊 ASG Dashboard System Info:', {
            version: ASG_CONFIG.version,
            apiUrl: ASG_CONFIG.apiUrl,
            mockData: ASG_CONFIG.mockData,
            timestamp: new Date().toISOString()
        });
    </script>
</body>
</html>
