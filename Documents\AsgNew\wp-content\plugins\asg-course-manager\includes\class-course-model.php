<?php
/**
 * Modelo de datos para cursos ASG
 * 
 * Maneja todas las operaciones de base de datos relacionadas con cursos
 */

if (!defined('ABSPATH')) {
    exit;
}

class ASG_Course_Model {
    
    private $wpdb;
    private $table_courses;
    private $table_modules;
    private $table_objectives;
    private $table_benefits;
    
    public function __construct() {
        global $wpdb;
        $this->wpdb = $wpdb;
        
        // Nombres de tablas
        $this->table_courses = $wpdb->prefix . 'asg_courses';
        $this->table_modules = $wpdb->prefix . 'asg_course_modules';
        $this->table_objectives = $wpdb->prefix . 'asg_course_learn_objectives';
        $this->table_benefits = $wpdb->prefix . 'asg_course_benefits';
    }
    
    /**
     * Crear un nuevo curso
     * 
     * @param array $course_data Datos del curso
     * @return array|WP_Error Resultado de la operación
     */
    public function create_course($course_data) {
        try {
            // Iniciar transacción
            $this->wpdb->query('START TRANSACTION');
            
            // 1. <PERSON>rear post en WordPress
            $post_data = [
                'post_title' => sanitize_text_field($course_data['title']),
                'post_content' => wp_kses_post($course_data['description']),
                'post_status' => 'draft',
                'post_type' => 'course',
                'post_author' => get_current_user_id()
            ];
            
            $post_id = wp_insert_post($post_data);
            
            if (is_wp_error($post_id)) {
                throw new Exception('Error al crear el post: ' . $post_id->get_error_message());
            }
            
            // 2. Insertar datos específicos del curso
            $course_insert = $this->wpdb->insert(
                $this->table_courses,
                [
                    'post_id' => $post_id,
                    'price' => floatval($course_data['price']),
                    'category' => sanitize_text_field($course_data['category']),
                    'level' => sanitize_text_field($course_data['level']),
                    'duration' => floatval($course_data['duration']),
                    'language' => sanitize_text_field($course_data['language'] ?? 'es'),
                    'status' => sanitize_text_field($course_data['status'] ?? 'draft')
                ],
                ['%d', '%f', '%s', '%s', '%f', '%s', '%s']
            );
            
            if ($course_insert === false) {
                throw new Exception('Error al insertar datos del curso');
            }
            
            $course_id = $this->wpdb->insert_id;
            
            // 3. Insertar módulos si existen
            if (!empty($course_data['modules'])) {
                $this->insert_modules($course_id, $course_data['modules']);
            }
            
            // 4. Insertar objetivos de aprendizaje si existen
            if (!empty($course_data['learn_objectives'])) {
                $this->insert_learn_objectives($course_id, $course_data['learn_objectives']);
            }
            
            // 5. Insertar beneficios si existen
            if (!empty($course_data['benefits'])) {
                $this->insert_benefits($course_id, $course_data['benefits']);
            }
            
            // 6. Guardar imagen destacada si existe
            if (!empty($course_data['featured_image'])) {
                set_post_thumbnail($post_id, $course_data['featured_image']);
            }
            
            // Confirmar transacción
            $this->wpdb->query('COMMIT');
            
            // Retornar datos del curso creado
            return [
                'success' => true,
                'data' => [
                    'course_id' => $course_id,
                    'post_id' => $post_id,
                    'title' => $course_data['title'],
                    'status' => $course_data['status'] ?? 'draft'
                ],
                'message' => 'Curso creado exitosamente'
            ];
            
        } catch (Exception $e) {
            // Rollback en caso de error
            $this->wpdb->query('ROLLBACK');
            
            error_log('❌ ASG Course Model - Error creando curso: ' . $e->getMessage());
            
            return new WP_Error(
                'course_creation_failed',
                'Error al crear el curso: ' . $e->getMessage(),
                ['status' => 500]
            );
        }
    }
    
    /**
     * Guardar curso como borrador
     * 
     * @param array $course_data Datos del curso
     * @return array|WP_Error Resultado de la operación
     */
    public function save_draft($course_data) {
        $course_data['status'] = 'draft';
        return $this->create_course($course_data);
    }
    
    /**
     * Insertar módulos del curso
     * 
     * @param int $course_id ID del curso
     * @param array $modules Array de módulos
     */
    private function insert_modules($course_id, $modules) {
        foreach ($modules as $index => $module) {
            $this->wpdb->insert(
                $this->table_modules,
                [
                    'course_id' => $course_id,
                    'title' => sanitize_text_field($module['title']),
                    'description' => wp_kses_post($module['description'] ?? ''),
                    'duration' => intval($module['duration'] ?? 0),
                    'order_index' => $index,
                    'image_url' => esc_url_raw($module['image_url'] ?? '')
                ],
                ['%d', '%s', '%s', '%d', '%d', '%s']
            );
        }
    }
    
    /**
     * Insertar objetivos de aprendizaje
     * 
     * @param int $course_id ID del curso
     * @param array $objectives Array de objetivos
     */
    private function insert_learn_objectives($course_id, $objectives) {
        foreach ($objectives as $index => $objective) {
            $this->wpdb->insert(
                $this->table_objectives,
                [
                    'course_id' => $course_id,
                    'objective' => wp_kses_post($objective),
                    'order_index' => $index
                ],
                ['%d', '%s', '%d']
            );
        }
    }
    
    /**
     * Insertar beneficios
     * 
     * @param int $course_id ID del curso
     * @param array $benefits Array de beneficios
     */
    private function insert_benefits($course_id, $benefits) {
        foreach ($benefits as $index => $benefit) {
            $this->wpdb->insert(
                $this->table_benefits,
                [
                    'course_id' => $course_id,
                    'benefit' => wp_kses_post($benefit),
                    'order_index' => $index
                ],
                ['%d', '%s', '%d']
            );
        }
    }
    
    /**
     * Validar datos del curso
     * 
     * @param array $course_data Datos a validar
     * @return array|WP_Error Resultado de la validación
     */
    public function validate_course_data($course_data) {
        $errors = [];
        
        // Validaciones requeridas
        if (empty($course_data['title'])) {
            $errors['title'] = 'El título del curso es requerido';
        }
        
        if (empty($course_data['description'])) {
            $errors['description'] = 'La descripción del curso es requerida';
        }
        
        if (!isset($course_data['price']) || !is_numeric($course_data['price'])) {
            $errors['price'] = 'El precio debe ser un número válido';
        }
        
        if (!empty($course_data['level']) && !in_array($course_data['level'], ['beginner', 'intermediate', 'advanced'])) {
            $errors['level'] = 'El nivel debe ser: beginner, intermediate o advanced';
        }
        
        // Si hay errores, retornar error
        if (!empty($errors)) {
            return new WP_Error(
                'validation_failed',
                'Los datos proporcionados no son válidos',
                ['status' => 400, 'details' => $errors]
            );
        }
        
        return ['success' => true, 'message' => 'Datos válidos'];
    }
    
    /**
     * Obtener categorías disponibles
     * 
     * @return array Lista de categorías
     */
    public function get_categories() {
        return [
            'business' => 'Negocios',
            'technology' => 'Tecnología',
            'marketing' => 'Marketing',
            'leadership' => 'Liderazgo',
            'sales' => 'Ventas',
            'finance' => 'Finanzas',
            'communication' => 'Comunicación',
            'productivity' => 'Productividad'
        ];
    }
}
