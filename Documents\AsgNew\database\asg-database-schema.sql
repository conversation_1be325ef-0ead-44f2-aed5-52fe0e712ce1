-- ========================================
-- ESQUEMA DE BASE DE DATOS - ASG COURSES
-- ========================================
-- 
-- Descripción: Estructura optimizada para sistema dinámico de cursos
-- Soporte para: course_id dinámico, relaciones jer<PERSON>, metadatos
-- Prefijo: wpic_ (WordPress existente)
-- 
-- Autor: ASG Team
-- Fecha: 2025-01-04
-- Versión: 2.0.0 - REESTRUCTURACIÓN COMPLETA
-- ========================================

-- ========================================
-- TABLA PRINCIPAL: CURSOS
-- ========================================
CREATE TABLE IF NOT EXISTS `wpic_courses` (
  `id_course` int(11) NOT NULL AUTO_INCREMENT,
  `code_course` varchar(255) NOT NULL UNIQUE COMMENT 'Código único: course_1, course_2, etc.',
  `name_course` varchar(255) NOT NULL COMMENT 'Título del curso',
  `description_course` text COMMENT 'Descripción completa del curso',
  `cover_img` text COMMENT 'URL de imagen de portada',
  `price_course` decimal(10,2) DEFAULT 0.00 COMMENT 'Precio del curso',
  `date_course` datetime DEFAULT CURRENT_TIMESTAMP COMMENT 'Fecha de creación',
  `category_course` varchar(100) DEFAULT 'general' COMMENT 'Categoría del curso',
  `duration_course` int(11) DEFAULT 0 COMMENT 'Duración en minutos',
  `language_course` varchar(10) DEFAULT 'es' COMMENT 'Idioma del curso',
  `status_course` enum('draft','published','archived') DEFAULT 'draft' COMMENT 'Estado del curso',
  `featured_course` tinyint(1) DEFAULT 0 COMMENT 'Curso destacado',
  `enrollment_count` int(11) DEFAULT 0 COMMENT 'Número de inscripciones',
  `rating_average` decimal(3,2) DEFAULT 0.00 COMMENT 'Calificación promedio',
  `rating_count` int(11) DEFAULT 0 COMMENT 'Número de calificaciones',
  `seo_title` varchar(255) COMMENT 'Título SEO',
  `seo_description` text COMMENT 'Descripción SEO',
  `seo_keywords` text COMMENT 'Palabras clave SEO',
  `id_user` int(11) NOT NULL COMMENT 'ID del usuario creador',
  `is_deleted` tinyint(1) DEFAULT 0 COMMENT 'Eliminación lógica',
  `created_at` datetime DEFAULT CURRENT_TIMESTAMP,
  `updated_at` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id_course`),
  UNIQUE KEY `code_course` (`code_course`),
  KEY `idx_status` (`status_course`),
  KEY `idx_category` (`category_course`),
  KEY `idx_featured` (`featured_course`),
  KEY `idx_user` (`id_user`),
  KEY `idx_deleted` (`is_deleted`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- ========================================
-- TABLA: MÓDULOS DEL CURSO
-- ========================================
CREATE TABLE IF NOT EXISTS `wpic_modules` (
  `id_modules` int(11) NOT NULL AUTO_INCREMENT,
  `code_module` varchar(60) NOT NULL COMMENT 'Código único del módulo',
  `title_module` varchar(255) NOT NULL COMMENT 'Título del módulo',
  `description_module` text COMMENT 'Descripción del módulo',
  `cover_img` varchar(255) COMMENT 'Imagen del módulo',
  `duration_module` int(11) DEFAULT 0 COMMENT 'Duración en minutos',
  `order_module` int(11) DEFAULT 0 COMMENT 'Orden de visualización',
  `status_module` enum('draft','published','archived') DEFAULT 'draft',
  `code_course` varchar(255) NOT NULL COMMENT 'Referencia al curso',
  `id_user` int(11) NOT NULL COMMENT 'ID del usuario creador',
  `is_deleted` tinyint(1) DEFAULT 0,
  `date_module` datetime DEFAULT CURRENT_TIMESTAMP,
  `updated_at` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id_modules`),
  UNIQUE KEY `code_module` (`code_module`),
  KEY `idx_course` (`code_course`),
  KEY `idx_order` (`order_module`),
  KEY `idx_status` (`status_module`),
  KEY `idx_deleted` (`is_deleted`),
  FOREIGN KEY (`code_course`) REFERENCES `wpic_courses`(`code_course`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- ========================================
-- TABLA: LECCIONES
-- ========================================
CREATE TABLE IF NOT EXISTS `wpic_lessons` (
  `id_lesson` int(11) NOT NULL AUTO_INCREMENT,
  `code_lesson` varchar(60) NOT NULL COMMENT 'Código único de la lección',
  `title_lesson` varchar(255) NOT NULL COMMENT 'Título de la lección',
  `description_lesson` text COMMENT 'Descripción de la lección',
  `content_lesson` longtext COMMENT 'Contenido completo de la lección',
  `video_url` text COMMENT 'URL del video de la lección',
  `cover_img` varchar(255) COMMENT 'Imagen de la lección',
  `duration_lesson` int(11) DEFAULT 0 COMMENT 'Duración en minutos',
  `order_lesson` int(11) DEFAULT 0 COMMENT 'Orden dentro del módulo',
  `lesson_type` enum('video','text','quiz','assignment') DEFAULT 'video',
  `is_preview` tinyint(1) DEFAULT 0 COMMENT 'Lección de vista previa gratuita',
  `code_module` varchar(60) NOT NULL COMMENT 'Referencia al módulo',
  `code_course` varchar(255) NOT NULL COMMENT 'Referencia al curso',
  `id_user` int(11) NOT NULL,
  `is_deleted` tinyint(1) DEFAULT 0,
  `date_lesson` datetime DEFAULT CURRENT_TIMESTAMP,
  `updated_at` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id_lesson`),
  UNIQUE KEY `code_lesson` (`code_lesson`),
  KEY `idx_module` (`code_module`),
  KEY `idx_course` (`code_course`),
  KEY `idx_order` (`order_lesson`),
  KEY `idx_type` (`lesson_type`),
  KEY `idx_preview` (`is_preview`),
  KEY `idx_deleted` (`is_deleted`),
  FOREIGN KEY (`code_module`) REFERENCES `wpic_modules`(`code_module`) ON DELETE CASCADE,
  FOREIGN KEY (`code_course`) REFERENCES `wpic_courses`(`code_course`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- ========================================
-- TABLA: OBJETIVOS DE APRENDIZAJE
-- ========================================
CREATE TABLE IF NOT EXISTS `wpic_learn_list` (
  `id_learn` int(11) NOT NULL AUTO_INCREMENT,
  `code_learn` varchar(60) NOT NULL COMMENT 'Código único del objetivo',
  `name_list` varchar(255) NOT NULL COMMENT 'Descripción del objetivo',
  `icon_learn` varchar(100) COMMENT 'Icono del objetivo',
  `order_learn` int(11) DEFAULT 0 COMMENT 'Orden de visualización',
  `code_course` varchar(255) NOT NULL COMMENT 'Referencia al curso',
  `is_deleted` tinyint(1) DEFAULT 0,
  `date_list` datetime DEFAULT CURRENT_TIMESTAMP,
  `updated_at` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id_learn`),
  UNIQUE KEY `code_learn` (`code_learn`),
  KEY `idx_course` (`code_course`),
  KEY `idx_order` (`order_learn`),
  KEY `idx_deleted` (`is_deleted`),
  FOREIGN KEY (`code_course`) REFERENCES `wpic_courses`(`code_course`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- ========================================
-- TABLA: BENEFICIOS DEL CURSO
-- ========================================
CREATE TABLE IF NOT EXISTS `wpic_benefit_list` (
  `id_benefit` int(11) NOT NULL AUTO_INCREMENT,
  `code_benefit` varchar(60) NOT NULL COMMENT 'Código único del beneficio',
  `name_list` varchar(255) NOT NULL COMMENT 'Descripción del beneficio',
  `icon_benefit` varchar(100) COMMENT 'Icono del beneficio',
  `order_benefit` int(11) DEFAULT 0 COMMENT 'Orden de visualización',
  `code_course` varchar(255) NOT NULL COMMENT 'Referencia al curso',
  `is_deleted` tinyint(1) DEFAULT 0,
  `date_list` datetime DEFAULT CURRENT_TIMESTAMP,
  `updated_at` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id_benefit`),
  UNIQUE KEY `code_benefit` (`code_benefit`),
  KEY `idx_course` (`code_course`),
  KEY `idx_order` (`order_benefit`),
  KEY `idx_deleted` (`is_deleted`),
  FOREIGN KEY (`code_course`) REFERENCES `wpic_courses`(`code_course`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- ========================================
-- TABLA: METADATOS ADICIONALES
-- ========================================
CREATE TABLE IF NOT EXISTS `wpic_course_meta` (
  `id_meta` int(11) NOT NULL AUTO_INCREMENT,
  `code_course` varchar(255) NOT NULL,
  `meta_key` varchar(255) NOT NULL,
  `meta_value` longtext,
  `meta_type` enum('string','number','boolean','json','array') DEFAULT 'string',
  `is_public` tinyint(1) DEFAULT 1 COMMENT 'Visible en API pública',
  `created_at` datetime DEFAULT CURRENT_TIMESTAMP,
  `updated_at` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id_meta`),
  UNIQUE KEY `course_meta_unique` (`code_course`, `meta_key`),
  KEY `idx_course` (`code_course`),
  KEY `idx_key` (`meta_key`),
  KEY `idx_public` (`is_public`),
  FOREIGN KEY (`code_course`) REFERENCES `wpic_courses`(`code_course`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- ========================================
-- DATOS DE EJEMPLO PARA TESTING
-- ========================================
INSERT INTO `wpic_courses` (`code_course`, `name_course`, `description_course`, `price_course`, `category_course`, `status_course`, `id_user`) VALUES
('course_1', 'Como hacerte millonario?', 'Aprende las estrategias más efectivas para generar riqueza y alcanzar la libertad financiera.', 299.99, 'finanzas', 'published', 1),
('course_2', 'Marketing Digital Avanzado', 'Domina las técnicas más avanzadas del marketing digital y haz crecer tu negocio online.', 199.99, 'marketing', 'published', 1);
