<!DOCTYPE html>
<html lang="es">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Dashboard - AbilitySeminarsGroup</title>
    
    <!-- Preload critical resources -->
    <link rel="preload" href="./assets/css/asg-design-system.css" as="style">
    <link rel="preload" href="./assets/js/asg-components.js" as="script">
    
    <!-- Styles -->
    <link href="./assets/css/asg-design-system.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.11.3/font/bootstrap-icons.css" rel="stylesheet">
    
    <!-- Meta tags for SEO -->
    <meta name="description" content="Panel de administración de AbilitySeminarsGroup - Gestiona cursos, estudiantes y contenido educativo">
    <meta name="robots" content="noindex, nofollow">
    
    <!-- Favicon -->
    <link rel="icon" type="image/x-icon" href="https://abilityseminarsgroup.com/favicon.ico">
</head>
<body>
    <!-- Sidebar -->
    <aside class="asg-sidebar">
        <div class="asg-sidebar-header">
            <img src="https://abilityseminarsgroup.com/wp-content/uploads/2023/09/Asset-6-4-768x355.png" 
                 alt="AbilitySeminarsGroup" 
                 class="asg-sidebar-logo"
                 style="width: 130px; height: auto;">
        </div>
        
        <nav class="asg-sidebar-nav">
            <a href="#" class="asg-sidebar-item active">
                <i class="bi bi-house asg-sidebar-icon"></i>
                <span>Dashboard</span>
            </a>
            
            <a href="https://abilityseminarsgroup.com/all-courses/" class="asg-sidebar-item">
                <i class="bi bi-collection asg-sidebar-icon"></i>
                <span>Todos los Cursos</span>
            </a>
            
            <a href="https://abilityseminarsgroup.com/new-course/" class="asg-sidebar-item">
                <i class="bi bi-plus-circle asg-sidebar-icon"></i>
                <span>Nuevo Curso</span>
            </a>
            
            <div class="asg-sidebar-divider" style="height: 1px; background: var(--asg-gray-200); margin: var(--asg-space-4) var(--asg-space-6);"></div>
            
            <a href="#" class="asg-sidebar-item">
                <i class="bi bi-people asg-sidebar-icon"></i>
                <span>Estudiantes</span>
            </a>
            
            <a href="#" class="asg-sidebar-item">
                <i class="bi bi-bar-chart asg-sidebar-icon"></i>
                <span>Analíticas</span>
            </a>
            
            <a href="#" class="asg-sidebar-item">
                <i class="bi bi-gear asg-sidebar-icon"></i>
                <span>Configuración</span>
            </a>
        </nav>
    </aside>
    
    <!-- Sidebar Overlay (Mobile) -->
    <div class="asg-sidebar-overlay"></div>
    
    <!-- Main Content -->
    <main class="asg-main">
        <!-- Header -->
        <header class="asg-header">
            <div style="display: flex; align-items: center; justify-content: space-between;">
                <div style="display: flex; align-items: center; gap: var(--asg-space-4);">
                    <button class="asg-sidebar-toggle asg-btn asg-btn-secondary" style="display: none;">
                        <i class="bi bi-list"></i>
                    </button>
                    <h1 style="margin: 0; font-size: var(--asg-text-2xl); font-weight: var(--asg-font-semibold); color: var(--asg-gray-900);">
                        Dashboard
                    </h1>
                </div>
                
                <div style="display: flex; align-items: center; gap: var(--asg-space-3);">
                    <button class="asg-btn asg-btn-secondary" id="refreshBtn">
                        <i class="bi bi-arrow-clockwise"></i>
                        <span>Actualizar</span>
                    </button>
                    
                    <div class="asg-badge asg-badge-success">
                        <i class="bi bi-circle-fill" style="font-size: 0.5rem; margin-right: var(--asg-space-1);"></i>
                        En línea
                    </div>
                </div>
            </div>
        </header>
        
        <!-- Content -->
        <div class="asg-content">
            <!-- Stats Cards -->
            <div class="stats-grid" style="display: grid; grid-template-columns: repeat(auto-fit, minmax(280px, 1fr)); gap: var(--asg-space-6); margin-bottom: var(--asg-space-8);">
                <!-- Total Courses -->
                <div class="asg-card">
                    <div class="asg-card-body">
                        <div style="display: flex; align-items: center; justify-content: space-between;">
                            <div>
                                <p style="margin: 0; font-size: var(--asg-text-sm); color: var(--asg-gray-600); font-weight: var(--asg-font-medium);">
                                    Total de Cursos
                                </p>
                                <p style="margin: var(--asg-space-1) 0 0 0; font-size: var(--asg-text-3xl); font-weight: var(--asg-font-bold); color: var(--asg-gray-900);" id="totalCourses">
                                    --
                                </p>
                            </div>
                            <div style="width: 3rem; height: 3rem; background: var(--asg-primary-100); border-radius: var(--asg-radius-lg); display: flex; align-items: center; justify-content: center;">
                                <i class="bi bi-collection" style="font-size: 1.5rem; color: var(--asg-primary-600);"></i>
                            </div>
                        </div>
                        <div style="margin-top: var(--asg-space-4); display: flex; align-items: center; gap: var(--asg-space-2);">
                            <span class="asg-badge asg-badge-success" id="coursesChange">+0%</span>
                            <span style="font-size: var(--asg-text-sm); color: var(--asg-gray-600);">vs mes anterior</span>
                        </div>
                    </div>
                </div>
                
                <!-- Published Courses -->
                <div class="asg-card">
                    <div class="asg-card-body">
                        <div style="display: flex; align-items: center; justify-content: space-between;">
                            <div>
                                <p style="margin: 0; font-size: var(--asg-text-sm); color: var(--asg-gray-600); font-weight: var(--asg-font-medium);">
                                    Cursos Publicados
                                </p>
                                <p style="margin: var(--asg-space-1) 0 0 0; font-size: var(--asg-text-3xl); font-weight: var(--asg-font-bold); color: var(--asg-gray-900);" id="publishedCourses">
                                    --
                                </p>
                            </div>
                            <div style="width: 3rem; height: 3rem; background: var(--asg-success-light); border-radius: var(--asg-radius-lg); display: flex; align-items: center; justify-content: center;">
                                <i class="bi bi-check-circle" style="font-size: 1.5rem; color: var(--asg-success);"></i>
                            </div>
                        </div>
                        <div style="margin-top: var(--asg-space-4); display: flex; align-items: center; gap: var(--asg-space-2);">
                            <span class="asg-badge asg-badge-primary" id="publishedPercentage">0%</span>
                            <span style="font-size: var(--asg-text-sm); color: var(--asg-gray-600);">del total</span>
                        </div>
                    </div>
                </div>
                
                <!-- Draft Courses -->
                <div class="asg-card">
                    <div class="asg-card-body">
                        <div style="display: flex; align-items: center; justify-content: space-between;">
                            <div>
                                <p style="margin: 0; font-size: var(--asg-text-sm); color: var(--asg-gray-600); font-weight: var(--asg-font-medium);">
                                    Borradores
                                </p>
                                <p style="margin: var(--asg-space-1) 0 0 0; font-size: var(--asg-text-3xl); font-weight: var(--asg-font-bold); color: var(--asg-gray-900);" id="draftCourses">
                                    --
                                </p>
                            </div>
                            <div style="width: 3rem; height: 3rem; background: var(--asg-warning-light); border-radius: var(--asg-radius-lg); display: flex; align-items: center; justify-content: center;">
                                <i class="bi bi-pencil" style="font-size: 1.5rem; color: var(--asg-warning);"></i>
                            </div>
                        </div>
                        <div style="margin-top: var(--asg-space-4); display: flex; align-items: center; gap: var(--asg-space-2);">
                            <span class="asg-badge asg-badge-warning" id="draftPercentage">0%</span>
                            <span style="font-size: var(--asg-text-sm); color: var(--asg-gray-600);">pendientes</span>
                        </div>
                    </div>
                </div>
                
                <!-- Total Revenue -->
                <div class="asg-card">
                    <div class="asg-card-body">
                        <div style="display: flex; align-items: center; justify-content: space-between;">
                            <div>
                                <p style="margin: 0; font-size: var(--asg-text-sm); color: var(--asg-gray-600); font-weight: var(--asg-font-medium);">
                                    Ingresos Potenciales
                                </p>
                                <p style="margin: var(--asg-space-1) 0 0 0; font-size: var(--asg-text-3xl); font-weight: var(--asg-font-bold); color: var(--asg-gray-900);" id="totalRevenue">
                                    --
                                </p>
                            </div>
                            <div style="width: 3rem; height: 3rem; background: var(--asg-info-light); border-radius: var(--asg-radius-lg); display: flex; align-items: center; justify-content: center;">
                                <i class="bi bi-currency-euro" style="font-size: 1.5rem; color: var(--asg-info);"></i>
                            </div>
                        </div>
                        <div style="margin-top: var(--asg-space-4); display: flex; align-items: center; gap: var(--asg-space-2);">
                            <span class="asg-badge asg-badge-gray" id="avgPrice">€0</span>
                            <span style="font-size: var(--asg-text-sm); color: var(--asg-gray-600);">precio promedio</span>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- Quick Actions -->
            <div class="asg-card" style="margin-bottom: var(--asg-space-8);">
                <div class="asg-card-header">
                    <h2 style="margin: 0; font-size: var(--asg-text-lg); font-weight: var(--asg-font-semibold); color: var(--asg-gray-900);">
                        Acciones Rápidas
                    </h2>
                </div>
                <div class="asg-card-body">
                    <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: var(--asg-space-4);">
                        <a href="https://abilityseminarsgroup.com/new-course/" class="asg-btn asg-btn-primary asg-btn-lg">
                            <i class="bi bi-plus-circle"></i>
                            <span>Crear Nuevo Curso</span>
                        </a>
                        
                        <a href="https://abilityseminarsgroup.com/all-courses/" class="asg-btn asg-btn-secondary asg-btn-lg">
                            <i class="bi bi-collection"></i>
                            <span>Ver Todos los Cursos</span>
                        </a>
                        
                        <button class="asg-btn asg-btn-secondary asg-btn-lg" id="importBtn">
                            <i class="bi bi-upload"></i>
                            <span>Importar Contenido</span>
                        </button>
                        
                        <button class="asg-btn asg-btn-secondary asg-btn-lg" id="exportBtn">
                            <i class="bi bi-download"></i>
                            <span>Exportar Datos</span>
                        </button>
                    </div>
                </div>
            </div>
            
            <!-- Recent Courses -->
            <div class="asg-card">
                <div class="asg-card-header">
                    <div style="display: flex; align-items: center; justify-content: space-between;">
                        <h2 style="margin: 0; font-size: var(--asg-text-lg); font-weight: var(--asg-font-semibold); color: var(--asg-gray-900);">
                            Cursos Recientes
                        </h2>
                        <a href="https://abilityseminarsgroup.com/all-courses/" class="asg-btn asg-btn-sm asg-btn-secondary">
                            Ver todos
                        </a>
                    </div>
                </div>
                <div class="asg-card-body">
                    <div id="recentCourses" style="display: flex; flex-direction: column; gap: var(--asg-space-4);">
                        <!-- Loading state -->
                        <div class="loading-skeleton" style="display: flex; align-items: center; gap: var(--asg-space-4); padding: var(--asg-space-4); border: 1px solid var(--asg-gray-200); border-radius: var(--asg-radius);">
                            <div style="width: 4rem; height: 4rem; background: var(--asg-gray-200); border-radius: var(--asg-radius); animation: pulse 2s infinite;"></div>
                            <div style="flex: 1;">
                                <div style="height: 1rem; background: var(--asg-gray-200); border-radius: var(--asg-radius-sm); margin-bottom: var(--asg-space-2); animation: pulse 2s infinite;"></div>
                                <div style="height: 0.75rem; background: var(--asg-gray-200); border-radius: var(--asg-radius-sm); width: 60%; animation: pulse 2s infinite;"></div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </main>
    
    <!-- Scripts -->
    <script src="./assets/js/asg-components.js"></script>
    <script src="./assets/js/dashboard-v2.js"></script>
    
    <!-- Pulse animation for loading -->
    <style>
        @keyframes pulse {
            0%, 100% { opacity: 1; }
            50% { opacity: 0.5; }
        }
        
        @media (max-width: 1024px) {
            .asg-sidebar-toggle {
                display: flex !important;
            }
        }
    </style>
</body>
</html>
