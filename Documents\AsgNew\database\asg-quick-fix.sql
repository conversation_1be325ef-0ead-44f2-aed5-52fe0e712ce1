-- ========================================
-- SOLUCIÓN RÁPIDA - ELIMINAR Y RECREAR
-- ========================================
-- 
-- INSTRUCCIONES:
-- 1. Ejecuta este archivo completo
-- 2. <PERSON><PERSON>rar<PERSON> errores y recreará todo limpio
-- ========================================

-- Eliminar tablas en orden correcto (si existen)
SET FOREIGN_KEY_CHECKS = 0;
DROP TABLE IF EXISTS `wpic_course_meta`;
DROP TABLE IF EXISTS `wpic_benefit_list`;
DROP TABLE IF EXISTS `wpic_learn_list`;
DROP TABLE IF EXISTS `wpic_lessons`;
DROP TABLE IF EXISTS `wpic_modules`;
DROP TABLE IF EXISTS `wpic_courses`;
SET FOREIGN_KEY_CHECKS = 1;

-- Crear tabla principal
CREATE TABLE `wpic_courses` (
  `id_course` int(11) NOT NULL AUTO_INCREMENT,
  `code_course` varchar(255) NOT NULL,
  `name_course` varchar(255) NOT NULL,
  `description_course` text,
  `cover_img` text,
  `price_course` decimal(10,2) DEFAULT 0.00,
  `date_course` datetime DEFAULT CURRENT_TIMESTAMP,
  `category_course` varchar(100) DEFAULT 'general',
  `duration_course` int(11) DEFAULT 0,
  `language_course` varchar(10) DEFAULT 'es',
  `status_course` enum('draft','published','archived') DEFAULT 'draft',
  `featured_course` tinyint(1) DEFAULT 0,
  `enrollment_count` int(11) DEFAULT 0,
  `rating_average` decimal(3,2) DEFAULT 0.00,
  `rating_count` int(11) DEFAULT 0,
  `seo_title` varchar(255),
  `seo_description` text,
  `seo_keywords` text,
  `id_user` int(11) NOT NULL,
  `is_deleted` tinyint(1) DEFAULT 0,
  `created_at` datetime DEFAULT CURRENT_TIMESTAMP,
  `updated_at` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id_course`),
  UNIQUE KEY `code_course` (`code_course`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- Crear módulos
CREATE TABLE `wpic_modules` (
  `id_modules` int(11) NOT NULL AUTO_INCREMENT,
  `code_module` varchar(60) NOT NULL,
  `title_module` varchar(255) NOT NULL,
  `description_module` text,
  `cover_img` varchar(255),
  `duration_module` int(11) DEFAULT 0,
  `order_module` int(11) DEFAULT 0,
  `status_module` enum('draft','published','archived') DEFAULT 'draft',
  `code_course` varchar(255) NOT NULL,
  `id_user` int(11) NOT NULL,
  `is_deleted` tinyint(1) DEFAULT 0,
  `date_module` datetime DEFAULT CURRENT_TIMESTAMP,
  `updated_at` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id_modules`),
  UNIQUE KEY `code_module` (`code_module`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- Crear lecciones
CREATE TABLE `wpic_lessons` (
  `id_lesson` int(11) NOT NULL AUTO_INCREMENT,
  `code_lesson` varchar(60) NOT NULL,
  `title_lesson` varchar(255) NOT NULL,
  `description_lesson` text,
  `content_lesson` longtext,
  `video_url` text,
  `cover_img` varchar(255),
  `duration_lesson` int(11) DEFAULT 0,
  `order_lesson` int(11) DEFAULT 0,
  `lesson_type` enum('video','text','quiz','assignment') DEFAULT 'video',
  `is_preview` tinyint(1) DEFAULT 0,
  `code_module` varchar(60) NOT NULL,
  `code_course` varchar(255) NOT NULL,
  `id_user` int(11) NOT NULL,
  `is_deleted` tinyint(1) DEFAULT 0,
  `date_lesson` datetime DEFAULT CURRENT_TIMESTAMP,
  `updated_at` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id_lesson`),
  UNIQUE KEY `code_lesson` (`code_lesson`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- Crear objetivos
CREATE TABLE `wpic_learn_list` (
  `id_learn` int(11) NOT NULL AUTO_INCREMENT,
  `code_learn` varchar(60) NOT NULL,
  `name_list` varchar(255) NOT NULL,
  `icon_learn` varchar(100),
  `order_learn` int(11) DEFAULT 0,
  `code_course` varchar(255) NOT NULL,
  `is_deleted` tinyint(1) DEFAULT 0,
  `date_list` datetime DEFAULT CURRENT_TIMESTAMP,
  `updated_at` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id_learn`),
  UNIQUE KEY `code_learn` (`code_learn`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- Crear beneficios
CREATE TABLE `wpic_benefit_list` (
  `id_benefit` int(11) NOT NULL AUTO_INCREMENT,
  `code_benefit` varchar(60) NOT NULL,
  `name_list` varchar(255) NOT NULL,
  `icon_benefit` varchar(100),
  `order_benefit` int(11) DEFAULT 0,
  `code_course` varchar(255) NOT NULL,
  `is_deleted` tinyint(1) DEFAULT 0,
  `date_list` datetime DEFAULT CURRENT_TIMESTAMP,
  `updated_at` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id_benefit`),
  UNIQUE KEY `code_benefit` (`code_benefit`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- Crear metadatos
CREATE TABLE `wpic_course_meta` (
  `id_meta` int(11) NOT NULL AUTO_INCREMENT,
  `code_course` varchar(255) NOT NULL,
  `meta_key` varchar(255) NOT NULL,
  `meta_value` longtext,
  `meta_type` enum('string','number','boolean','json','array') DEFAULT 'string',
  `is_public` tinyint(1) DEFAULT 1,
  `created_at` datetime DEFAULT CURRENT_TIMESTAMP,
  `updated_at` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id_meta`),
  UNIQUE KEY `course_meta_unique` (`code_course`, `meta_key`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- Insertar datos de ejemplo
INSERT INTO `wpic_courses` (`code_course`, `name_course`, `description_course`, `price_course`, `category_course`, `status_course`, `id_user`) VALUES
('course_1', 'Como hacerte millonario?', 'Aprende las estrategias más efectivas para generar riqueza y alcanzar la libertad financiera.', 299.99, 'finanzas', 'published', 1),
('course_2', 'Marketing Digital Avanzado', 'Domina las técnicas más avanzadas del marketing digital y haz crecer tu negocio online.', 199.99, 'marketing', 'published', 1),
('course_3', 'Desarrollo Personal Integral', 'Transforma tu vida con técnicas probadas de crecimiento personal y liderazgo.', 149.99, 'desarrollo-personal', 'draft', 1);

-- Verificar
SELECT 'Base de datos creada correctamente' as resultado;
SELECT COUNT(*) as total_cursos FROM wpic_courses;
