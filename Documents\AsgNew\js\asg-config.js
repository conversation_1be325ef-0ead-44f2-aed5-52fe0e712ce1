/**
 * ========================================
 * ASG CONFIGURATION v2.0
 * ========================================
 * 
 * Descripción: Configuración central del sistema ASG
 * Incluye: URLs, configuraciones, constantes globales
 * 
 * Autor: ASG Team
 * Fecha: 2025-01-04
 * Versión: 2.0.0 - CONFIGURACIÓN CENTRALIZADA
 * Ubicación: js/asg-config.js (movido desde config/)
 */

/**
 * ========================================
 * CONFIGURACIÓN PRINCIPAL
 * ========================================
 */
window.ASG_CONFIG = {
    // Información del sistema
    version: '2.0.0',
    name: 'AbilitySeminarsGroup Course Management',
    
    // URLs del sistema
    urls: {
        // Base URLs
        baseUrl: 'https://abilityseminarsgroup.com',
        apiBaseUrl: 'https://abilityseminarsgroup.com/wp-json/asg/v1',
        
        // Admin URLs
        dashboard: 'https://abilityseminarsgroup.com/admin-dashboard/',
        allCourses: 'https://abilityseminarsgroup.com/all-courses/',
        newCourse: 'https://abilityseminarsgroup.com/new-course/',
        editCourse: 'https://abilityseminarsgroup.com/edit-course/',
        
        // Public URLs
        publicCourse: '/course',
        
        // Assets
        logoUrl: 'https://abilityseminarsgroup.com/wp-content/uploads/2023/09/Asset-6-4-768x355.png',
        defaultCourseImage: 'https://images.unsplash.com/photo-1516321318423-f06f85e504b3?w=400&h=300&fit=crop'
    },
    
    // Configuración de API
    api: {
        timeout: 30000, // 30 segundos
        retries: 3,
        endpoints: {
            // Públicos (basados en asg-course-endpoints.php)
            courses: '/courses',
            course: '/courses/{code}',
            dashboardStats: '/dashboard/stats',
            categories: '/courses/categories',
            uploadImage: '/courses/upload-image',
            
            // Búsqueda y filtros
            searchCourses: '/courses/search',
            filterCourses: '/courses/filter',
            
            // Administrativos (requieren autenticación)
            adminCourses: '/admin/courses',
            adminCourse: '/admin/courses/{code}',
            saveDraft: '/courses/draft',
            validateCourse: '/courses/validate'
        }
    },
    
    // Configuración de UI
    ui: {
        // Breakpoints responsive
        breakpoints: {
            mobile: 640,
            tablet: 768,
            desktop: 1024,
            xl: 1280,
            xxl: 1536
        },
        
        // Animaciones
        animations: {
            fast: 150,
            normal: 200,
            slow: 300,
            verySlow: 500
        },
        
        // Paginación
        pagination: {
            defaultPerPage: 12,
            maxPerPage: 100,
            maxVisiblePages: 5
        },
        
        // Notificaciones
        notifications: {
            duration: 5000,
            maxNotifications: 5,
            position: 'top-right'
        },
        
        // Auto-guardado
        autoSave: {
            interval: 30000, // 30 segundos para formularios
            editInterval: 120000 // 2 minutos para editor
        }
    },
    
    // Configuración de archivos
    files: {
        maxSize: 5 * 1024 * 1024, // 5MB
        allowedTypes: ['image/jpeg', 'image/png', 'image/gif', 'image/webp'],
        allowedExtensions: ['.jpg', '.jpeg', '.png', '.gif', '.webp']
    },
    
    // Configuración de validaciones
    validation: {
        course: {
            title: {
                minLength: 3,
                maxLength: 255
            },
            description: {
                minLength: 10,
                maxLength: 1000
            },
            price: {
                min: 0,
                max: 9999.99
            }
        },
        module: {
            title: {
                minLength: 3,
                maxLength: 255
            },
            description: {
                maxLength: 500
            }
        },
        lesson: {
            title: {
                minLength: 3,
                maxLength: 255
            },
            content: {
                maxLength: 10000
            }
        }
    },
    
    // Configuración de categorías
    categories: {
        finanzas: {
            label: 'Finanzas',
            color: '#10b981',
            icon: 'bi-currency-euro'
        },
        marketing: {
            label: 'Marketing',
            color: '#f59e0b',
            icon: 'bi-megaphone'
        },
        'desarrollo-personal': {
            label: 'Desarrollo Personal',
            color: '#8b5cf6',
            icon: 'bi-person-check'
        },
        tecnologia: {
            label: 'Tecnología',
            color: '#06b6d4',
            icon: 'bi-laptop'
        },
        negocios: {
            label: 'Negocios',
            color: '#ef4444',
            icon: 'bi-briefcase'
        },
        salud: {
            label: 'Salud y Bienestar',
            color: '#84cc16',
            icon: 'bi-heart'
        },
        educacion: {
            label: 'Educación',
            color: '#6366f1',
            icon: 'bi-book'
        },
        arte: {
            label: 'Arte y Creatividad',
            color: '#ec4899',
            icon: 'bi-palette'
        }
    },
    
    // Configuración de idiomas
    languages: {
        es: {
            label: 'Español',
            flag: '🇪🇸',
            locale: 'es-ES'
        },
        en: {
            label: 'English',
            flag: '🇺🇸',
            locale: 'en-US'
        },
        fr: {
            label: 'Français',
            flag: '🇫🇷',
            locale: 'fr-FR'
        },
        pt: {
            label: 'Português',
            flag: '🇵🇹',
            locale: 'pt-PT'
        }
    },
    
    // Estados de curso
    courseStates: {
        draft: {
            label: 'Borrador',
            color: '#f59e0b',
            badge: 'asg-badge-warning'
        },
        published: {
            label: 'Publicado',
            color: '#10b981',
            badge: 'asg-badge-success'
        },
        archived: {
            label: 'Archivado',
            color: '#6b7280',
            badge: 'asg-badge-gray'
        }
    },
    
    // Configuración de desarrollo
    development: {
        debug: true,
        mockData: true,
        logLevel: 'info', // 'debug', 'info', 'warn', 'error'
        enableAutoSave: true,
        enableNotifications: true
    },
    
    // Configuración de producción
    production: {
        debug: false,
        mockData: false,
        logLevel: 'error',
        enableAutoSave: true,
        enableNotifications: true,
        enableAnalytics: true
    }
};

/**
 * ========================================
 * UTILIDADES DE CONFIGURACIÓN
 * ========================================
 */
window.ASG_CONFIG.utils = {
    /**
     * Obtener configuración según entorno
     */
    getConfig: function(key) {
        const isProduction = window.location.hostname !== 'localhost' && 
                           !window.location.hostname.includes('dev');
        
        const envConfig = isProduction ? this.production : this.development;
        
        return envConfig[key] !== undefined ? envConfig[key] : this[key];
    },
    
    /**
     * Construir URL de API
     */
    buildApiUrl: function(endpoint, params = {}) {
        let url = this.urls.apiBaseUrl + endpoint;
        
        // Reemplazar parámetros en la URL
        Object.keys(params).forEach(key => {
            url = url.replace(`{${key}}`, params[key]);
        });
        
        return url;
    },
    
    /**
     * Obtener endpoint de API
     */
    getApiEndpoint: function(endpointName, params = {}) {
        const endpoint = this.api.endpoints[endpointName];
        if (!endpoint) {
            throw new Error(`Endpoint '${endpointName}' no encontrado`);
        }
        
        return this.buildApiUrl(endpoint, params);
    },
    
    /**
     * Obtener configuración de categoría
     */
    getCategoryConfig: function(categoryCode) {
        return this.categories[categoryCode] || {
            label: categoryCode,
            color: '#6b7280',
            icon: 'bi-tag'
        };
    },
    
    /**
     * Obtener configuración de idioma
     */
    getLanguageConfig: function(languageCode) {
        return this.languages[languageCode] || this.languages.es;
    },
    
    /**
     * Obtener configuración de estado
     */
    getStateConfig: function(stateCode) {
        return this.courseStates[stateCode] || this.courseStates.draft;
    },
    
    /**
     * Validar archivo
     */
    validateFile: function(file) {
        const errors = [];
        
        // Validar tamaño
        if (file.size > this.files.maxSize) {
            errors.push(`El archivo no puede superar ${this.files.maxSize / (1024 * 1024)}MB`);
        }
        
        // Validar tipo
        if (!this.files.allowedTypes.includes(file.type)) {
            errors.push('Tipo de archivo no permitido');
        }
        
        return {
            valid: errors.length === 0,
            errors: errors
        };
    },
    
    /**
     * Formatear moneda
     */
    formatCurrency: function(amount, currency = 'EUR', locale = 'es-ES') {
        return new Intl.NumberFormat(locale, {
            style: 'currency',
            currency: currency
        }).format(amount);
    },
    
    /**
     * Formatear fecha
     */
    formatDate: function(date, locale = 'es-ES', options = {}) {
        const defaultOptions = {
            year: 'numeric',
            month: 'long',
            day: 'numeric'
        };

        return new Intl.DateTimeFormat(locale, { ...defaultOptions, ...options })
               .format(new Date(date));
    },

    /**
     * Log con nivel
     */
    log: function(level, message, data = null) {
        const logLevel = this.getConfig('logLevel');
        const levels = ['debug', 'info', 'warn', 'error'];

        if (levels.indexOf(level) >= levels.indexOf(logLevel)) {
            const timestamp = new Date().toISOString();
            const prefix = `[ASG ${level.toUpperCase()}] ${timestamp}:`;

            switch (level) {
                case 'debug':
                    console.debug(prefix, message, data);
                    break;
                case 'info':
                    console.info(prefix, message, data);
                    break;
                case 'warn':
                    console.warn(prefix, message, data);
                    break;
                case 'error':
                    console.error(prefix, message, data);
                    break;
            }
        }
    },

    /**
     * Mostrar notificación toast
     */
    showNotification: function(message, type = 'info', duration = 5000) {
        if (!this.getConfig('enableNotifications')) return;

        const toast = this.createToast(message, type);
        const container = this.getToastContainer();

        container.appendChild(toast);

        // Auto-remove
        setTimeout(() => {
            if (toast.parentNode) {
                toast.parentNode.removeChild(toast);
            }
        }, duration);
    },

    /**
     * Crear contenedor de toasts
     */
    getToastContainer: function() {
        let container = document.getElementById('asg-toast-container');

        if (!container) {
            container = document.createElement('div');
            container.id = 'asg-toast-container';
            container.style.cssText = `
                position: fixed;
                top: 90px;
                right: 20px;
                z-index: 9999;
                display: flex;
                flex-direction: column;
                gap: 10px;
                max-width: 400px;
            `;
            document.body.appendChild(container);
        }

        return container;
    },

    /**
     * Crear toast individual
     */
    createToast: function(message, type) {
        const toast = document.createElement('div');
        const colors = {
            success: '#10b981',
            error: '#ef4444',
            warning: '#f59e0b',
            info: '#06b6d4'
        };

        const icons = {
            success: '✅',
            error: '❌',
            warning: '⚠️',
            info: 'ℹ️'
        };

        toast.style.cssText = `
            background: ${colors[type] || colors.info};
            color: white;
            padding: 12px 16px;
            border-radius: 8px;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
            animation: asgSlideIn 0.3s ease-out;
            cursor: pointer;
            transition: transform 0.2s ease;
        `;

        toast.innerHTML = `
            <div style="display: flex; align-items: center; gap: 8px;">
                <span style="font-size: 1.1em;">${icons[type] || icons.info}</span>
                <span style="flex: 1;">${message}</span>
                <span style="opacity: 0.7; font-size: 0.9em;">×</span>
            </div>
        `;

        // Permitir cerrar al hacer clic
        toast.addEventListener('click', () => {
            if (toast.parentNode) {
                toast.style.animation = 'asgSlideOut 0.3s ease-in';
                setTimeout(() => {
                    if (toast.parentNode) {
                        toast.parentNode.removeChild(toast);
                    }
                }, 300);
            }
        });

        // Hover effect
        toast.addEventListener('mouseenter', () => {
            toast.style.transform = 'translateX(-5px)';
        });

        toast.addEventListener('mouseleave', () => {
            toast.style.transform = 'translateX(0)';
        });

        return toast;
    },

    /**
     * Truncar texto
     */
    truncateText: function(text, maxLength = 100) {
        if (!text || text.length <= maxLength) return text;
        return text.substring(0, maxLength) + '...';
    },

    /**
     * Formatear número
     */
    formatNumber: function(number, locale = 'es-ES') {
        return new Intl.NumberFormat(locale).format(number || 0);
    },

    /**
     * Debounce function
     */
    debounce: function(func, wait) {
        let timeout;
        return function executedFunction(...args) {
            const later = () => {
                clearTimeout(timeout);
                func(...args);
            };
            clearTimeout(timeout);
            timeout = setTimeout(later, wait);
        };
    },

    /**
     * Generar ID único
     */
    generateId: function() {
        return Date.now().toString(36) + Math.random().toString(36).substr(2);
    },

    /**
     * Agregar estilos CSS para toasts
     */
    addToastStyles: function() {
        if (document.getElementById('asg-toast-styles')) return;

        const style = document.createElement('style');
        style.id = 'asg-toast-styles';
        style.textContent = `
            @keyframes asgSlideIn {
                from { transform: translateX(100%); opacity: 0; }
                to { transform: translateX(0); opacity: 1; }
            }

            @keyframes asgSlideOut {
                from { transform: translateX(0); opacity: 1; }
                to { transform: translateX(100%); opacity: 0; }
            }

            @keyframes asgSpin {
                from { transform: rotate(0deg); }
                to { transform: rotate(360deg); }
            }

            .asg-spin { animation: asgSpin 1s linear infinite; }

            .asg-loading-skeleton {
                background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
                background-size: 200% 100%;
                animation: asgLoading 1.5s infinite;
                border-radius: 4px;
            }

            @keyframes asgLoading {
                0% { background-position: 200% 0; }
                100% { background-position: -200% 0; }
            }

            .asg-fade-in { animation: asgFadeIn 0.3s ease-out; }

            @keyframes asgFadeIn {
                from { opacity: 0; transform: translateY(10px); }
                to { opacity: 1; transform: translateY(0); }
            }

            .asg-badge-success {
                background-color: #d1fae5; color: #065f46;
                padding: 0.25rem 0.75rem; border-radius: 9999px;
                font-size: 0.75rem; font-weight: 500; text-transform: uppercase;
            }

            .asg-badge-warning {
                background-color: #fef3c7; color: #92400e;
                padding: 0.25rem 0.75rem; border-radius: 9999px;
                font-size: 0.75rem; font-weight: 500; text-transform: uppercase;
            }

            .asg-badge-gray {
                background-color: #f3f4f6; color: #374151;
                padding: 0.25rem 0.75rem; border-radius: 9999px;
                font-size: 0.75rem; font-weight: 500; text-transform: uppercase;
            }
        `;

        document.head.appendChild(style);
    }
};

/**
 * ========================================
 * INICIALIZACIÓN
 * ========================================
 */
document.addEventListener('DOMContentLoaded', function() {
    // Aplicar configuración al objeto ASG global si existe
    if (window.ASG && window.ASG.config) {
        Object.assign(window.ASG.config, window.ASG_CONFIG);
    }

    // Agregar estilos CSS para toasts
    ASG_CONFIG.utils.addToastStyles();

    // Log de inicialización
    ASG_CONFIG.utils.log('info', 'ASG Configuration loaded', {
        version: ASG_CONFIG.version,
        environment: ASG_CONFIG.utils.getConfig('debug') ? 'development' : 'production'
    });
});

/**
 * ========================================
 * EXPORTAR CONFIGURACIÓN
 * ========================================
 */
if (typeof module !== 'undefined' && module.exports) {
    module.exports = ASG_CONFIG;
}

console.log('🚀 ASG Config v2.0.0 loaded from js/ folder');
