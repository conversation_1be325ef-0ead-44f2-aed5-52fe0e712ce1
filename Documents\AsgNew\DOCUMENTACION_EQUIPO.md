# 📚 Documentación del Sistema AbilitySeminarsGroup

## 🎯 Descripción General

Sistema completo de gestión de cursos en línea desarrollado para **AbilitySeminarsGroup**. Incluye dashboard administrativo, gestión de cursos, creación de nuevos cursos y edición avanzada con funcionalidades intuitivas para administradores.

## 📁 Estructura del Proyecto

```
Documents/AsgNew/
├── dashboard.html          # Panel principal de administración
├── all-courses.html        # Visualización y gestión de todos los cursos
├── new-course.html         # Creación de nuevos cursos
├── edit-course.html        # Edición avanzada de cursos existentes
└── DOCUMENTACION_EQUIPO.md # Esta documentación
```

---

## 🏗️ Arquitectura del Sistema

### **Estructura Modular por Página**

#### **📊 Dashboard (dashboard.html)**
```javascript
├── DashboardSidebarManager     // Gestión del sidebar del dashboard
├── DashboardNavigationManager  // Navegación y menús del dashboard
└── Eventos Globales           // Responsive y eventos de ventana
```

#### **📚 Gestión de Cursos (all-courses.html)**
```javascript
├── CoursesSidebarManager  // Gestión del sidebar de cursos
├── CoursesDataManager     // Carga y gestión de datos de cursos
├── CoursesUIManager       // Interfaz de usuario de cursos
└── Funciones Globales     // Navegación y edición de cursos
```

#### **➕ Nuevo Curso (new-course.html)**
```javascript
├── NewCourseSidebarManager  // Gestión del sidebar de creación
├── NewCourseFormManager     // Gestión del formulario multi-paso
├── NewCourseStepManager     // Navegación entre pasos del formulario
└── Eventos Globales         // Responsive y validaciones
```

#### **✏️ Edición de Curso (edit-course.html)**
```javascript
├── SidebarManager       // Gestión del menú lateral
├── CourseLoader         // Carga de datos de cursos
├── ModuleManager        // Gestión de módulos del curso
├── ImageUploadManager   // Subida de imágenes
└── Funciones Globales   // Funciones de edición principales
```

---

## 📄 Páginas del Sistema

### **📊 Dashboard Principal (dashboard.html)**
**URL:** `https://abilityseminarsgroup.com/admin-dashboard/`

**Funcionalidades:**
- ✅ Panel de control principal
- ✅ Tarjetas de acceso rápido
- ✅ Navegación responsive
- ✅ Sidebar colapsable

**Módulos Principales:**
- `DashboardSidebarManager`: Gestión del sidebar
- `DashboardNavigationManager`: Navegación y menús

### **📚 Gestión de Cursos (all-courses.html)**
**URL:** `https://abilityseminarsgroup.com/all-courses/`

**Funcionalidades:**
- ✅ Visualización en grid de cursos
- ✅ Búsqueda y filtrado
- ✅ Navegación a edición
- ✅ Estados vacíos

**Módulos Principales:**
- `CoursesSidebarManager`: Gestión del sidebar
- `CoursesDataManager`: Carga de datos desde localStorage
- `CoursesUIManager`: Gestión de interfaz

### **➕ Creación de Cursos (new-course.html)**
**URL:** `https://abilityseminarsgroup.com/new-course/`

**Funcionalidades:**
- ✅ Formulario multi-paso (4 pasos)
- ✅ Validaciones en tiempo real
- ✅ Subida de archivos
- ✅ Configuración de precios
- ✅ Preview del curso

**Módulos Principales:**
- `NewCourseSidebarManager`: Gestión del sidebar
- `NewCourseFormManager`: Validaciones del formulario
- `NewCourseStepManager`: Navegación entre pasos

### **✏️ Edición de Cursos (edit-course.html)**
**URL:** `https://abilityseminarsgroup.com/edit-course/?id={courseId}`

**Funcionalidades:**
- ✅ Edición completa de cursos
- ✅ Gestión de módulos con drag & drop
- ✅ Subida de imágenes
- ✅ Configuraciones avanzadas

**Módulos Principales:**
- `SidebarManager`: Gestión del sidebar
- `CourseLoader`: Carga de datos específicos
- `ModuleManager`: Gestión completa de módulos
- `ImageUploadManager`: Subida de imágenes

---

## 🔧 Módulos del Sistema

### **1. Gestores de Sidebar (Patrón Común)**
**Responsabilidad:** Manejo del menú lateral responsive en todas las páginas

**Implementaciones:**
- `DashboardSidebarManager` (dashboard.html)
- `CoursesSidebarManager` (all-courses.html)
- `NewCourseSidebarManager` (new-course.html)
- `SidebarManager` (edit-course.html)

```javascript
const [Prefix]SidebarManager = {
    init(sidebar, overlay, bodyContent, toggleBtn1, toggleBtn2) // Inicialización
    toggle()                                                    // Alternar visibilidad
    toggleDesktopOrCloseMobile()                               // Toggle específico
    closeMobile()                                              // Cerrar en móvil
}
```

**Funcionalidades Comunes:**
- ✅ Responsive (móvil/desktop)
- ✅ Toggle inteligente según dispositivo
- ✅ Cierre automático en móvil
- ✅ Gestión de overlay

### **2. Gestores de Datos**
**Responsabilidad:** Carga y gestión de datos específicos

#### **CourseLoader (edit-course.html)**
```javascript
const CourseLoader = {
    init()                    // Inicialización y detección de ID
    loadCourseData(courseId)  // Carga datos del curso específico
}
```

#### **CoursesDataManager (all-courses.html)**
```javascript
const CoursesDataManager = {
    init()                    // Inicialización
    loadCourses()            // Carga todos los cursos
    createCourseCard(course) // Crea tarjeta de curso
    setupSubmenuToggles()    // Configura menús
}
```

### **3. Gestores de Formularios**
**Responsabilidad:** Validación y gestión de formularios

#### **NewCourseFormManager (new-course.html)**
```javascript
const NewCourseFormManager = {
    init()                           // Inicialización
    setupFormElements()              // Configura elementos
    setupValidation()                // Configura validaciones
    validateTitle(title)             // Valida título
    validateDescription(description) // Valida descripción
}
```

#### **NewCourseStepManager (new-course.html)**
```javascript
const NewCourseStepManager = {
    init()                    // Inicialización
    setupStepButtons()        // Configura botones
    showStep(step)           // Muestra paso específico
    nextStep()               // Avanza paso
    previousStep()           // Retrocede paso
    saveCourse()             // Guarda curso
}
```

### **4. Gestores Especializados (edit-course.html)**

#### **ModuleManager**
```javascript
const ModuleManager = {
    render()                    // Renderizar lista de módulos
    setupDragAndDrop()         // Configurar arrastrar y soltar
    handleDragStart(e)         // Inicio de arrastre
    handleDragOver(e)          // Arrastre sobre elemento
    handleDrop(e)              // Soltar elemento
    handleDragEnd(e)           // Fin de arrastre
    draggedElement: null       // Elemento siendo arrastrado
}
```

#### **ImageUploadManager**
```javascript
const ImageUploadManager = {
    init()                                              // Inicialización
    setupCourseImageUpload()                           // Imagen del curso
    setupModuleImageUpload()                           // Imagen del módulo
    handleDragOver(e)                                  // Arrastrar archivo
    handleImageDrop(e, imgElement, previewElement)     // Soltar archivo
    handleImageSelect(e, imgElement, previewElement)   // Seleccionar archivo
    processImageFile(file, imgElement, previewElement) // Procesar imagen
}
```

---

## 🎮 Funciones Globales Principales

### **Edición de Curso**
```javascript
window.editCourseInfo()    // Abrir modal de edición
window.saveCourseInfo()    // Guardar información del curso
```

### **Gestión de Módulos**
```javascript
window.addNewModule()           // Agregar nuevo módulo
window.editModule(moduleIndex)  // Editar módulo existente
window.deleteModule(moduleIndex)// Eliminar módulo
window.saveModule()             // Guardar módulo
```

### **Gestión de Secciones**
```javascript
window.editSection(section)     // Editar sección (learn/benefits)
window.addListItem()            // Agregar item a lista
window.removeListItem(index)    // Remover item de lista
window.saveSectionContent()     // Guardar contenido de sección
```

### **Guardado General**
```javascript
window.saveCourse()  // Guardar configuraciones del curso
```

---

## 📊 Estructura de Datos

### **Objeto Curso**
```javascript
{
    id: number,              // ID único del curso
    title: string,           // Título del curso
    description: string,     // Descripción
    rating: number,          // Calificación (1-5)
    reviews: number,         // Número de reseñas
    price: number,           // Precio del curso
    status: string,          // Estado: 'draft'|'published'|'archived'
    category: string,        // Categoría del curso
    level: string,           // Nivel: 'beginner'|'intermediate'|'advanced'
    duration: number,        // Duración en horas
    language: string,        // Idioma: 'es'|'en'|'pt'
    image: string,           // URL de la imagen
    learn: string[],         // Array de objetivos de aprendizaje
    benefits: string[],      // Array de beneficios
    modules: Module[]        // Array de módulos
}
```

### **Objeto Módulo**
```javascript
{
    title: string,           // Título del módulo
    description: string,     // Descripción del módulo
    duration: number,        // Duración en minutos
    image: string           // URL de la imagen/thumbnail
}
```

---

## 🔄 Flujo de Trabajo

### **1. Inicialización**
```
1. DOMContentLoaded se dispara
2. Se inicializan los managers:
   - SidebarManager.init()
   - CourseLoader.init()
   - ImageUploadManager.init()
3. Se detecta ID del curso desde URL
4. Se cargan datos del curso
5. Se renderiza la interfaz
```

### **2. Edición de Curso**
```
1. Usuario hace clic en "Edit Course Info"
2. Se abre modal con datos actuales
3. Usuario modifica información
4. Se validan los datos
5. Se actualizan datos globales
6. Se actualiza la interfaz
7. Se cierra el modal
```

### **3. Gestión de Módulos**
```
Agregar:
1. Clic en "Add Module"
2. Modal vacío se abre
3. Usuario llena formulario
4. Se valida y guarda
5. Se re-renderiza lista

Editar:
1. Clic en lápiz del módulo
2. Modal se llena con datos
3. Usuario modifica
4. Se actualiza en array
5. Se re-renderiza lista

Reordenar:
1. Usuario arrastra módulo
2. Se detecta nueva posición
3. Se reordena array de datos
4. Se re-renderiza lista
```

---

## 🎨 Convenciones de Código

### **Nomenclatura**
- **Variables:** camelCase (`currentCourse`, `editingModuleIndex`)
- **Funciones:** camelCase (`editCourseInfo`, `saveCourseInfo`)
- **Constantes:** UPPER_CASE (`COURSE_LOADER`, `MODULE_MANAGER`)
- **Objetos:** PascalCase (`SidebarManager`, `CourseLoader`)

### **Comentarios**
```javascript
// ========================================
// TÍTULO DE SECCIÓN
// ========================================

/**
 * Descripción detallada de la función
 * @param {type} param - Descripción del parámetro
 * @returns {type} Descripción del retorno
 */
function miFuncion(param) {
    // Comentario de línea para explicar lógica específica
    console.log('🎯 Mensaje descriptivo con emoji');
}
```

### **Logging**
- ✅ `console.log('✅ Acción exitosa')`
- ⚠️ `console.warn('⚠️ Advertencia')`
- ❌ `console.error('❌ Error')`
- 🚀 `console.log('🚀 Iniciando proceso')`
- 💾 `console.log('💾 Guardando datos')`

---

## 🔧 Configuración y Personalización

### **Variables Globales por Página**

#### **Dashboard (dashboard.html)**
```javascript
const DashboardConfig = {
    breakpoints: { mobile: 992, desktop: 1200 },
    animations: { sidebarTransition: '0.3s ease', cardHover: '0.2s ease' }
};
```

#### **Gestión de Cursos (all-courses.html)**
```javascript
const CoursesConfig = {
    breakpoints: { mobile: 992, tablet: 768 },
    grid: { columns: { desktop: 3, tablet: 2, mobile: 1 } },
    animations: { cardHover: '0.3s ease', loadingFade: '0.5s ease' }
};
let coursesData = [];  // Datos de cursos cargados
```

#### **Nuevo Curso (new-course.html)**
```javascript
const NewCourseConfig = {
    steps: { total: 4, current: 1 },
    validation: { minTitleLength: 3, minDescriptionLength: 10, maxTitleLength: 100 },
    breakpoints: { mobile: 992, tablet: 768 }
};
let newCourseData = { /* datos del curso en creación */ };
```

#### **Edición de Curso (edit-course.html)**
```javascript
window.currentCourse = null;        // Curso actual
window.editingModuleIndex = -1;     // Índice de módulo editándose
window.currentEditSection = '';     // Sección siendo editada
```

### **Configuraciones CSS Globales**
```css
:root {
    --primary-color: #1e3a5f;      // Color principal
    --secondary-color: #f6d55c;    // Color secundario
    --accent-color: #3caea3;       // Color de acento
    --text-light: #6c757d;         // Texto claro
    --border-color: #dee2e6;       // Color de bordes
    --hover-color: #f8f9fa;        // Color de hover
}
```

---

## 🚀 Próximos Pasos

### **Integraciones Pendientes**
1. **API Backend:** Reemplazar localStorage con API REST
2. **Autenticación:** Sistema de login/permisos por roles
3. **Base de Datos:** Migrar de localStorage a base de datos
4. **Notificaciones:** Sistema de notificaciones toast/push

### **Mejoras por Página**

#### **Dashboard**
- [ ] Métricas en tiempo real
- [ ] Gráficos de estadísticas
- [ ] Notificaciones del sistema
- [ ] Accesos directos personalizables

#### **Gestión de Cursos**
- [ ] Filtros avanzados (categoría, precio, estado)
- [ ] Búsqueda en tiempo real
- [ ] Ordenamiento personalizable
- [ ] Vista de lista/grid toggle

#### **Nuevo Curso**
- [ ] Autoguardado de borrador
- [ ] Validaciones más robustas
- [ ] Preview en tiempo real
- [ ] Plantillas de curso

#### **Edición de Curso**
- [ ] Historial de cambios/versiones
- [ ] Colaboración en tiempo real
- [ ] Autoguardado automático
- [ ] Editor de contenido WYSIWYG

### **Mejoras Técnicas Generales**
1. **Performance:** Lazy loading y optimización
2. **PWA:** Convertir en Progressive Web App
3. **Testing:** Implementar tests unitarios
4. **CI/CD:** Pipeline de despliegue automático
5. **Monitoring:** Logs y métricas de uso

---

## 👥 Equipo de Desarrollo

**Mantenedores:**
- Equipo AbilitySeminarsGroup

**Contribuciones:**
- Seguir las convenciones establecidas
- Documentar nuevas funcionalidades
- Mantener compatibilidad con módulos existentes

---

## 📞 Soporte

Para dudas o problemas:
1. Revisar esta documentación
2. Consultar comentarios en el código
3. Contactar al equipo de desarrollo

**¡Código limpio, equipo feliz! 🎉**
