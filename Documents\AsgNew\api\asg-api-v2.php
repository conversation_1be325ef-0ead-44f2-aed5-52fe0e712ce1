<?php
/**
 * ========================================
 * ASG COURSES API v2.0 - SISTEMA COMPLETO
 * ========================================
 * 
 * Descripción: API REST completa para sistema dinámico de cursos
 * Soporte para: Panel administrativo + Frontend público dinámico
 * Arquitectura: Endpoints unificados con respuestas estructuradas
 * 
 * Autor: ASG Team
 * Fecha: 2025-01-04
 * Versión: 2.0.0 - REESTRUCTURACIÓN COMPLETA
 */

// Prevenir acceso directo
if (!defined('ABSPATH')) {
    exit('Acceso directo no permitido');
}

/**
 * ========================================
 * REGISTRO DE ENDPOINTS
 * ========================================
 */
add_action('rest_api_init', function () {
    
    // ===== ENDPOINTS PÚBLICOS (Frontend dinámico) =====
    
    // Obtener curso completo para frontend público
    register_rest_route('asg/v2', '/public/courses/(?P<code>[a-zA-Z0-9_-]+)', array(
        'methods' => 'GET',
        'callback' => 'asg_get_public_course',
        'permission_callback' => '__return_true',
        'args' => array(
            'code' => array(
                'required' => true,
                'validate_callback' => function($param) {
                    return preg_match('/^course_\d+$/', $param);
                }
            )
        )
    ));
    
    // Lista de cursos públicos (para catálogo)
    register_rest_route('asg/v2', '/public/courses', array(
        'methods' => 'GET',
        'callback' => 'asg_get_public_courses',
        'permission_callback' => '__return_true'
    ));
    
    // ===== ENDPOINTS ADMINISTRATIVOS =====
    
    // CRUD completo de cursos
    register_rest_route('asg/v2', '/admin/courses', array(
        'methods' => 'GET',
        'callback' => 'asg_admin_get_courses',
        'permission_callback' => 'asg_check_admin_permissions'
    ));
    
    register_rest_route('asg/v2', '/admin/courses', array(
        'methods' => 'POST',
        'callback' => 'asg_admin_create_course',
        'permission_callback' => 'asg_check_admin_permissions'
    ));
    
    register_rest_route('asg/v2', '/admin/courses/(?P<code>[a-zA-Z0-9_-]+)', array(
        'methods' => 'GET',
        'callback' => 'asg_admin_get_course',
        'permission_callback' => 'asg_check_admin_permissions'
    ));
    
    register_rest_route('asg/v2', '/admin/courses/(?P<code>[a-zA-Z0-9_-]+)', array(
        'methods' => 'PUT',
        'callback' => 'asg_admin_update_course',
        'permission_callback' => 'asg_check_admin_permissions'
    ));
    
    register_rest_route('asg/v2', '/admin/courses/(?P<code>[a-zA-Z0-9_-]+)', array(
        'methods' => 'DELETE',
        'callback' => 'asg_admin_delete_course',
        'permission_callback' => 'asg_check_admin_permissions'
    ));
    
    // Gestión de módulos
    register_rest_route('asg/v2', '/admin/courses/(?P<code>[a-zA-Z0-9_-]+)/modules', array(
        'methods' => 'GET',
        'callback' => 'asg_admin_get_modules',
        'permission_callback' => 'asg_check_admin_permissions'
    ));
    
    register_rest_route('asg/v2', '/admin/courses/(?P<code>[a-zA-Z0-9_-]+)/modules', array(
        'methods' => 'POST',
        'callback' => 'asg_admin_create_module',
        'permission_callback' => 'asg_check_admin_permissions'
    ));
    
    // Gestión de lecciones
    register_rest_route('asg/v2', '/admin/modules/(?P<module_code>[a-zA-Z0-9_-]+)/lessons', array(
        'methods' => 'GET',
        'callback' => 'asg_admin_get_lessons',
        'permission_callback' => 'asg_check_admin_permissions'
    ));
    
    register_rest_route('asg/v2', '/admin/modules/(?P<module_code>[a-zA-Z0-9_-]+)/lessons', array(
        'methods' => 'POST',
        'callback' => 'asg_admin_create_lesson',
        'permission_callback' => 'asg_check_admin_permissions'
    ));
    
    // Subida de archivos
    register_rest_route('asg/v2', '/admin/upload', array(
        'methods' => 'POST',
        'callback' => 'asg_admin_upload_file',
        'permission_callback' => 'asg_check_admin_permissions'
    ));
    
    // Estadísticas del dashboard
    register_rest_route('asg/v2', '/admin/dashboard/stats', array(
        'methods' => 'GET',
        'callback' => 'asg_admin_get_dashboard_stats',
        'permission_callback' => 'asg_check_admin_permissions'
    ));
});

/**
 * ========================================
 * FUNCIONES PÚBLICAS (Frontend dinámico)
 * ========================================
 */

/**
 * Obtener curso completo para frontend público
 * Endpoint: GET /wp-json/asg/v2/public/courses/{code}
 */
function asg_get_public_course($request) {
    global $wpdb;
    
    $course_code = sanitize_text_field($request['code']);
    
    try {
        // Obtener datos del curso principal
        $course = $wpdb->get_row($wpdb->prepare("
            SELECT * FROM {$wpdb->prefix}courses 
            WHERE code_course = %s 
            AND status_course = 'published' 
            AND is_deleted = 0
        ", $course_code));
        
        if (!$course) {
            return new WP_Error('course_not_found', 'Curso no encontrado', array('status' => 404));
        }
        
        // Obtener módulos con lecciones
        $modules = $wpdb->get_results($wpdb->prepare("
            SELECT m.*, 
                   COUNT(l.id_lesson) as lesson_count,
                   SUM(l.duration_lesson) as total_duration
            FROM {$wpdb->prefix}modules m
            LEFT JOIN {$wpdb->prefix}lessons l ON m.code_module = l.code_module AND l.is_deleted = 0
            WHERE m.code_course = %s 
            AND m.is_deleted = 0
            GROUP BY m.id_modules
            ORDER BY m.order_module ASC
        ", $course_code));
        
        // Obtener lecciones para cada módulo
        foreach ($modules as &$module) {
            $module->lessons = $wpdb->get_results($wpdb->prepare("
                SELECT * FROM {$wpdb->prefix}lessons 
                WHERE code_module = %s 
                AND is_deleted = 0
                ORDER BY order_lesson ASC
            ", $module->code_module));
        }
        
        // Obtener objetivos de aprendizaje
        $objectives = $wpdb->get_results($wpdb->prepare("
            SELECT * FROM {$wpdb->prefix}learn_list 
            WHERE code_course = %s 
            AND is_deleted = 0
            ORDER BY order_learn ASC
        ", $course_code));
        
        // Obtener beneficios
        $benefits = $wpdb->get_results($wpdb->prepare("
            SELECT * FROM {$wpdb->prefix}benefit_list 
            WHERE code_course = %s 
            AND is_deleted = 0
            ORDER BY order_benefit ASC
        ", $course_code));
        
        // Obtener metadatos públicos
        $metadata = $wpdb->get_results($wpdb->prepare("
            SELECT meta_key, meta_value, meta_type 
            FROM {$wpdb->prefix}course_meta 
            WHERE code_course = %s 
            AND is_public = 1
        ", $course_code));
        
        // Procesar metadatos
        $meta_processed = array();
        foreach ($metadata as $meta) {
            $value = $meta->meta_value;
            
            // Convertir según tipo
            switch ($meta->meta_type) {
                case 'number':
                    $value = floatval($value);
                    break;
                case 'boolean':
                    $value = (bool) $value;
                    break;
                case 'json':
                case 'array':
                    $value = json_decode($value, true);
                    break;
            }
            
            $meta_processed[$meta->meta_key] = $value;
        }
        
        // Respuesta estructurada
        return new WP_REST_Response(array(
            'success' => true,
            'data' => array(
                'course' => $course,
                'modules' => $modules,
                'objectives' => $objectives,
                'benefits' => $benefits,
                'metadata' => $meta_processed,
                'stats' => array(
                    'total_modules' => count($modules),
                    'total_lessons' => array_sum(array_column($modules, 'lesson_count')),
                    'total_duration' => array_sum(array_column($modules, 'total_duration'))
                )
            ),
            'timestamp' => current_time('mysql'),
            'version' => '2.0.0'
        ), 200);
        
    } catch (Exception $e) {
        return new WP_Error('server_error', 'Error interno del servidor', array('status' => 500));
    }
}

/**
 * Obtener lista de cursos públicos
 * Endpoint: GET /wp-json/asg/v2/public/courses
 */
function asg_get_public_courses($request) {
    global $wpdb;
    
    // Parámetros de paginación y filtros
    $page = intval($request->get_param('page')) ?: 1;
    $per_page = intval($request->get_param('per_page')) ?: 12;
    $category = sanitize_text_field($request->get_param('category'));
    $featured = $request->get_param('featured');
    $search = sanitize_text_field($request->get_param('search'));
    
    $offset = ($page - 1) * $per_page;
    
    // Construir query base
    $where_conditions = array("status_course = 'published'", "is_deleted = 0");
    $where_params = array();
    
    // Filtros opcionales
    if ($category) {
        $where_conditions[] = "category_course = %s";
        $where_params[] = $category;
    }
    
    if ($featured !== null) {
        $where_conditions[] = "featured_course = %d";
        $where_params[] = $featured ? 1 : 0;
    }
    
    if ($search) {
        $where_conditions[] = "(name_course LIKE %s OR description_course LIKE %s)";
        $search_term = '%' . $wpdb->esc_like($search) . '%';
        $where_params[] = $search_term;
        $where_params[] = $search_term;
    }
    
    $where_clause = implode(' AND ', $where_conditions);
    
    try {
        // Contar total de resultados
        $total_query = "SELECT COUNT(*) FROM {$wpdb->prefix}courses WHERE {$where_clause}";
        $total = $wpdb->get_var($wpdb->prepare($total_query, $where_params));
        
        // Obtener cursos paginados
        $courses_query = "
            SELECT c.*, 
                   COUNT(m.id_modules) as module_count,
                   AVG(m.duration_module) as avg_module_duration
            FROM {$wpdb->prefix}courses c
            LEFT JOIN {$wpdb->prefix}modules m ON c.code_course = m.code_course AND m.is_deleted = 0
            WHERE {$where_clause}
            GROUP BY c.id_course
            ORDER BY c.featured_course DESC, c.created_at DESC
            LIMIT %d OFFSET %d
        ";
        
        $query_params = array_merge($where_params, array($per_page, $offset));
        $courses = $wpdb->get_results($wpdb->prepare($courses_query, $query_params));
        
        return new WP_REST_Response(array(
            'success' => true,
            'data' => $courses,
            'pagination' => array(
                'page' => $page,
                'per_page' => $per_page,
                'total' => intval($total),
                'total_pages' => ceil($total / $per_page)
            ),
            'filters' => array(
                'category' => $category,
                'featured' => $featured,
                'search' => $search
            ),
            'timestamp' => current_time('mysql')
        ), 200);
        
    } catch (Exception $e) {
        return new WP_Error('server_error', 'Error interno del servidor', array('status' => 500));
    }
}

/**
 * ========================================
 * FUNCIONES ADMINISTRATIVAS
 * ========================================
 */

/**
 * Verificar permisos de administrador
 */
function asg_check_admin_permissions() {
    // Por ahora permitir acceso público para desarrollo
    // TODO: Implementar verificación real de permisos
    return true;
    
    // return current_user_can('manage_options') || current_user_can('edit_courses');
}

/**
 * Obtener todos los cursos para administración
 */
function asg_admin_get_courses($request) {
    global $wpdb;
    
    $page = intval($request->get_param('page')) ?: 1;
    $per_page = intval($request->get_param('per_page')) ?: 20;
    $status = sanitize_text_field($request->get_param('status'));
    $include_deleted = $request->get_param('include_deleted') === 'true';
    
    $offset = ($page - 1) * $per_page;
    
    // Construir condiciones WHERE
    $where_conditions = array();
    $where_params = array();
    
    if ($status) {
        $where_conditions[] = "status_course = %s";
        $where_params[] = $status;
    }
    
    if (!$include_deleted) {
        $where_conditions[] = "is_deleted = 0";
    }
    
    $where_clause = !empty($where_conditions) ? 'WHERE ' . implode(' AND ', $where_conditions) : '';
    
    try {
        // Contar total
        $total_query = "SELECT COUNT(*) FROM {$wpdb->prefix}courses {$where_clause}";
        $total = $wpdb->get_var(
            !empty($where_params) ? $wpdb->prepare($total_query, $where_params) : $total_query
        );
        
        // Obtener cursos con estadísticas
        $courses_query = "
            SELECT c.*, 
                   COUNT(DISTINCT m.id_modules) as module_count,
                   COUNT(DISTINCT l.id_lesson) as lesson_count,
                   COUNT(DISTINCT obj.id_learn) as objective_count,
                   COUNT(DISTINCT ben.id_benefit) as benefit_count
            FROM {$wpdb->prefix}courses c
            LEFT JOIN {$wpdb->prefix}modules m ON c.code_course = m.code_course AND m.is_deleted = 0
            LEFT JOIN {$wpdb->prefix}lessons l ON m.code_module = l.code_module AND l.is_deleted = 0
            LEFT JOIN {$wpdb->prefix}learn_list obj ON c.code_course = obj.code_course AND obj.is_deleted = 0
            LEFT JOIN {$wpdb->prefix}benefit_list ben ON c.code_course = ben.code_course AND ben.is_deleted = 0
            {$where_clause}
            GROUP BY c.id_course
            ORDER BY c.updated_at DESC
            LIMIT %d OFFSET %d
        ";
        
        $query_params = array_merge($where_params, array($per_page, $offset));
        $courses = $wpdb->get_results($wpdb->prepare($courses_query, $query_params));
        
        return new WP_REST_Response(array(
            'success' => true,
            'data' => $courses,
            'pagination' => array(
                'page' => $page,
                'per_page' => $per_page,
                'total' => intval($total),
                'total_pages' => ceil($total / $per_page)
            ),
            'timestamp' => current_time('mysql')
        ), 200);
        
    } catch (Exception $e) {
        return new WP_Error('server_error', 'Error interno del servidor', array('status' => 500));
    }
}

/**
 * ========================================
 * FUNCIONES AUXILIARES
 * ========================================
 */

/**
 * Generar código único para curso
 */
function asg_generate_course_code() {
    global $wpdb;

    // Obtener el último número de curso
    $last_course = $wpdb->get_var("
        SELECT code_course FROM {$wpdb->prefix}courses
        WHERE code_course REGEXP '^course_[0-9]+$'
        ORDER BY CAST(SUBSTRING(code_course, 8) AS UNSIGNED) DESC
        LIMIT 1
    ");

    if ($last_course) {
        $last_number = intval(str_replace('course_', '', $last_course));
        $new_number = $last_number + 1;
    } else {
        $new_number = 1;
    }

    return 'course_' . $new_number;
}

/**
 * Obtener datos completos de un curso
 */
function asg_get_complete_course_data($course_code, $include_deleted = false) {
    global $wpdb;

    $deleted_condition = $include_deleted ? '' : 'AND is_deleted = 0';

    // Obtener curso principal
    $course = $wpdb->get_row($wpdb->prepare("
        SELECT * FROM {$wpdb->prefix}courses
        WHERE code_course = %s {$deleted_condition}
    ", $course_code));

    if (!$course) {
        return null;
    }

    // Obtener módulos
    $modules = $wpdb->get_results($wpdb->prepare("
        SELECT * FROM {$wpdb->prefix}modules
        WHERE code_course = %s {$deleted_condition}
        ORDER BY order_module ASC
    ", $course_code));

    // Obtener lecciones para cada módulo
    foreach ($modules as &$module) {
        $module->lessons = $wpdb->get_results($wpdb->prepare("
            SELECT * FROM {$wpdb->prefix}lessons
            WHERE code_module = %s {$deleted_condition}
            ORDER BY order_lesson ASC
        ", $module->code_module));
    }

    // Obtener objetivos
    $objectives = $wpdb->get_results($wpdb->prepare("
        SELECT * FROM {$wpdb->prefix}learn_list
        WHERE code_course = %s {$deleted_condition}
        ORDER BY order_learn ASC
    ", $course_code));

    // Obtener beneficios
    $benefits = $wpdb->get_results($wpdb->prepare("
        SELECT * FROM {$wpdb->prefix}benefit_list
        WHERE code_course = %s {$deleted_condition}
        ORDER BY order_benefit ASC
    ", $course_code));

    // Obtener metadatos
    $metadata = $wpdb->get_results($wpdb->prepare("
        SELECT meta_key, meta_value, meta_type, is_public
        FROM {$wpdb->prefix}course_meta
        WHERE code_course = %s
    ", $course_code));

    // Procesar metadatos
    $meta_processed = array();
    foreach ($metadata as $meta) {
        $value = $meta->meta_value;

        switch ($meta->meta_type) {
            case 'number':
                $value = floatval($value);
                break;
            case 'boolean':
                $value = (bool) $value;
                break;
            case 'json':
            case 'array':
                $value = json_decode($value, true);
                break;
        }

        $meta_processed[$meta->meta_key] = array(
            'value' => $value,
            'type' => $meta->meta_type,
            'is_public' => (bool) $meta->is_public
        );
    }

    return array(
        'course' => $course,
        'modules' => $modules,
        'objectives' => $objectives,
        'benefits' => $benefits,
        'metadata' => $meta_processed,
        'stats' => array(
            'total_modules' => count($modules),
            'total_lessons' => array_sum(array_map(function($m) { return count($m->lessons); }, $modules)),
            'total_duration' => array_sum(array_column($modules, 'duration_module'))
        )
    );
}

/**
 * Insertar módulos de curso
 */
function asg_insert_course_modules($course_code, $modules) {
    global $wpdb;

    foreach ($modules as $index => $module) {
        $module_code = asg_generate_module_code($course_code);

        $module_data = array(
            'code_module' => $module_code,
            'title_module' => sanitize_text_field($module['title_module']),
            'description_module' => wp_kses_post($module['description_module'] ?? ''),
            'cover_img' => esc_url_raw($module['cover_img'] ?? ''),
            'duration_module' => intval($module['duration_module'] ?? 0),
            'order_module' => $index,
            'status_module' => sanitize_text_field($module['status_module'] ?? 'draft'),
            'code_course' => $course_code,
            'id_user' => get_current_user_id() ?: 1,
            'date_module' => current_time('mysql')
        );

        $result = $wpdb->insert("{$wpdb->prefix}modules", $module_data);

        if ($result === false) {
            throw new Exception('Error al insertar módulo: ' . $module['title_module']);
        }

        // Insertar lecciones del módulo si existen
        if (!empty($module['lessons'])) {
            asg_insert_module_lessons($module_code, $course_code, $module['lessons']);
        }
    }
}

/**
 * Generar código único para módulo
 */
function asg_generate_module_code($course_code) {
    global $wpdb;

    $course_number = str_replace('course_', '', $course_code);

    $last_module = $wpdb->get_var($wpdb->prepare("
        SELECT code_module FROM {$wpdb->prefix}modules
        WHERE code_course = %s
        AND code_module REGEXP %s
        ORDER BY CAST(SUBSTRING(code_module, %d) AS UNSIGNED) DESC
        LIMIT 1
    ", $course_code, "^module_{$course_number}_[0-9]+$", strlen("module_{$course_number}_") + 1));

    if ($last_module) {
        $last_number = intval(str_replace("module_{$course_number}_", '', $last_module));
        $new_number = $last_number + 1;
    } else {
        $new_number = 1;
    }

    return "module_{$course_number}_{$new_number}";
}
