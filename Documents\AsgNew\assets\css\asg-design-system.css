/**
 * ========================================
 * ASG DESIGN SYSTEM v2.0
 * ========================================
 * 
 * Descripción: Sistema de diseño completo para AbilitySeminarsGroup
 * Incluye: Variables CSS, componentes, utilidades, responsive design
 * 
 * Autor: ASG Team
 * Fecha: 2025-01-04
 * Versión: 2.0.0 - DISEÑO MODERNO
 */

/**
 * ========================================
 * VARIABLES CSS (Custom Properties)
 * ========================================
 */
:root {
  /* === COLORES PRINCIPALES === */
  --asg-primary: #2563eb;
  --asg-primary-dark: #1d4ed8;
  --asg-primary-light: #3b82f6;
  --asg-primary-50: #eff6ff;
  --asg-primary-100: #dbeafe;
  --asg-primary-500: #3b82f6;
  --asg-primary-600: #2563eb;
  --asg-primary-700: #1d4ed8;
  
  /* === COLORES SECUNDARIOS === */
  --asg-secondary: #64748b;
  --asg-secondary-dark: #475569;
  --asg-secondary-light: #94a3b8;
  
  /* === COLORES DE ESTADO === */
  --asg-success: #10b981;
  --asg-success-light: #d1fae5;
  --asg-warning: #f59e0b;
  --asg-warning-light: #fef3c7;
  --asg-error: #ef4444;
  --asg-error-light: #fee2e2;
  --asg-info: #06b6d4;
  --asg-info-light: #cffafe;
  
  /* === COLORES NEUTROS === */
  --asg-white: #ffffff;
  --asg-gray-50: #f8fafc;
  --asg-gray-100: #f1f5f9;
  --asg-gray-200: #e2e8f0;
  --asg-gray-300: #cbd5e1;
  --asg-gray-400: #94a3b8;
  --asg-gray-500: #64748b;
  --asg-gray-600: #475569;
  --asg-gray-700: #334155;
  --asg-gray-800: #1e293b;
  --asg-gray-900: #0f172a;
  
  /* === TIPOGRAFÍA === */
  --asg-font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  --asg-font-mono: 'JetBrains Mono', 'Fira Code', Consolas, monospace;
  
  /* Tamaños de fuente */
  --asg-text-xs: 0.75rem;    /* 12px */
  --asg-text-sm: 0.875rem;   /* 14px */
  --asg-text-base: 1rem;     /* 16px */
  --asg-text-lg: 1.125rem;   /* 18px */
  --asg-text-xl: 1.25rem;    /* 20px */
  --asg-text-2xl: 1.5rem;    /* 24px */
  --asg-text-3xl: 1.875rem;  /* 30px */
  --asg-text-4xl: 2.25rem;   /* 36px */
  
  /* Pesos de fuente */
  --asg-font-light: 300;
  --asg-font-normal: 400;
  --asg-font-medium: 500;
  --asg-font-semibold: 600;
  --asg-font-bold: 700;
  
  /* === ESPACIADO === */
  --asg-space-1: 0.25rem;   /* 4px */
  --asg-space-2: 0.5rem;    /* 8px */
  --asg-space-3: 0.75rem;   /* 12px */
  --asg-space-4: 1rem;      /* 16px */
  --asg-space-5: 1.25rem;   /* 20px */
  --asg-space-6: 1.5rem;    /* 24px */
  --asg-space-8: 2rem;      /* 32px */
  --asg-space-10: 2.5rem;   /* 40px */
  --asg-space-12: 3rem;     /* 48px */
  --asg-space-16: 4rem;     /* 64px */
  --asg-space-20: 5rem;     /* 80px */
  
  /* === BORDES Y RADIOS === */
  --asg-radius-sm: 0.25rem;  /* 4px */
  --asg-radius: 0.375rem;    /* 6px */
  --asg-radius-md: 0.5rem;   /* 8px */
  --asg-radius-lg: 0.75rem;  /* 12px */
  --asg-radius-xl: 1rem;     /* 16px */
  --asg-radius-2xl: 1.5rem;  /* 24px */
  --asg-radius-full: 9999px;
  
  /* === SOMBRAS === */
  --asg-shadow-sm: 0 1px 2px 0 rgb(0 0 0 / 0.05);
  --asg-shadow: 0 1px 3px 0 rgb(0 0 0 / 0.1), 0 1px 2px -1px rgb(0 0 0 / 0.1);
  --asg-shadow-md: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1);
  --asg-shadow-lg: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);
  --asg-shadow-xl: 0 20px 25px -5px rgb(0 0 0 / 0.1), 0 8px 10px -6px rgb(0 0 0 / 0.1);
  
  /* === TRANSICIONES === */
  --asg-transition-fast: 150ms ease-in-out;
  --asg-transition: 200ms ease-in-out;
  --asg-transition-slow: 300ms ease-in-out;
  
  /* === BREAKPOINTS === */
  --asg-breakpoint-sm: 640px;
  --asg-breakpoint-md: 768px;
  --asg-breakpoint-lg: 1024px;
  --asg-breakpoint-xl: 1280px;
  --asg-breakpoint-2xl: 1536px;
}

/**
 * ========================================
 * RESET Y BASE STYLES
 * ========================================
 */
* {
  box-sizing: border-box;
}

body {
  font-family: var(--asg-font-family);
  font-size: var(--asg-text-base);
  line-height: 1.6;
  color: var(--asg-gray-700);
  background-color: var(--asg-gray-50);
  margin: 0;
  padding: 0;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

/**
 * ========================================
 * COMPONENTES BASE
 * ========================================
 */

/* === BOTONES === */
.asg-btn {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  gap: var(--asg-space-2);
  padding: var(--asg-space-3) var(--asg-space-4);
  font-size: var(--asg-text-sm);
  font-weight: var(--asg-font-medium);
  line-height: 1;
  border: 1px solid transparent;
  border-radius: var(--asg-radius);
  cursor: pointer;
  transition: all var(--asg-transition);
  text-decoration: none;
  white-space: nowrap;
  user-select: none;
}

.asg-btn:focus {
  outline: 2px solid var(--asg-primary-500);
  outline-offset: 2px;
}

.asg-btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

/* Variantes de botones */
.asg-btn-primary {
  background-color: var(--asg-primary-600);
  color: var(--asg-white);
  border-color: var(--asg-primary-600);
}

.asg-btn-primary:hover:not(:disabled) {
  background-color: var(--asg-primary-700);
  border-color: var(--asg-primary-700);
}

.asg-btn-secondary {
  background-color: var(--asg-white);
  color: var(--asg-gray-700);
  border-color: var(--asg-gray-300);
}

.asg-btn-secondary:hover:not(:disabled) {
  background-color: var(--asg-gray-50);
  border-color: var(--asg-gray-400);
}

.asg-btn-success {
  background-color: var(--asg-success);
  color: var(--asg-white);
  border-color: var(--asg-success);
}

.asg-btn-danger {
  background-color: var(--asg-error);
  color: var(--asg-white);
  border-color: var(--asg-error);
}

/* Tamaños de botones */
.asg-btn-sm {
  padding: var(--asg-space-2) var(--asg-space-3);
  font-size: var(--asg-text-xs);
}

.asg-btn-lg {
  padding: var(--asg-space-4) var(--asg-space-6);
  font-size: var(--asg-text-base);
}

/* === CARDS === */
.asg-card {
  background-color: var(--asg-white);
  border: 1px solid var(--asg-gray-200);
  border-radius: var(--asg-radius-lg);
  box-shadow: var(--asg-shadow-sm);
  overflow: hidden;
  transition: all var(--asg-transition);
}

.asg-card:hover {
  box-shadow: var(--asg-shadow-md);
  border-color: var(--asg-gray-300);
}

.asg-card-header {
  padding: var(--asg-space-6);
  border-bottom: 1px solid var(--asg-gray-200);
  background-color: var(--asg-gray-50);
}

.asg-card-body {
  padding: var(--asg-space-6);
}

.asg-card-footer {
  padding: var(--asg-space-4) var(--asg-space-6);
  border-top: 1px solid var(--asg-gray-200);
  background-color: var(--asg-gray-50);
}

/* === FORMULARIOS === */
.asg-form-group {
  margin-bottom: var(--asg-space-6);
}

.asg-label {
  display: block;
  font-size: var(--asg-text-sm);
  font-weight: var(--asg-font-medium);
  color: var(--asg-gray-700);
  margin-bottom: var(--asg-space-2);
}

.asg-input {
  display: block;
  width: 100%;
  padding: var(--asg-space-3) var(--asg-space-4);
  font-size: var(--asg-text-sm);
  line-height: 1.5;
  color: var(--asg-gray-700);
  background-color: var(--asg-white);
  border: 1px solid var(--asg-gray-300);
  border-radius: var(--asg-radius);
  transition: all var(--asg-transition);
}

.asg-input:focus {
  outline: none;
  border-color: var(--asg-primary-500);
  box-shadow: 0 0 0 3px var(--asg-primary-100);
}

.asg-input:disabled {
  background-color: var(--asg-gray-100);
  opacity: 0.6;
  cursor: not-allowed;
}

.asg-textarea {
  resize: vertical;
  min-height: 100px;
}

.asg-select {
  background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 20 20'%3e%3cpath stroke='%236b7280' stroke-linecap='round' stroke-linejoin='round' stroke-width='1.5' d='m6 8 4 4 4-4'/%3e%3c/svg%3e");
  background-position: right var(--asg-space-3) center;
  background-repeat: no-repeat;
  background-size: 1.5em 1.5em;
  padding-right: var(--asg-space-10);
}

/* === BADGES === */
.asg-badge {
  display: inline-flex;
  align-items: center;
  padding: var(--asg-space-1) var(--asg-space-3);
  font-size: var(--asg-text-xs);
  font-weight: var(--asg-font-medium);
  border-radius: var(--asg-radius-full);
  text-transform: uppercase;
  letter-spacing: 0.025em;
}

.asg-badge-primary {
  background-color: var(--asg-primary-100);
  color: var(--asg-primary-700);
}

.asg-badge-success {
  background-color: var(--asg-success-light);
  color: var(--asg-success);
}

.asg-badge-warning {
  background-color: var(--asg-warning-light);
  color: var(--asg-warning);
}

.asg-badge-error {
  background-color: var(--asg-error-light);
  color: var(--asg-error);
}

.asg-badge-gray {
  background-color: var(--asg-gray-100);
  color: var(--asg-gray-600);
}

/**
 * ========================================
 * LAYOUT COMPONENTS
 * ========================================
 */

/* === SIDEBAR === */
.asg-sidebar {
  width: 280px;
  height: 100vh;
  background-color: var(--asg-white);
  border-right: 1px solid var(--asg-gray-200);
  box-shadow: var(--asg-shadow-sm);
  position: fixed;
  left: 0;
  top: 0;
  z-index: 1000;
  overflow-y: auto;
  transition: transform var(--asg-transition);
}

.asg-sidebar-header {
  padding: var(--asg-space-6);
  border-bottom: 1px solid var(--asg-gray-200);
}

.asg-sidebar-logo {
  height: 40px;
  width: auto;
}

.asg-sidebar-nav {
  padding: var(--asg-space-4) 0;
}

.asg-sidebar-item {
  display: flex;
  align-items: center;
  gap: var(--asg-space-3);
  padding: var(--asg-space-3) var(--asg-space-6);
  color: var(--asg-gray-600);
  text-decoration: none;
  font-size: var(--asg-text-sm);
  font-weight: var(--asg-font-medium);
  transition: all var(--asg-transition);
  border-left: 3px solid transparent;
}

.asg-sidebar-item:hover {
  background-color: var(--asg-gray-50);
  color: var(--asg-gray-900);
}

.asg-sidebar-item.active {
  background-color: var(--asg-primary-50);
  color: var(--asg-primary-700);
  border-left-color: var(--asg-primary-600);
}

.asg-sidebar-icon {
  width: 20px;
  height: 20px;
  flex-shrink: 0;
}

/* === MAIN CONTENT === */
.asg-main {
  margin-left: 280px;
  min-height: 100vh;
  background-color: var(--asg-gray-50);
}

.asg-header {
  background-color: var(--asg-white);
  border-bottom: 1px solid var(--asg-gray-200);
  padding: var(--asg-space-4) var(--asg-space-6);
  box-shadow: var(--asg-shadow-sm);
}

.asg-content {
  padding: var(--asg-space-8) var(--asg-space-6);
  max-width: 1200px;
  margin: 0 auto;
}

/**
 * ========================================
 * RESPONSIVE DESIGN
 * ========================================
 */
@media (max-width: 1024px) {
  .asg-sidebar {
    transform: translateX(-100%);
  }
  
  .asg-sidebar.open {
    transform: translateX(0);
  }
  
  .asg-main {
    margin-left: 0;
  }
  
  .asg-content {
    padding: var(--asg-space-6) var(--asg-space-4);
  }
}

@media (max-width: 640px) {
  .asg-content {
    padding: var(--asg-space-4) var(--asg-space-3);
  }
  
  .asg-btn {
    width: 100%;
    justify-content: center;
  }
  
  .asg-card-body {
    padding: var(--asg-space-4);
  }
}
