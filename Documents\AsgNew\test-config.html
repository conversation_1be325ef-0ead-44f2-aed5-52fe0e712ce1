<!DOCTYPE html>
<html lang="es">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test ASG Config - Nueva Ubicación</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 2rem;
            background-color: #f8f9fa;
        }
        
        .test-card {
            background: white;
            border-radius: 12px;
            padding: 1.5rem;
            margin-bottom: 1rem;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            border-left: 4px solid #2563eb;
        }
        
        .test-result {
            padding: 0.75rem;
            border-radius: 6px;
            margin: 0.5rem 0;
            font-family: monospace;
        }
        
        .success {
            background-color: #d1fae5;
            color: #065f46;
            border: 1px solid #10b981;
        }
        
        .error {
            background-color: #fef2f2;
            color: #991b1b;
            border: 1px solid #ef4444;
        }
        
        .info {
            background-color: #dbeafe;
            color: #1e40af;
            border: 1px solid #3b82f6;
        }
        
        button {
            background: #2563eb;
            color: white;
            border: none;
            padding: 0.75rem 1.5rem;
            border-radius: 6px;
            cursor: pointer;
            font-weight: 500;
            margin: 0.5rem 0.5rem 0.5rem 0;
        }
        
        button:hover {
            background: #1d4ed8;
        }
        
        .config-info {
            background: #f3f4f6;
            padding: 1rem;
            border-radius: 6px;
            margin: 1rem 0;
        }
        
        .config-info h4 {
            margin: 0 0 0.5rem 0;
            color: #374151;
        }
        
        .config-info pre {
            background: white;
            padding: 0.75rem;
            border-radius: 4px;
            overflow-x: auto;
            font-size: 0.9rem;
        }
    </style>
</head>
<body>
    <h1>🧪 Test ASG Config - Nueva Ubicación</h1>
    <p>Verificando que los archivos funcionen correctamente desde la carpeta <code>js/</code></p>

    <div class="test-card">
        <h3>📁 Test 1: Carga de Archivos</h3>
        <div id="fileLoadTest">
            <div class="test-result info">Ejecutando tests...</div>
        </div>
    </div>

    <div class="test-card">
        <h3>⚙️ Test 2: Configuración ASG_CONFIG</h3>
        <div id="configTest">
            <div class="test-result info">Verificando configuración...</div>
        </div>
    </div>

    <div class="test-card">
        <h3>🌐 Test 3: Cliente API</h3>
        <div id="apiTest">
            <div class="test-result info">Verificando cliente API...</div>
        </div>
        <button onclick="testApiConnection()">🔗 Test Conexión API</button>
        <button onclick="testMockData()">📊 Test Datos Mock</button>
    </div>

    <div class="test-card">
        <h3>🔔 Test 4: Notificaciones</h3>
        <div id="notificationTest">
            <div class="test-result info">Sistema de notificaciones...</div>
        </div>
        <button onclick="testNotifications()">✅ Test Success</button>
        <button onclick="testErrorNotification()">❌ Test Error</button>
        <button onclick="testWarningNotification()">⚠️ Test Warning</button>
        <button onclick="testInfoNotification()">ℹ️ Test Info</button>
    </div>

    <div class="test-card">
        <h3>🛠️ Test 5: Utilidades</h3>
        <div id="utilsTest">
            <div class="test-result info">Verificando utilidades...</div>
        </div>
        <button onclick="testUtilities()">🧰 Test Utilidades</button>
    </div>

    <div class="test-card">
        <h3>📋 Información de Configuración</h3>
        <div id="configInfo" class="config-info">
            <div class="test-result info">Cargando información...</div>
        </div>
    </div>

    <!-- Scripts desde wp-content/uploads -->
    <script src="https://abilityseminarsgroup.com/wp-content/uploads/asg/js/asg-config.js"></script>
    <script src="https://abilityseminarsgroup.com/wp-content/uploads/asg/js/asg-api-client.js"></script>
    
    <script>
        // Variables para tests
        let testResults = {
            fileLoad: false,
            config: false,
            api: false,
            notifications: false,
            utils: false
        };

        // Ejecutar tests al cargar
        document.addEventListener('DOMContentLoaded', function() {
            setTimeout(() => {
                runAllTests();
            }, 500);
        });

        function runAllTests() {
            testFileLoad();
            testConfig();
            testApiClient();
            testNotificationSystem();
            testUtilitiesSystem();
            showConfigInfo();
        }

        function testFileLoad() {
            const container = document.getElementById('fileLoadTest');
            let results = [];

            // Test ASG_CONFIG
            if (typeof window.ASG_CONFIG !== 'undefined') {
                results.push('<div class="test-result success">✅ asg-config.js cargado correctamente</div>');
                testResults.fileLoad = true;
            } else {
                results.push('<div class="test-result error">❌ asg-config.js NO cargado</div>');
            }

            // Test ASG_API
            if (typeof window.ASG_API !== 'undefined') {
                results.push('<div class="test-result success">✅ asg-api-client.js cargado correctamente</div>');
            } else {
                results.push('<div class="test-result error">❌ asg-api-client.js NO cargado</div>');
            }

            container.innerHTML = results.join('');
        }

        function testConfig() {
            const container = document.getElementById('configTest');
            let results = [];

            if (window.ASG_CONFIG) {
                testResults.config = true;
                
                // Test propiedades principales
                results.push(`<div class="test-result success">✅ Versión: ${ASG_CONFIG.version}</div>`);
                results.push(`<div class="test-result success">✅ API URL: ${ASG_CONFIG.urls.apiBaseUrl}</div>`);
                results.push(`<div class="test-result success">✅ Endpoints: ${Object.keys(ASG_CONFIG.api.endpoints).length} configurados</div>`);
                results.push(`<div class="test-result success">✅ Categorías: ${Object.keys(ASG_CONFIG.categories).length} disponibles</div>`);
                
                // Test utilidades
                if (ASG_CONFIG.utils) {
                    results.push('<div class="test-result success">✅ Utilidades disponibles</div>');
                } else {
                    results.push('<div class="test-result error">❌ Utilidades NO disponibles</div>');
                }
            } else {
                results.push('<div class="test-result error">❌ ASG_CONFIG no está disponible</div>');
            }

            container.innerHTML = results.join('');
        }

        function testApiClient() {
            const container = document.getElementById('apiTest');
            let results = [];

            if (window.ASG_API) {
                testResults.api = true;
                results.push('<div class="test-result success">✅ Cliente API inicializado</div>');
                results.push(`<div class="test-result success">✅ Base URL: ${ASG_API.baseUrl}</div>`);
                results.push(`<div class="test-result success">✅ Timeout: ${ASG_API.timeout}ms</div>`);
                results.push(`<div class="test-result success">✅ Reintentos: ${ASG_API.retries}</div>`);
            } else {
                results.push('<div class="test-result error">❌ Cliente API NO disponible</div>');
            }

            container.innerHTML = results.join('');
        }

        function testNotificationSystem() {
            const container = document.getElementById('notificationTest');
            let results = [];

            if (window.ASG_CONFIG && ASG_CONFIG.utils && ASG_CONFIG.utils.showNotification) {
                testResults.notifications = true;
                results.push('<div class="test-result success">✅ Sistema de notificaciones disponible</div>');
                results.push('<div class="test-result info">ℹ️ Usa los botones para probar diferentes tipos</div>');
            } else {
                results.push('<div class="test-result error">❌ Sistema de notificaciones NO disponible</div>');
            }

            container.innerHTML = results.join('');
        }

        function testUtilitiesSystem() {
            const container = document.getElementById('utilsTest');
            let results = [];

            if (window.ASG_CONFIG && ASG_CONFIG.utils) {
                testResults.utils = true;
                
                const utils = [
                    'formatCurrency', 'formatDate', 'getCategoryConfig', 
                    'getStateConfig', 'validateFile', 'truncateText', 
                    'formatNumber', 'debounce', 'generateId'
                ];
                
                const availableUtils = utils.filter(util => typeof ASG_CONFIG.utils[util] === 'function');
                
                results.push(`<div class="test-result success">✅ ${availableUtils.length}/${utils.length} utilidades disponibles</div>`);
                results.push('<div class="test-result info">ℹ️ Usa el botón para probar las utilidades</div>');
            } else {
                results.push('<div class="test-result error">❌ Utilidades NO disponibles</div>');
            }

            container.innerHTML = results.join('');
        }

        function showConfigInfo() {
            const container = document.getElementById('configInfo');
            
            if (window.ASG_CONFIG) {
                const info = {
                    version: ASG_CONFIG.version,
                    apiUrl: ASG_CONFIG.urls.apiBaseUrl,
                    endpoints: Object.keys(ASG_CONFIG.api.endpoints).length,
                    categories: Object.keys(ASG_CONFIG.categories).length,
                    languages: Object.keys(ASG_CONFIG.languages).length,
                    environment: ASG_CONFIG.utils.getConfig('debug') ? 'development' : 'production'
                };
                
                container.innerHTML = `
                    <h4>📊 Información de Configuración</h4>
                    <pre>${JSON.stringify(info, null, 2)}</pre>
                `;
            } else {
                container.innerHTML = '<div class="test-result error">❌ No se puede mostrar información de configuración</div>';
            }
        }

        // Funciones de test específicas
        async function testApiConnection() {
            if (window.ASG_API) {
                try {
                    const isOnline = await ASG_API.checkApiStatus();
                    ASG_CONFIG.utils.showNotification(
                        isOnline ? '✅ API conectada correctamente' : '⚠️ API offline, usando datos mock', 
                        isOnline ? 'success' : 'warning'
                    );
                } catch (error) {
                    ASG_CONFIG.utils.showNotification('❌ Error al conectar con la API', 'error');
                }
            }
        }

        async function testMockData() {
            if (window.ASG_API) {
                try {
                    const stats = await ASG_API.getDashboardStats();
                    const courses = await ASG_API.getCourses();
                    
                    ASG_CONFIG.utils.showNotification(
                        `📊 Datos cargados: ${stats.totalCourses || 0} cursos, ${courses.length || 0} en lista`, 
                        'info'
                    );
                } catch (error) {
                    ASG_CONFIG.utils.showNotification('❌ Error al cargar datos mock', 'error');
                }
            }
        }

        function testNotifications() {
            if (ASG_CONFIG && ASG_CONFIG.utils) {
                ASG_CONFIG.utils.showNotification('¡Notificación de éxito funcionando!', 'success');
            }
        }

        function testErrorNotification() {
            if (ASG_CONFIG && ASG_CONFIG.utils) {
                ASG_CONFIG.utils.showNotification('Notificación de error funcionando', 'error');
            }
        }

        function testWarningNotification() {
            if (ASG_CONFIG && ASG_CONFIG.utils) {
                ASG_CONFIG.utils.showNotification('Notificación de advertencia funcionando', 'warning');
            }
        }

        function testInfoNotification() {
            if (ASG_CONFIG && ASG_CONFIG.utils) {
                ASG_CONFIG.utils.showNotification('Notificación informativa funcionando', 'info');
            }
        }

        function testUtilities() {
            if (ASG_CONFIG && ASG_CONFIG.utils) {
                const tests = [
                    `💰 Moneda: ${ASG_CONFIG.utils.formatCurrency(299.99)}`,
                    `📅 Fecha: ${ASG_CONFIG.utils.formatDate(new Date())}`,
                    `🔢 Número: ${ASG_CONFIG.utils.formatNumber(1234567)}`,
                    `✂️ Texto: ${ASG_CONFIG.utils.truncateText('Este es un texto muy largo para probar la función de truncado', 30)}`,
                    `🆔 ID: ${ASG_CONFIG.utils.generateId()}`
                ];
                
                tests.forEach((test, index) => {
                    setTimeout(() => {
                        ASG_CONFIG.utils.showNotification(test, 'info');
                    }, index * 1000);
                });
            }
        }
    </script>
</body>
</html>
