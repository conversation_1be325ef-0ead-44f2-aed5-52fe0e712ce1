/**
 * ========================================
 * ASG API CLIENT - PARA ARCHIVOS HTML
 * ========================================
 * 
 * Descripción: Cliente de API optimizado para archivos HTML
 * Conecta directamente con los endpoints públicos de WordPress
 * 
 * Autor: ASG Team
 * Fecha: 2025-01-04
 * Versión: 2.0.0 - HTML CLIENT
 */

/**
 * ========================================
 * CLIENTE DE API PRINCIPAL
 * ========================================
 */
class ASGApiClient {
    constructor() {
        // Esperar a que ASG_CONFIG esté disponible
        this.waitForConfig().then(() => {
            this.initializeWithConfig();
        });

        // Configuración por defecto mientras se carga ASG_CONFIG
        this.baseUrl = 'https://abilityseminarsgroup.com/wp-json/asg/v1';
        this.timeout = 30000;
        this.retries = 3;
        this.usePublicEndpoints = true;

        console.log('🚀 ASG API Client initialized');
    }

    /**
     * Esperar a que ASG_CONFIG esté disponible
     */
    async waitForConfig() {
        return new Promise((resolve) => {
            if (window.ASG_CONFIG) {
                resolve();
                return;
            }

            const checkConfig = () => {
                if (window.ASG_CONFIG) {
                    resolve();
                } else {
                    setTimeout(checkConfig, 50);
                }
            };

            checkConfig();
        });
    }

    /**
     * Inicializar con configuración centralizada
     */
    initializeWithConfig() {
        if (window.ASG_CONFIG) {
            this.baseUrl = ASG_CONFIG.urls.apiBaseUrl;
            this.timeout = ASG_CONFIG.api.timeout;
            this.retries = ASG_CONFIG.api.retries;

            console.log('✅ ASG API Client configured with ASG_CONFIG');
        }
    }
    
    /**
     * Realizar petición HTTP con reintentos
     */
    async request(endpoint, options = {}) {
        const url = `${this.baseUrl}${endpoint}`;
        const defaultOptions = {
            method: 'GET',
            headers: {
                'Content-Type': 'application/json',
                'Accept': 'application/json'
            },
            timeout: this.timeout
        };
        
        const finalOptions = { ...defaultOptions, ...options };
        
        for (let attempt = 1; attempt <= this.retries; attempt++) {
            try {
                console.log(`📡 API Request [${attempt}/${this.retries}]: ${finalOptions.method} ${url}`);
                
                const controller = new AbortController();
                const timeoutId = setTimeout(() => controller.abort(), this.timeout);
                
                const response = await fetch(url, {
                    ...finalOptions,
                    signal: controller.signal
                });
                
                clearTimeout(timeoutId);
                
                if (!response.ok) {
                    throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                }
                
                const data = await response.json();
                console.log(`✅ API Success: ${url}`, data);
                
                return data;
                
            } catch (error) {
                console.warn(`⚠️ API Attempt ${attempt} failed:`, error.message);
                
                if (attempt === this.retries) {
                    console.error(`❌ API Failed after ${this.retries} attempts:`, error);
                    throw error;
                }
                
                // Esperar antes del siguiente intento
                await this.delay(1000 * attempt);
            }
        }
    }
    
    /**
     * Delay helper
     */
    delay(ms) {
        return new Promise(resolve => setTimeout(resolve, ms));
    }
    
    /**
     * ========================================
     * MÉTODOS DE DASHBOARD
     * ========================================
     */
    
    /**
     * Obtener estadísticas del dashboard
     */
    async getDashboardStats() {
        try {
            const endpoint = window.ASG_CONFIG ?
                ASG_CONFIG.api.endpoints.dashboardStats :
                '/dashboard/stats';

            const response = await this.request(endpoint);
            return response.data || response;
        } catch (error) {
            console.warn('📊 Using mock dashboard stats due to API error');
            return this.getMockDashboardStats();
        }
    }
    
    /**
     * Datos simulados del dashboard
     */
    getMockDashboardStats() {
        return {
            totalCourses: 6,
            publishedCourses: 4,
            draftCourses: 2,
            totalRevenue: 1299.96,
            avgPrice: 216.66,
            coursesChange: 25,
            recentActivity: [
                { action: 'Curso publicado', course: 'Marketing Digital', time: '2 horas' },
                { action: 'Nuevo estudiante', course: 'Finanzas Personales', time: '4 horas' },
                { action: 'Curso actualizado', course: 'Desarrollo Personal', time: '1 día' }
            ]
        };
    }
    
    /**
     * ========================================
     * MÉTODOS DE CURSOS
     * ========================================
     */
    
    /**
     * Obtener lista de cursos
     */
    async getCourses(params = {}) {
        try {
            const endpoint = window.ASG_CONFIG ?
                ASG_CONFIG.api.endpoints.courses :
                '/courses';

            const queryParams = new URLSearchParams(params).toString();
            const fullEndpoint = queryParams ? `${endpoint}?${queryParams}` : endpoint;

            const response = await this.request(fullEndpoint);
            return response.data || response;
        } catch (error) {
            console.warn('📚 Using mock courses due to API error');
            return this.getMockCourses();
        }
    }
    
    /**
     * Obtener curso específico
     */
    async getCourse(courseCode) {
        try {
            const endpoint = this.usePublicEndpoints ? 
                `/public/courses/${courseCode}` : 
                `/admin/courses/${courseCode}`;
            
            const response = await this.request(endpoint);
            return response.data || response;
        } catch (error) {
            console.warn(`📖 Using mock course data for ${courseCode}`);
            return this.getMockCourse(courseCode);
        }
    }
    
    /**
     * Crear nuevo curso
     */
    async createCourse(courseData) {
        try {
            const response = await this.request('/admin/courses', {
                method: 'POST',
                body: JSON.stringify(courseData)
            });
            
            this.showNotification('✅ Curso creado exitosamente', 'success');
            return response.data || response;
        } catch (error) {
            this.showNotification('❌ Error al crear el curso', 'error');
            throw error;
        }
    }

    /**
     * Actualizar curso
     */
    async updateCourse(courseCode, courseData) {
        try {
            const response = await this.request(`/admin/courses/${courseCode}`, {
                method: 'PUT',
                body: JSON.stringify(courseData)
            });

            this.showNotification('✅ Curso actualizado exitosamente', 'success');
            return response.data || response;
        } catch (error) {
            this.showNotification('❌ Error al actualizar el curso', 'error');
            throw error;
        }
    }

    /**
     * Eliminar curso
     */
    async deleteCourse(courseCode) {
        try {
            const response = await this.request(`/admin/courses/${courseCode}`, {
                method: 'DELETE'
            });

            this.showNotification('✅ Curso eliminado exitosamente', 'success');
            return response.data || response;
        } catch (error) {
            this.showNotification('❌ Error al eliminar el curso', 'error');
            throw error;
        }
    }

    /**
     * Mostrar notificación usando ASG_CONFIG
     */
    showNotification(message, type = 'info') {
        if (window.ASG_CONFIG && ASG_CONFIG.utils) {
            ASG_CONFIG.utils.showNotification(message, type);
        } else {
            // Fallback simple
            console.log(`[${type.toUpperCase()}] ${message}`);
        }
    }
    
    /**
     * Datos simulados de cursos
     */
    getMockCourses() {
        return [
            {
                id_course: 1,
                code_course: 'course_1',
                name_course: 'Como hacerte millonario?',
                description_course: 'Aprende las estrategias más efectivas para generar riqueza y alcanzar la libertad financiera.',
                cover_img: 'https://images.unsplash.com/photo-1579621970563-ebec7560ff3e?w=400&h=300&fit=crop',
                price_course: 299.99,
                status_course: 'published',
                category_course: 'finanzas',
                language_course: 'es',
                module_count: 8,
                lesson_count: 24,
                enrollment_count: 156,
                rating_average: 4.8,
                created_at: '2024-12-15 10:30:00',
                updated_at: '2024-12-20 15:45:00'
            },
            {
                id_course: 2,
                code_course: 'course_2',
                name_course: 'Marketing Digital Avanzado',
                description_course: 'Domina las técnicas más avanzadas del marketing digital y haz crecer tu negocio online.',
                cover_img: 'https://images.unsplash.com/photo-1460925895917-afdab827c52f?w=400&h=300&fit=crop',
                price_course: 199.99,
                status_course: 'published',
                category_course: 'marketing',
                language_course: 'es',
                module_count: 6,
                lesson_count: 18,
                enrollment_count: 89,
                rating_average: 4.6,
                created_at: '2024-12-10 14:20:00',
                updated_at: '2024-12-18 09:30:00'
            },
            {
                id_course: 3,
                code_course: 'course_3',
                name_course: 'Desarrollo Personal Integral',
                description_course: 'Transforma tu vida con técnicas probadas de crecimiento personal y liderazgo.',
                cover_img: 'https://images.unsplash.com/photo-1516321318423-f06f85e504b3?w=400&h=300&fit=crop',
                price_course: 149.99,
                status_course: 'draft',
                category_course: 'desarrollo-personal',
                language_course: 'es',
                module_count: 4,
                lesson_count: 12,
                enrollment_count: 0,
                rating_average: 0,
                created_at: '2024-12-05 09:15:00',
                updated_at: '2024-12-22 11:20:00'
            },
            {
                id_course: 4,
                code_course: 'course_4',
                name_course: 'Programación Web Moderna',
                description_course: 'Aprende las tecnologías más demandadas del desarrollo web actual.',
                cover_img: 'https://images.unsplash.com/photo-1461749280684-dccba630e2f6?w=400&h=300&fit=crop',
                price_course: 249.99,
                status_course: 'published',
                category_course: 'tecnologia',
                language_course: 'es',
                module_count: 10,
                lesson_count: 35,
                enrollment_count: 67,
                rating_average: 4.9,
                created_at: '2024-11-28 16:45:00',
                updated_at: '2024-12-19 13:15:00'
            },
            {
                id_course: 5,
                code_course: 'course_5',
                name_course: 'Gestión de Negocios Exitosos',
                description_course: 'Estrategias comprobadas para crear y hacer crecer tu negocio.',
                cover_img: 'https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=400&h=300&fit=crop',
                price_course: 179.99,
                status_course: 'published',
                category_course: 'negocios',
                language_course: 'es',
                module_count: 7,
                lesson_count: 21,
                enrollment_count: 134,
                rating_average: 4.7,
                created_at: '2024-11-20 12:30:00',
                updated_at: '2024-12-21 08:45:00'
            },
            {
                id_course: 6,
                code_course: 'course_6',
                name_course: 'Nutrición y Bienestar',
                description_course: 'Guía completa para una vida saludable y equilibrada.',
                cover_img: 'https://images.unsplash.com/photo-1490645935967-10de6ba17061?w=400&h=300&fit=crop',
                price_course: 129.99,
                status_course: 'draft',
                category_course: 'salud',
                language_course: 'es',
                module_count: 5,
                lesson_count: 15,
                enrollment_count: 0,
                rating_average: 0,
                created_at: '2024-12-01 14:00:00',
                updated_at: '2024-12-23 10:30:00'
            }
        ];
    }
    
    /**
     * Datos simulados de curso específico
     */
    getMockCourse(courseCode) {
        const courses = this.getMockCourses();
        return courses.find(course => course.code_course === courseCode) || courses[0];
    }
    
    /**
     * ========================================
     * MÉTODOS DE UTILIDAD
     * ========================================
     */
    
    /**
     * Verificar estado de la API
     */
    async checkApiStatus() {
        try {
            const response = await this.request('/info');
            console.log('✅ API Status: Online', response);
            return true;
        } catch (error) {
            console.warn('⚠️ API Status: Offline, using mock data');
            return false;
        }
    }
    
    /**
     * Cambiar modo de endpoints
     */
    setPublicMode(usePublic = true) {
        this.usePublicEndpoints = usePublic;
        console.log(`🔄 API Mode: ${usePublic ? 'Public' : 'Admin'} endpoints`);
    }
}

/**
 * ========================================
 * INSTANCIA GLOBAL
 * ========================================
 */
window.ASG_API = new ASGApiClient();

/**
 * ========================================
 * UTILIDADES DE API
 * ========================================
 */
window.ASG_API_UTILS = {
    /**
     * Formatear respuesta de error
     */
    formatError: (error) => {
        if (error.response && error.response.data) {
            return error.response.data.message || 'Error desconocido';
        }
        return error.message || 'Error de conexión';
    },
    
    /**
     * Validar respuesta de API
     */
    validateResponse: (response) => {
        return response && (response.success !== false);
    },
    
    /**
     * Procesar lista paginada
     */
    processPaginatedResponse: (response) => {
        return {
            data: response.data || response,
            pagination: response.pagination || {
                page: 1,
                per_page: 20,
                total: (response.data || response).length,
                total_pages: 1
            }
        };
    }
};

console.log('🚀 ASG API Client v2.0.0 loaded - Ready for HTML integration');
