<!DOCTYPE html>
<html lang="es">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Edit Course - AbilitySeminarsGroup</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.11.3/font/bootstrap-icons.css" rel="stylesheet">
    
    <style>
        body {
            font-family: 'Inter', sans-serif;
            background-color: #f8f9fa;
            margin: 0;
            padding-top: 70px;
        }

        .navbar {
            background-color: #1e3a5f;
            height: 70px;
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            z-index: 1030;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
        }

        .sidebar {
            position: fixed;
            top: 70px;
            left: -280px;
            width: 280px;
            height: calc(100vh - 70px);
            background: white;
            box-shadow: 2px 0 10px rgba(0, 0, 0, 0.1);
            transition: left 0.3s ease;
            z-index: 1020;
            overflow-y: auto;
        }

        .sidebar.active {
            left: 0;
        }

        .main-content {
            margin-left: 0;
            padding: 2rem;
            transition: margin-left 0.3s ease;
        }

        @media (min-width: 992px) {
            .sidebar {
                left: 0;
            }
            .main-content {
                margin-left: 280px;
            }
        }

        .page-header {
            background: white;
            border-radius: 12px;
            padding: 2rem;
            margin-bottom: 2rem;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
        }

        .form-section {
            background: white;
            border-radius: 12px;
            padding: 2rem;
            margin-bottom: 2rem;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
        }

        .section-title {
            color: #1e3a5f;
            font-size: 1.25rem;
            font-weight: 600;
            margin-bottom: 1.5rem;
            padding-bottom: 0.5rem;
            border-bottom: 2px solid #f6d55c;
        }

        .form-group {
            margin-bottom: 1.5rem;
        }

        .form-group label {
            display: block;
            margin-bottom: 0.5rem;
            color: #495057;
            font-weight: 500;
        }

        .form-group input,
        .form-group textarea,
        .form-group select {
            width: 100%;
            padding: 0.75rem;
            border: 1px solid #ddd;
            border-radius: 6px;
            font-size: 1rem;
        }

        .btn-primary {
            background-color: #1e3a5f;
            border-color: #1e3a5f;
            color: white;
            font-weight: 500;
            padding: 0.75rem 1.5rem;
            border-radius: 6px;
        }

        .loading-state {
            text-align: center;
            padding: 3rem;
            color: #6c757d;
        }

        .error-state {
            text-align: center;
            padding: 3rem;
            color: #dc3545;
            display: none;
        }

        .module-item {
            background: #f8f9fa;
            border: 1px solid #e9ecef;
            border-radius: 8px;
            padding: 1rem;
            margin-bottom: 1rem;
            display: flex;
            align-items: center;
            gap: 1rem;
        }

        .module-actions button {
            padding: 0.25rem 0.5rem;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            margin-left: 0.5rem;
        }

        .edit-btn {
            background-color: #007bff;
            color: white;
        }

        .delete-btn {
            background-color: #dc3545;
            color: white;
        }

        /* Module Advanced Styles */
        .module-item {
            position: relative;
            transition: all 0.3s ease;
        }

        .module-item:hover {
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
            transform: translateY(-2px);
        }

        .module-item.dragging {
            opacity: 0.5;
            transform: rotate(5deg);
        }

        .module-drag-handle {
            width: 30px;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: grab;
            color: #6c757d;
        }

        .module-drag-handle:hover {
            color: #1e3a5f;
        }

        .module-drag-handle:active {
            cursor: grabbing;
        }

        .module-image {
            width: 80px;
            height: 60px;
            border-radius: 6px;
            overflow: hidden;
            background: #e9ecef;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .module-info {
            flex: 1;
            padding: 0 1rem;
        }

        .module-title {
            font-weight: 600;
            color: #1e3a5f;
            margin-bottom: 0.25rem;
        }

        .module-description {
            color: #6c757d;
            font-size: 0.9rem;
            margin-bottom: 0.25rem;
        }

        .module-meta {
            display: flex;
            gap: 1rem;
        }

        .module-actions {
            display: flex;
            gap: 0.25rem;
            align-items: center;
        }

        /* Objectives and Benefits Styles */
        .objective-item,
        .benefit-item {
            transition: all 0.3s ease;
        }

        .objective-item:hover,
        .benefit-item:hover {
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        }

        /* Timeline Styles for History */
        .timeline-item {
            position: relative;
        }

        .timeline-icon {
            font-size: 0.8rem;
        }

        /* Responsive Design */
        @media (max-width: 768px) {
            .main-content {
                padding: 1rem;
            }

            .module-item {
                flex-direction: column;
                text-align: center;
            }

            .module-actions {
                justify-content: center;
                margin-top: 1rem;
            }

            .module-drag-handle {
                display: none;
            }

            .module-info {
                padding: 1rem 0;
            }
        }
    </style>
</head>
<body>
    <!-- Navbar -->
    <nav class="navbar navbar-expand-lg">
        <div class="container-fluid">
            <button class="btn d-lg-none me-3" id="sidebarToggle">
                <i class="bi bi-list text-white"></i>
            </button>
            
            <a class="navbar-brand d-flex align-items-center" href="https://abilityseminarsgroup.com/admin-dashboard/">
                <img src="https://abilityseminarsgroup.com/wp-content/uploads/2023/09/Asset-6-4-768x355.png" 
                     alt="AbilitySeminarsGroup" style="width:130px;height:auto;">
            </a>
            
            <div class="navbar-nav ms-auto">
                <a class="nav-link text-white" href="https://abilityseminarsgroup.com/admin-dashboard/">
                    <i class="bi bi-house-door me-1"></i>Dashboard
                </a>
                <a class="nav-link text-white" href="https://abilityseminarsgroup.com/all-courses/">
                    <i class="bi bi-arrow-left me-1"></i>Back to Courses
                </a>
            </div>
        </div>
    </nav>

    <!-- Sidebar -->
    <div class="sidebar" id="sidebar">
        <div class="p-3 border-bottom">
            <h5 class="mb-0"><i class="bi bi-gear me-2"></i>Course Management</h5>
        </div>
        <div class="p-2">
            <a href="https://abilityseminarsgroup.com/admin-dashboard/" class="d-block p-2 text-decoration-none text-dark">
                <i class="bi bi-speedometer2 me-2"></i>Dashboard
            </a>
            <a href="https://abilityseminarsgroup.com/all-courses/" class="d-block p-2 text-decoration-none text-dark">
                <i class="bi bi-collection me-2"></i>All Courses
            </a>
            <a href="https://abilityseminarsgroup.com/new-course/" class="d-block p-2 text-decoration-none text-dark">
                <i class="bi bi-plus-circle me-2"></i>New Course
            </a>
            <a href="#" class="d-block p-2 text-decoration-none text-primary fw-bold">
                <i class="bi bi-pencil-square me-2"></i>Edit Course
            </a>
        </div>
    </div>

    <!-- Main Content -->
    <div class="main-content">
        <!-- Page Header -->
        <div class="page-header">
            <h1 class="text-primary mb-2">
                <i class="bi bi-pencil-square me-2"></i>Edit Course
            </h1>
            <p class="text-muted mb-0">Modify course information, modules, and settings</p>
        </div>

        <!-- Loading State -->
        <div id="loadingState" class="loading-state">
            <i class="bi bi-arrow-clockwise fs-1"></i>
            <h3>Loading Course Data...</h3>
            <p>Please wait while we fetch the course information.</p>
        </div>

        <!-- Error State -->
        <div id="errorState" class="error-state">
            <i class="bi bi-exclamation-triangle fs-1"></i>
            <h3>Error Loading Course</h3>
            <p id="errorMessage">Unable to load course data. Please try again.</p>
            <button class="btn btn-primary" onclick="location.reload()">
                <i class="bi bi-arrow-clockwise me-1"></i>Retry
            </button>
        </div>

        <!-- Course Content -->
        <div id="courseContent" style="display: none;">
            <!-- Course Info -->
            <div class="form-section">
                <div class="row">
                    <div class="col-md-4">
                        <img id="courseImage" src="" alt="Course Image" class="img-fluid rounded mb-3" style="max-height: 200px;">
                    </div>
                    <div class="col-md-8">
                        <h2 id="courseTitle" class="text-primary">Course Title</h2>
                        <p id="courseDescription" class="text-muted">Course description...</p>
                        <div class="d-flex gap-3 flex-wrap">
                            <span><i class="bi bi-calendar text-warning"></i> <span id="courseDate">Date</span></span>
                            <span><i class="bi bi-tag text-warning"></i> <span id="courseCategory">Category</span></span>
                            <span><i class="bi bi-currency-dollar text-warning"></i> <span id="coursePrice">Price</span></span>
                            <span><i class="bi bi-eye text-warning"></i> <span id="courseStatus">Status</span></span>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Basic Information Form -->
            <div class="form-section">
                <h3 class="section-title">
                    <i class="bi bi-info-circle me-2"></i>Basic Information
                </h3>
                <form id="basicInfoForm">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="title">Course Title *</label>
                                <input type="text" id="title" name="title" required>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="category">Category *</label>
                                <select id="category" name="category" required>
                                    <option value="">Select Category</option>
                                    <option value="technology">Technology</option>
                                    <option value="business">Business</option>
                                    <option value="design">Design</option>
                                    <option value="marketing">Marketing</option>
                                    <option value="personal-development">Personal Development</option>
                                </select>
                            </div>
                        </div>
                    </div>
                    
                    <div class="form-group">
                        <label for="description">Description *</label>
                        <textarea id="description" name="description" rows="4" required></textarea>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-4">
                            <div class="form-group">
                                <label for="price">Price ($) *</label>
                                <input type="number" id="price" name="price" min="0" step="0.01" required>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="form-group">
                                <label for="duration">Duration (hours)</label>
                                <input type="number" id="duration" name="duration" min="0" step="0.5">
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="form-group">
                                <label for="status">Status *</label>
                                <select id="status" name="status" required>
                                    <option value="draft">Draft</option>
                                    <option value="published">Published</option>
                                    <option value="archived">Archived</option>
                                </select>
                            </div>
                        </div>
                    </div>
                    
                    <div class="form-group">
                        <label for="image_url">Course Image URL</label>
                        <input type="url" id="image_url" name="image_url" placeholder="https://example.com/image.jpg">
                    </div>
                    
                    <div class="d-flex gap-2">
                        <button type="submit" class="btn btn-primary">
                            <i class="bi bi-check-circle me-1"></i>Save Basic Info
                        </button>
                        <button type="button" class="btn btn-secondary" onclick="resetForm()">
                            <i class="bi bi-arrow-clockwise me-1"></i>Reset
                        </button>
                    </div>
                </form>
            </div>

            <!-- Modules Section -->
            <div class="form-section">
                <h3 class="section-title">
                    <i class="bi bi-collection me-2"></i>Course Modules
                </h3>
                
                <div class="d-flex justify-content-between align-items-center mb-3">
                    <p class="text-muted mb-0">Manage your course modules</p>
                    <button class="btn btn-success" onclick="addNewModule()">
                        <i class="bi bi-plus-circle me-1"></i>Add Module
                    </button>
                </div>
                
                <div id="modulesContainer">
                    <!-- Modules will be loaded here -->
                </div>
            </div>

            <!-- Objectives and Benefits Section -->
            <div class="form-section">
                <h3 class="section-title">
                    <i class="bi bi-bullseye me-2"></i>Learning Objectives & Benefits
                </h3>

                <div class="row">
                    <div class="col-md-6">
                        <h5>Learning Objectives</h5>
                        <p class="text-muted">What will students learn from this course?</p>
                        <div id="objectivesContainer">
                            <!-- Objectives will be loaded here -->
                        </div>
                        <button class="btn btn-outline-primary btn-sm mt-2" onclick="addObjective()">
                            <i class="bi bi-plus-circle me-1"></i>Add Objective
                        </button>
                    </div>
                    <div class="col-md-6">
                        <h5>Course Benefits</h5>
                        <p class="text-muted">What benefits will students gain?</p>
                        <div id="benefitsContainer">
                            <!-- Benefits will be loaded here -->
                        </div>
                        <button class="btn btn-outline-success btn-sm mt-2" onclick="addBenefit()">
                            <i class="bi bi-plus-circle me-1"></i>Add Benefit
                        </button>
                    </div>
                </div>

                <div class="d-flex gap-2 mt-3">
                    <button class="btn btn-primary" onclick="saveObjectivesAndBenefits()">
                        <i class="bi bi-check-circle me-1"></i>Save Objectives & Benefits
                    </button>
                </div>
            </div>

            <!-- Action Buttons -->
            <div class="form-section">
                <div class="d-flex gap-2 justify-content-between">
                    <div>
                        <button class="btn btn-success" onclick="saveAllChanges()">
                            <i class="bi bi-check-circle me-1"></i>Save All Changes
                        </button>
                        <button class="btn btn-secondary" onclick="previewCourse()">
                            <i class="bi bi-eye me-1"></i>Preview Course
                        </button>
                        <button class="btn btn-info" onclick="duplicateCourse()">
                            <i class="bi bi-copy me-1"></i>Duplicate Course
                        </button>
                    </div>
                    <div>
                        <button class="btn btn-warning" onclick="showCourseHistory()">
                            <i class="bi bi-clock-history me-1"></i>History
                        </button>
                        <button class="btn btn-danger" onclick="deleteCourse()">
                            <i class="bi bi-trash me-1"></i>Delete Course
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.3/dist/js/bootstrap.bundle.min.js"></script>

    <script>
        // Variables globales
        let currentCourse = null;
        let courseModules = [];
        let courseObjectives = [];
        let courseBenefits = [];
        let hasUnsavedChanges = false;

        // Inicialización
        document.addEventListener('DOMContentLoaded', function() {
            console.log('🚀 Iniciando sistema de edición de cursos...');

            setupSidebar();

            const urlParams = new URLSearchParams(window.location.search);
            const courseId = urlParams.get('id');

            if (courseId) {
                loadCourseData(courseId);
            } else {
                showError('No course ID provided. Please select a course to edit.');
                setTimeout(() => {
                    window.location.href = 'https://abilityseminarsgroup.com/all-courses/';
                }, 3000);
            }

            setupForms();
        });

        // Configurar sidebar
        function setupSidebar() {
            const sidebarToggle = document.getElementById('sidebarToggle');
            const sidebar = document.getElementById('sidebar');

            if (sidebarToggle) {
                sidebarToggle.addEventListener('click', function() {
                    sidebar.classList.toggle('active');
                });
            }
        }

        // Cargar datos del curso
        async function loadCourseData(courseId) {
            try {
                console.log('📡 Cargando datos del curso:', courseId);

                showLoading();

                const response = await fetch(`https://abilityseminarsgroup.com/wp-json/asg/v1/courses/${courseId}`);
                const result = await response.json();

                if (result.success && result.data) {
                    currentCourse = result.data;
                    courseModules = result.data.modules || [];

                    populateCourseData(currentCourse);
                    hideLoading();

                } else {
                    throw new Error(result.message || 'Failed to load course data');
                }

            } catch (error) {
                console.error('❌ Error cargando curso:', error);
                showError(`Error loading course: ${error.message}`);
            }
        }

        // Poblar datos en la interfaz
        function populateCourseData(course) {
            document.getElementById('courseTitle').textContent = course.title || 'Untitled Course';
            document.getElementById('courseDescription').textContent = course.description || 'No description available';
            document.getElementById('courseDate').textContent = formatDate(course.created_at);
            document.getElementById('courseCategory').textContent = course.category || 'Uncategorized';
            document.getElementById('coursePrice').textContent = `$${course.price || '0.00'}`;
            document.getElementById('courseStatus').textContent = course.status || 'draft';

            const courseImage = document.getElementById('courseImage');
            if (course.image_url) {
                courseImage.src = course.image_url;
                courseImage.style.display = 'block';
            } else {
                courseImage.style.display = 'none';
            }

            // Formulario básico
            document.getElementById('title').value = course.title || '';
            document.getElementById('description').value = course.description || '';
            document.getElementById('category').value = course.category || '';
            document.getElementById('price').value = course.price || '';
            document.getElementById('duration').value = course.duration || '';
            document.getElementById('status').value = course.status || 'draft';
            document.getElementById('image_url').value = course.image_url || '';

            // Cargar módulos, objetivos y beneficios
            loadModules();

            // Cargar objetivos y beneficios si existen
            courseObjectives = course.objectives || [];
            courseBenefits = course.benefits || [];
            loadObjectivesAndBenefits();
        }

        // Cargar módulos
        function loadModules() {
            const container = document.getElementById('modulesContainer');

            if (!courseModules || courseModules.length === 0) {
                container.innerHTML = `
                    <div class="text-center py-4">
                        <i class="bi bi-collection text-muted" style="font-size: 3rem;"></i>
                        <h5 class="text-muted mt-2">No modules yet</h5>
                        <p class="text-muted">Add your first module to get started</p>
                        <button class="btn btn-primary" onclick="addNewModule()">
                            <i class="bi bi-plus-circle me-1"></i>Add First Module
                        </button>
                    </div>
                `;
                return;
            }

            container.innerHTML = '';

            courseModules.forEach((module, index) => {
                const moduleElement = createModuleElement(module, index);
                container.appendChild(moduleElement);
            });
        }

        // Crear elemento de módulo
        function createModuleElement(module, index) {
            const moduleDiv = document.createElement('div');
            moduleDiv.className = 'module-item';
            moduleDiv.setAttribute('data-module-id', module.id || index);
            moduleDiv.setAttribute('data-order-index', index);

            moduleDiv.innerHTML = `
                <div class="module-drag-handle" title="Drag to reorder">
                    <i class="bi bi-grip-vertical text-muted"></i>
                </div>
                <div class="module-image">
                    ${module.image_url ?
                        `<img src="${module.image_url}" alt="Module Image" style="width: 100%; height: 100%; object-fit: cover;">` :
                        '<i class="bi bi-image text-muted" style="font-size: 1.5rem;"></i>'
                    }
                </div>
                <div class="module-info">
                    <div class="module-title">${module.title || 'Untitled Module'}</div>
                    <div class="module-description">${module.description || 'No description'}</div>
                    <div class="module-meta">
                        <small class="text-muted">Order: ${index + 1}</small>
                        ${module.id ? `<small class="text-muted">ID: ${module.id}</small>` : ''}
                    </div>
                </div>
                <div class="module-actions">
                    <button class="btn btn-sm btn-outline-primary" onclick="editModuleAdvanced(${index})" title="Edit Module">
                        <i class="bi bi-pencil"></i>
                    </button>
                    <button class="btn btn-sm btn-outline-secondary" onclick="duplicateModule(${index})" title="Duplicate Module">
                        <i class="bi bi-copy"></i>
                    </button>
                    <button class="btn btn-sm btn-outline-success" onclick="moveModuleUp(${index})" ${index === 0 ? 'disabled' : ''} title="Move Up">
                        <i class="bi bi-arrow-up"></i>
                    </button>
                    <button class="btn btn-sm btn-outline-success" onclick="moveModuleDown(${index})" ${index === courseModules.length - 1 ? 'disabled' : ''} title="Move Down">
                        <i class="bi bi-arrow-down"></i>
                    </button>
                    <button class="btn btn-sm btn-outline-danger" onclick="deleteModuleAdvanced(${index})" title="Delete Module">
                        <i class="bi bi-trash"></i>
                    </button>
                </div>
            `;

            // Make draggable
            moduleDiv.draggable = true;
            moduleDiv.addEventListener('dragstart', handleDragStart);
            moduleDiv.addEventListener('dragover', handleDragOver);
            moduleDiv.addEventListener('drop', handleDrop);
            moduleDiv.addEventListener('dragend', handleDragEnd);

            return moduleDiv;
        }

        // Configurar formularios
        function setupForms() {
            const basicInfoForm = document.getElementById('basicInfoForm');

            if (basicInfoForm) {
                basicInfoForm.addEventListener('submit', async function(e) {
                    e.preventDefault();
                    await saveBasicInfo();
                });

                const inputs = basicInfoForm.querySelectorAll('input, textarea, select');
                inputs.forEach(input => {
                    input.addEventListener('change', () => {
                        hasUnsavedChanges = true;
                    });
                });
            }
        }

        // Guardar información básica
        async function saveBasicInfo() {
            try {
                console.log('💾 Guardando información básica...');

                const formData = new FormData(document.getElementById('basicInfoForm'));
                const courseData = {
                    title: formData.get('title'),
                    description: formData.get('description'),
                    category: formData.get('category'),
                    price: parseFloat(formData.get('price')) || 0,
                    duration: parseFloat(formData.get('duration')) || 0,
                    status: formData.get('status'),
                    image_url: formData.get('image_url') || null
                };

                const response = await fetch(`https://abilityseminarsgroup.com/wp-json/asg/v1/courses/${currentCourse.course_id}`, {
                    method: 'PUT',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify(courseData)
                });

                const result = await response.json();

                if (result.success) {
                    Object.assign(currentCourse, courseData);
                    populateCourseData(currentCourse);

                    hasUnsavedChanges = false;
                    showAlert('success', 'Course information updated successfully!');

                } else {
                    throw new Error(result.message || 'Failed to update course');
                }

            } catch (error) {
                console.error('❌ Error guardando información básica:', error);
                showAlert('danger', `Error updating course: ${error.message}`);
            }
        }

        // Agregar nuevo módulo
        function addNewModule() {
            const title = prompt('Enter module title:');
            if (!title) return;

            const description = prompt('Enter module description (optional):') || '';

            const newModule = {
                title: title,
                description: description,
                image_url: null,
                order_index: courseModules.length
            };

            courseModules.push(newModule);
            loadModules();
            hasUnsavedChanges = true;
        }

        // Editar módulo
        function editModule(index) {
            const module = courseModules[index];
            if (!module) return;

            const newTitle = prompt('Edit module title:', module.title);
            if (newTitle === null) return;

            const newDescription = prompt('Edit module description:', module.description || '');
            if (newDescription === null) return;

            const newImageUrl = prompt('Edit module image URL (optional):', module.image_url || '');

            module.title = newTitle;
            module.description = newDescription;
            module.image_url = newImageUrl || null;

            loadModules();
            hasUnsavedChanges = true;
        }

        // Eliminar módulo
        function deleteModule(index) {
            const module = courseModules[index];
            if (!module) return;

            if (!confirm(`Are you sure you want to delete the module "${module.title}"?`)) {
                return;
            }

            courseModules.splice(index, 1);
            courseModules.forEach((mod, idx) => {
                mod.order_index = idx;
            });

            loadModules();
            hasUnsavedChanges = true;
        }

        // Guardar todos los cambios
        async function saveAllChanges() {
            try {
                await saveBasicInfo();

                if (courseModules.length > 0) {
                    await saveModules();
                }

                showAlert('success', 'All changes saved successfully!');
                hasUnsavedChanges = false;

            } catch (error) {
                showAlert('danger', `Error saving changes: ${error.message}`);
            }
        }

        // Guardar módulos
        async function saveModules() {
            const response = await fetch(`https://abilityseminarsgroup.com/wp-json/asg/v1/courses/${currentCourse.course_id}/modules`, {
                method: 'PUT',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({ modules: courseModules })
            });

            const result = await response.json();

            if (!result.success) {
                throw new Error(result.message || 'Failed to save modules');
            }
        }

        /**
         * ========================================
         * FUNCIONES AVANZADAS DE MÓDULOS - FASE 3
         * ========================================
         */

        // Variables para drag & drop
        let draggedElement = null;
        let draggedIndex = null;

        /**
         * Mover módulo hacia arriba
         */
        function moveModuleUp(index) {
            if (index <= 0) return;

            const temp = courseModules[index];
            courseModules[index] = courseModules[index - 1];
            courseModules[index - 1] = temp;

            // Actualizar índices
            courseModules[index].order_index = index;
            courseModules[index - 1].order_index = index - 1;

            loadModules();
            hasUnsavedChanges = true;

            console.log('⬆️ Módulo movido hacia arriba');
        }

        /**
         * Mover módulo hacia abajo
         */
        function moveModuleDown(index) {
            if (index >= courseModules.length - 1) return;

            const temp = courseModules[index];
            courseModules[index] = courseModules[index + 1];
            courseModules[index + 1] = temp;

            // Actualizar índices
            courseModules[index].order_index = index;
            courseModules[index + 1].order_index = index + 1;

            loadModules();
            hasUnsavedChanges = true;

            console.log('⬇️ Módulo movido hacia abajo');
        }

        /**
         * Duplicar módulo
         */
        function duplicateModule(index) {
            const originalModule = courseModules[index];
            if (!originalModule) return;

            const duplicatedModule = {
                title: originalModule.title + ' (Copy)',
                description: originalModule.description,
                image_url: originalModule.image_url,
                order_index: courseModules.length
            };

            courseModules.push(duplicatedModule);
            loadModules();
            hasUnsavedChanges = true;

            showAlert('success', 'Module duplicated successfully');
            console.log('📋 Módulo duplicado:', duplicatedModule);
        }

        /**
         * Edición avanzada de módulo
         */
        function editModuleAdvanced(index) {
            const module = courseModules[index];
            if (!module) return;

            // Usar prompt mejorado por ahora (se puede mejorar con modal más adelante)
            const newTitle = prompt('Edit module title:', module.title);
            if (newTitle === null) return;

            const newDescription = prompt('Edit module description:', module.description || '');
            if (newDescription === null) return;

            const newImageUrl = prompt('Edit module image URL (optional):', module.image_url || '');

            module.title = newTitle;
            module.description = newDescription;
            module.image_url = newImageUrl || null;

            loadModules();
            hasUnsavedChanges = true;

            showAlert('success', 'Module updated successfully');
            console.log('✏️ Módulo editado avanzado:', module);
        }

        /**
         * Eliminar módulo avanzado
         */
        function deleteModuleAdvanced(index) {
            const module = courseModules[index];
            if (!module) return;

            if (!confirm(`Are you sure you want to delete the module "${module.title}"?\n\nThis action cannot be undone.`)) {
                return;
            }

            courseModules.splice(index, 1);

            // Reordenar índices
            courseModules.forEach((mod, idx) => {
                mod.order_index = idx;
            });

            loadModules();
            hasUnsavedChanges = true;

            showAlert('success', 'Module deleted successfully');
            console.log('🗑️ Módulo eliminado avanzado');
        }

        /**
         * Manejar inicio de arrastre
         */
        function handleDragStart(e) {
            draggedElement = this;
            draggedIndex = parseInt(this.getAttribute('data-order-index'));
            this.style.opacity = '0.5';
            e.dataTransfer.effectAllowed = 'move';
            e.dataTransfer.setData('text/html', this.outerHTML);
        }

        /**
         * Manejar arrastre sobre elemento
         */
        function handleDragOver(e) {
            if (e.preventDefault) {
                e.preventDefault();
            }
            e.dataTransfer.dropEffect = 'move';
            return false;
        }

        /**
         * Manejar soltar elemento
         */
        function handleDrop(e) {
            if (e.stopPropagation) {
                e.stopPropagation();
            }

            if (draggedElement !== this) {
                const targetIndex = parseInt(this.getAttribute('data-order-index'));

                // Reordenar array de módulos
                const draggedModule = courseModules[draggedIndex];
                courseModules.splice(draggedIndex, 1);
                courseModules.splice(targetIndex, 0, draggedModule);

                // Actualizar índices
                courseModules.forEach((module, index) => {
                    module.order_index = index;
                });

                // Recargar módulos
                loadModules();
                hasUnsavedChanges = true;

                showAlert('success', 'Module reordered successfully');
                console.log(`📦 Módulo movido de posición ${draggedIndex} a ${targetIndex}`);
            }

            return false;
        }

        /**
         * Manejar fin de arrastre
         */
        function handleDragEnd(e) {
            this.style.opacity = '1';
            draggedElement = null;
            draggedIndex = null;
        }

        /**
         * ========================================
         * FUNCIONES DE OBJETIVOS Y BENEFICIOS
         * ========================================
         */

        /**
         * Cargar objetivos y beneficios
         */
        function loadObjectivesAndBenefits() {
            loadObjectives();
            loadBenefits();
        }

        /**
         * Cargar objetivos
         */
        function loadObjectives() {
            const container = document.getElementById('objectivesContainer');

            if (!courseObjectives || courseObjectives.length === 0) {
                container.innerHTML = `
                    <div class="text-center py-3 border rounded bg-light">
                        <i class="bi bi-bullseye text-muted" style="font-size: 2rem;"></i>
                        <p class="text-muted mb-0 mt-2">No learning objectives yet</p>
                    </div>
                `;
                return;
            }

            container.innerHTML = '';

            courseObjectives.forEach((objective, index) => {
                const objectiveElement = createObjectiveElement(objective, index);
                container.appendChild(objectiveElement);
            });
        }

        /**
         * Cargar beneficios
         */
        function loadBenefits() {
            const container = document.getElementById('benefitsContainer');

            if (!courseBenefits || courseBenefits.length === 0) {
                container.innerHTML = `
                    <div class="text-center py-3 border rounded bg-light">
                        <i class="bi bi-award text-muted" style="font-size: 2rem;"></i>
                        <p class="text-muted mb-0 mt-2">No benefits defined yet</p>
                    </div>
                `;
                return;
            }

            container.innerHTML = '';

            courseBenefits.forEach((benefit, index) => {
                const benefitElement = createBenefitElement(benefit, index);
                container.appendChild(benefitElement);
            });
        }

        /**
         * Crear elemento de objetivo
         */
        function createObjectiveElement(objective, index) {
            const div = document.createElement('div');
            div.className = 'objective-item mb-2 p-2 border rounded bg-white';
            div.innerHTML = `
                <div class="d-flex align-items-center">
                    <i class="bi bi-check-circle text-success me-2"></i>
                    <input type="text" class="form-control form-control-sm me-2" value="${objective.objective || objective}"
                           onchange="updateObjective(${index}, this.value)">
                    <button class="btn btn-sm btn-outline-danger" onclick="removeObjective(${index})" title="Remove">
                        <i class="bi bi-trash"></i>
                    </button>
                </div>
            `;
            return div;
        }

        /**
         * Crear elemento de beneficio
         */
        function createBenefitElement(benefit, index) {
            const div = document.createElement('div');
            div.className = 'benefit-item mb-2 p-2 border rounded bg-white';
            div.innerHTML = `
                <div class="d-flex align-items-center">
                    <i class="bi bi-award text-warning me-2"></i>
                    <input type="text" class="form-control form-control-sm me-2" value="${benefit.benefit || benefit}"
                           onchange="updateBenefit(${index}, this.value)">
                    <button class="btn btn-sm btn-outline-danger" onclick="removeBenefit(${index})" title="Remove">
                        <i class="bi bi-trash"></i>
                    </button>
                </div>
            `;
            return div;
        }

        /**
         * Agregar nuevo objetivo
         */
        function addObjective() {
            const newObjective = {
                objective: 'New learning objective',
                order_index: courseObjectives.length
            };

            courseObjectives.push(newObjective);
            loadObjectives();
            hasUnsavedChanges = true;

            console.log('🎯 Objetivo agregado:', newObjective);
        }

        /**
         * Agregar nuevo beneficio
         */
        function addBenefit() {
            const newBenefit = {
                benefit: 'New course benefit',
                order_index: courseBenefits.length
            };

            courseBenefits.push(newBenefit);
            loadBenefits();
            hasUnsavedChanges = true;

            console.log('🏆 Beneficio agregado:', newBenefit);
        }

        /**
         * Actualizar objetivo
         */
        function updateObjective(index, value) {
            if (courseObjectives[index]) {
                courseObjectives[index].objective = value;
                hasUnsavedChanges = true;
                console.log('🎯 Objetivo actualizado:', courseObjectives[index]);
            }
        }

        /**
         * Actualizar beneficio
         */
        function updateBenefit(index, value) {
            if (courseBenefits[index]) {
                courseBenefits[index].benefit = value;
                hasUnsavedChanges = true;
                console.log('🏆 Beneficio actualizado:', courseBenefits[index]);
            }
        }

        /**
         * Remover objetivo
         */
        function removeObjective(index) {
            if (confirm('Are you sure you want to remove this objective?')) {
                courseObjectives.splice(index, 1);

                // Reordenar índices
                courseObjectives.forEach((obj, idx) => {
                    obj.order_index = idx;
                });

                loadObjectives();
                hasUnsavedChanges = true;
                console.log('🗑️ Objetivo eliminado');
            }
        }

        /**
         * Remover beneficio
         */
        function removeBenefit(index) {
            if (confirm('Are you sure you want to remove this benefit?')) {
                courseBenefits.splice(index, 1);

                // Reordenar índices
                courseBenefits.forEach((ben, idx) => {
                    ben.order_index = idx;
                });

                loadBenefits();
                hasUnsavedChanges = true;
                console.log('🗑️ Beneficio eliminado');
            }
        }

        /**
         * Guardar objetivos y beneficios
         */
        async function saveObjectivesAndBenefits() {
            try {
                console.log('💾 Guardando objetivos y beneficios...');

                // Guardar objetivos
                if (courseObjectives.length > 0) {
                    const objectivesResponse = await fetch(`https://abilityseminarsgroup.com/wp-json/asg/v1/courses/${currentCourse.course_id}/objectives`, {
                        method: 'PUT',
                        headers: {
                            'Content-Type': 'application/json'
                        },
                        body: JSON.stringify({ objectives: courseObjectives })
                    });

                    const objectivesResult = await objectivesResponse.json();
                    if (!objectivesResult.success) {
                        throw new Error('Failed to save objectives: ' + (objectivesResult.error?.message || 'Unknown error'));
                    }
                }

                // Guardar beneficios
                if (courseBenefits.length > 0) {
                    const benefitsResponse = await fetch(`https://abilityseminarsgroup.com/wp-json/asg/v1/courses/${currentCourse.course_id}/benefits`, {
                        method: 'PUT',
                        headers: {
                            'Content-Type': 'application/json'
                        },
                        body: JSON.stringify({ benefits: courseBenefits })
                    });

                    const benefitsResult = await benefitsResponse.json();
                    if (!benefitsResult.success) {
                        throw new Error('Failed to save benefits: ' + (benefitsResult.error?.message || 'Unknown error'));
                    }
                }

                showAlert('success', 'Objectives and benefits saved successfully!');
                console.log('✅ Objetivos y beneficios guardados exitosamente');

            } catch (error) {
                console.error('❌ Error guardando objetivos y beneficios:', error);
                showAlert('danger', `Error saving objectives and benefits: ${error.message}`);
            }
        }

        // Vista previa del curso
        function previewCourse() {
            if (hasUnsavedChanges) {
                if (!confirm('You have unsaved changes. Do you want to continue without saving?')) {
                    return;
                }
            }

            window.open(`https://abilityseminarsgroup.com/course-preview/?id=${currentCourse.course_id}`, '_blank');
        }

        // Eliminar curso
        async function deleteCourse() {
            if (!confirm('Are you sure you want to delete this course? This action cannot be undone.')) {
                return;
            }

            try {
                const response = await fetch(`https://abilityseminarsgroup.com/wp-json/asg/v1/courses/${currentCourse.course_id}`, {
                    method: 'DELETE'
                });

                const result = await response.json();

                if (result.success) {
                    showAlert('success', 'Course deleted successfully!');
                    setTimeout(() => {
                        window.location.href = 'https://abilityseminarsgroup.com/all-courses/';
                    }, 2000);
                } else {
                    throw new Error(result.message || 'Failed to delete course');
                }

            } catch (error) {
                showAlert('danger', `Error deleting course: ${error.message}`);
            }
        }

        // Resetear formulario
        function resetForm() {
            if (hasUnsavedChanges) {
                if (!confirm('This will reset all unsaved changes. Are you sure?')) {
                    return;
                }
            }

            if (currentCourse) {
                populateCourseData(currentCourse);
                hasUnsavedChanges = false;
                showAlert('info', 'Form reset to last saved state');
            }
        }

        // Mostrar estado de carga
        function showLoading() {
            document.getElementById('loadingState').style.display = 'block';
            document.getElementById('errorState').style.display = 'none';
            document.getElementById('courseContent').style.display = 'none';
        }

        // Ocultar estado de carga
        function hideLoading() {
            document.getElementById('loadingState').style.display = 'none';
            document.getElementById('courseContent').style.display = 'block';
        }

        // Mostrar error
        function showError(message) {
            document.getElementById('loadingState').style.display = 'none';
            document.getElementById('courseContent').style.display = 'none';
            document.getElementById('errorState').style.display = 'block';
            document.getElementById('errorMessage').textContent = message;
        }

        // Mostrar alerta
        function showAlert(type, message) {
            const alertDiv = document.createElement('div');
            alertDiv.className = `alert alert-${type} alert-dismissible fade show position-fixed`;
            alertDiv.style.cssText = 'top: 90px; right: 20px; z-index: 9999; min-width: 300px;';
            alertDiv.innerHTML = `
                ${message}
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            `;

            document.body.appendChild(alertDiv);

            setTimeout(() => {
                if (alertDiv.parentNode) {
                    alertDiv.remove();
                }
            }, 5000);
        }

        // Formatear fecha
        function formatDate(dateString) {
            if (!dateString) return 'N/A';

            try {
                const date = new Date(dateString);
                return date.toLocaleDateString('en-US', {
                    year: 'numeric',
                    month: 'short',
                    day: 'numeric'
                });
            } catch (error) {
                return 'Invalid Date';
            }
        }

        // Advertencia de cambios no guardados
        window.addEventListener('beforeunload', function(e) {
            if (hasUnsavedChanges) {
                e.preventDefault();
                e.returnValue = '';
            }
        });

        /**
         * ========================================
         * FUNCIONES ADICIONALES - FASE 3
         * ========================================
         */

        /**
         * Duplicar curso completo
         */
        async function duplicateCourse() {
            if (!currentCourse) {
                showAlert('warning', 'No course loaded to duplicate');
                return;
            }

            if (hasUnsavedChanges) {
                if (!confirm('You have unsaved changes. Do you want to save them before duplicating?')) {
                    return;
                }
                await saveAllChanges();
            }

            if (!confirm(`Are you sure you want to duplicate the course "${currentCourse.title}"?\n\nThis will create a new course with all modules and content.`)) {
                return;
            }

            try {
                console.log('📋 Duplicando curso...');

                const response = await fetch(`https://abilityseminarsgroup.com/wp-json/asg/v1/courses/${currentCourse.course_id}/duplicate`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    }
                });

                const result = await response.json();

                if (result.success) {
                    showAlert('success', 'Course duplicated successfully!');

                    // Preguntar si quiere ir al curso duplicado
                    if (confirm('Course duplicated successfully! Do you want to edit the new course?')) {
                        window.location.href = `https://abilityseminarsgroup.com/edit-course/?id=${result.data.new_course.course_id}`;
                    }

                } else {
                    throw new Error(result.error?.message || 'Failed to duplicate course');
                }

            } catch (error) {
                console.error('❌ Error duplicando curso:', error);
                showAlert('danger', `Error duplicating course: ${error.message}`);
            }
        }

        /**
         * Mostrar historial del curso
         */
        async function showCourseHistory() {
            if (!currentCourse) {
                showAlert('warning', 'No course loaded');
                return;
            }

            try {
                console.log('📜 Cargando historial del curso...');

                const response = await fetch(`https://abilityseminarsgroup.com/wp-json/asg/v1/courses/${currentCourse.course_id}/history`);
                const result = await response.json();

                if (result.success) {
                    displayCourseHistory(result.data);
                } else {
                    throw new Error(result.error?.message || 'Failed to load course history');
                }

            } catch (error) {
                console.error('❌ Error cargando historial:', error);
                showAlert('danger', `Error loading course history: ${error.message}`);
            }
        }

        /**
         * Mostrar historial en modal
         */
        function displayCourseHistory(historyData) {
            const modalHtml = `
                <div class="modal fade" id="courseHistoryModal" tabindex="-1">
                    <div class="modal-dialog modal-lg">
                        <div class="modal-content">
                            <div class="modal-header">
                                <h5 class="modal-title">
                                    <i class="bi bi-clock-history me-2"></i>Course History
                                </h5>
                                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                            </div>
                            <div class="modal-body">
                                <div class="row mb-3">
                                    <div class="col-md-6">
                                        <div class="card">
                                            <div class="card-body">
                                                <h6 class="card-title">Summary</h6>
                                                <p class="mb-1"><strong>Created:</strong> ${formatDate(historyData.summary.created_at)}</p>
                                                <p class="mb-1"><strong>Last Updated:</strong> ${formatDate(historyData.summary.last_updated)}</p>
                                                <p class="mb-1"><strong>Current Status:</strong> <span class="badge bg-primary">${historyData.summary.current_status}</span></p>
                                                <p class="mb-1"><strong>Modules:</strong> ${historyData.summary.modules_count}</p>
                                                <p class="mb-0"><strong>Total Changes:</strong> ${historyData.summary.total_changes}</p>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="card">
                                            <div class="card-body">
                                                <h6 class="card-title">Quick Stats</h6>
                                                <p class="mb-1"><strong>Course ID:</strong> ${historyData.course_id}</p>
                                                <p class="mb-1"><strong>Objectives:</strong> ${historyData.summary.objectives_count || 0}</p>
                                                <p class="mb-0"><strong>Benefits:</strong> ${historyData.summary.benefits_count || 0}</p>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <h6>Change History</h6>
                                <div class="timeline">
                                    ${historyData.history.map(entry => `
                                        <div class="timeline-item mb-3">
                                            <div class="d-flex">
                                                <div class="flex-shrink-0">
                                                    <div class="timeline-icon bg-primary text-white rounded-circle d-flex align-items-center justify-content-center" style="width: 40px; height: 40px;">
                                                        <i class="bi bi-${getHistoryIcon(entry.action)}"></i>
                                                    </div>
                                                </div>
                                                <div class="flex-grow-1 ms-3">
                                                    <div class="card">
                                                        <div class="card-body py-2">
                                                            <h6 class="card-title mb-1">${entry.description}</h6>
                                                            <p class="card-text text-muted mb-1">
                                                                <small>
                                                                    <i class="bi bi-person me-1"></i>${entry.user_name}
                                                                    <i class="bi bi-clock ms-2 me-1"></i>${formatDate(entry.timestamp)}
                                                                </small>
                                                            </p>
                                                            ${entry.details ? `
                                                                <div class="mt-2">
                                                                    <small class="text-muted">
                                                                        ${Object.entries(entry.details).map(([key, value]) =>
                                                                            `<span class="badge bg-light text-dark me-1">${key}: ${value}</span>`
                                                                        ).join('')}
                                                                    </small>
                                                                </div>
                                                            ` : ''}
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    `).join('')}
                                </div>
                            </div>
                            <div class="modal-footer">
                                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
                            </div>
                        </div>
                    </div>
                </div>
            `;

            // Remover modal existente si existe
            const existingModal = document.getElementById('courseHistoryModal');
            if (existingModal) {
                existingModal.remove();
            }

            // Agregar modal al DOM
            document.body.insertAdjacentHTML('beforeend', modalHtml);

            // Mostrar modal
            const modal = new bootstrap.Modal(document.getElementById('courseHistoryModal'));
            modal.show();
        }

        /**
         * Obtener icono para tipo de acción en historial
         */
        function getHistoryIcon(action) {
            const icons = {
                'created': 'plus-circle',
                'updated': 'pencil',
                'published': 'check-circle',
                'archived': 'archive',
                'deleted': 'trash',
                'duplicated': 'copy'
            };
            return icons[action] || 'clock';
        }

        console.log('✅ Sistema de edición de cursos cargado correctamente');
    </script>
</body>
</html>
