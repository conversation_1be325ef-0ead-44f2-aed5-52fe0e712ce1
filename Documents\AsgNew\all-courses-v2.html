<!DOCTYPE html>
<html lang="es">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Gestión de Cursos - AbilitySeminarsGroup</title>
    
    <!-- Preload critical resources -->
    <link rel="preload" href="./assets/css/asg-design-system.css" as="style">
    <link rel="preload" href="./assets/js/asg-components.js" as="script">
    
    <!-- Styles -->
    <link href="./assets/css/asg-design-system.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.11.3/font/bootstrap-icons.css" rel="stylesheet">
    
    <!-- Meta tags -->
    <meta name="description" content="Gestión completa de cursos - AbilitySeminarsGroup">
    <meta name="robots" content="noindex, nofollow">
    
    <!-- Favicon -->
    <link rel="icon" type="image/x-icon" href="https://abilityseminarsgroup.com/favicon.ico">
</head>
<body>
    <!-- Sidebar -->
    <aside class="asg-sidebar">
        <div class="asg-sidebar-header">
            <img src="https://abilityseminarsgroup.com/wp-content/uploads/2023/09/Asset-6-4-768x355.png" 
                 alt="AbilitySeminarsGroup" 
                 class="asg-sidebar-logo"
                 style="width: 130px; height: auto;">
        </div>
        
        <nav class="asg-sidebar-nav">
            <a href="https://abilityseminarsgroup.com/admin-dashboard/" class="asg-sidebar-item">
                <i class="bi bi-house asg-sidebar-icon"></i>
                <span>Dashboard</span>
            </a>
            
            <a href="#" class="asg-sidebar-item active">
                <i class="bi bi-collection asg-sidebar-icon"></i>
                <span>Todos los Cursos</span>
            </a>
            
            <a href="https://abilityseminarsgroup.com/new-course/" class="asg-sidebar-item">
                <i class="bi bi-plus-circle asg-sidebar-icon"></i>
                <span>Nuevo Curso</span>
            </a>
            
            <div class="asg-sidebar-divider" style="height: 1px; background: var(--asg-gray-200); margin: var(--asg-space-4) var(--asg-space-6);"></div>
            
            <a href="#" class="asg-sidebar-item">
                <i class="bi bi-people asg-sidebar-icon"></i>
                <span>Estudiantes</span>
            </a>
            
            <a href="#" class="asg-sidebar-item">
                <i class="bi bi-bar-chart asg-sidebar-icon"></i>
                <span>Analíticas</span>
            </a>
            
            <a href="#" class="asg-sidebar-item">
                <i class="bi bi-gear asg-sidebar-icon"></i>
                <span>Configuración</span>
            </a>
        </nav>
    </aside>
    
    <!-- Sidebar Overlay (Mobile) -->
    <div class="asg-sidebar-overlay"></div>
    
    <!-- Main Content -->
    <main class="asg-main">
        <!-- Header -->
        <header class="asg-header">
            <div style="display: flex; align-items: center; justify-content: space-between;">
                <div style="display: flex; align-items: center; gap: var(--asg-space-4);">
                    <button class="asg-sidebar-toggle asg-btn asg-btn-secondary" style="display: none;">
                        <i class="bi bi-list"></i>
                    </button>
                    <h1 style="margin: 0; font-size: var(--asg-text-2xl); font-weight: var(--asg-font-semibold); color: var(--asg-gray-900);">
                        Gestión de Cursos
                    </h1>
                    <span class="asg-badge asg-badge-primary" id="coursesCount">0 cursos</span>
                </div>
                
                <div style="display: flex; align-items: center; gap: var(--asg-space-3);">
                    <button class="asg-btn asg-btn-secondary" id="refreshBtn">
                        <i class="bi bi-arrow-clockwise"></i>
                        <span class="btn-text">Actualizar</span>
                    </button>
                    
                    <a href="https://abilityseminarsgroup.com/new-course/" class="asg-btn asg-btn-primary">
                        <i class="bi bi-plus-circle"></i>
                        <span class="btn-text">Nuevo Curso</span>
                    </a>
                </div>
            </div>
        </header>
        
        <!-- Content -->
        <div class="asg-content">
            <!-- Filters and Search -->
            <div class="asg-card" style="margin-bottom: var(--asg-space-6);">
                <div class="asg-card-body">
                    <div style="display: grid; grid-template-columns: 1fr auto auto auto; gap: var(--asg-space-4); align-items: end;">
                        <!-- Search -->
                        <div class="asg-form-group" style="margin-bottom: 0;">
                            <label for="searchInput" class="asg-label">Buscar cursos</label>
                            <div style="position: relative;">
                                <input type="text" 
                                       id="searchInput" 
                                       class="asg-input" 
                                       placeholder="Buscar por título, descripción o categoría..."
                                       style="padding-left: 2.5rem;">
                                <i class="bi bi-search" style="
                                    position: absolute; 
                                    left: var(--asg-space-3); 
                                    top: 50%; 
                                    transform: translateY(-50%); 
                                    color: var(--asg-gray-400);
                                "></i>
                            </div>
                        </div>
                        
                        <!-- Status Filter -->
                        <div class="asg-form-group" style="margin-bottom: 0;">
                            <label for="statusFilter" class="asg-label">Estado</label>
                            <select id="statusFilter" class="asg-input asg-select">
                                <option value="">Todos los estados</option>
                                <option value="published">Publicados</option>
                                <option value="draft">Borradores</option>
                                <option value="archived">Archivados</option>
                            </select>
                        </div>
                        
                        <!-- Category Filter -->
                        <div class="asg-form-group" style="margin-bottom: 0;">
                            <label for="categoryFilter" class="asg-label">Categoría</label>
                            <select id="categoryFilter" class="asg-input asg-select">
                                <option value="">Todas las categorías</option>
                                <option value="finanzas">Finanzas</option>
                                <option value="marketing">Marketing</option>
                                <option value="desarrollo-personal">Desarrollo Personal</option>
                                <option value="tecnologia">Tecnología</option>
                                <option value="negocios">Negocios</option>
                            </select>
                        </div>
                        
                        <!-- View Toggle -->
                        <div class="asg-form-group" style="margin-bottom: 0;">
                            <label class="asg-label">Vista</label>
                            <div class="view-toggle" style="display: flex; border: 1px solid var(--asg-gray-300); border-radius: var(--asg-radius); overflow: hidden;">
                                <button class="view-btn active" data-view="grid" style="
                                    padding: var(--asg-space-2) var(--asg-space-3);
                                    border: none;
                                    background: var(--asg-primary-600);
                                    color: white;
                                    cursor: pointer;
                                    transition: all var(--asg-transition);
                                ">
                                    <i class="bi bi-grid-3x3-gap"></i>
                                </button>
                                <button class="view-btn" data-view="list" style="
                                    padding: var(--asg-space-2) var(--asg-space-3);
                                    border: none;
                                    background: var(--asg-white);
                                    color: var(--asg-gray-600);
                                    cursor: pointer;
                                    transition: all var(--asg-transition);
                                ">
                                    <i class="bi bi-list-ul"></i>
                                </button>
                            </div>
                        </div>
                    </div>
                    
                    <!-- Active Filters -->
                    <div id="activeFilters" style="margin-top: var(--asg-space-4); display: none;">
                        <div style="display: flex; align-items: center; gap: var(--asg-space-2); flex-wrap: wrap;">
                            <span style="font-size: var(--asg-text-sm); color: var(--asg-gray-600); font-weight: var(--asg-font-medium);">
                                Filtros activos:
                            </span>
                            <div id="filterTags" style="display: flex; gap: var(--asg-space-2); flex-wrap: wrap;"></div>
                            <button id="clearFilters" class="asg-btn asg-btn-sm" style="
                                background: none; 
                                border: none; 
                                color: var(--asg-primary-600); 
                                text-decoration: underline;
                                padding: 0;
                                font-size: var(--asg-text-sm);
                            ">
                                Limpiar filtros
                            </button>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- Courses Grid/List -->
            <div id="coursesContainer">
                <!-- Loading State -->
                <div id="loadingState" class="loading-grid" style="
                    display: grid; 
                    grid-template-columns: repeat(auto-fill, minmax(350px, 1fr)); 
                    gap: var(--asg-space-6);
                ">
                    <!-- Loading cards -->
                    <div class="asg-card loading-card" style="animation: pulse 2s infinite;">
                        <div style="height: 200px; background: var(--asg-gray-200);"></div>
                        <div class="asg-card-body">
                            <div style="height: 1.5rem; background: var(--asg-gray-200); border-radius: var(--asg-radius-sm); margin-bottom: var(--asg-space-3);"></div>
                            <div style="height: 1rem; background: var(--asg-gray-200); border-radius: var(--asg-radius-sm); margin-bottom: var(--asg-space-2);"></div>
                            <div style="height: 1rem; background: var(--asg-gray-200); border-radius: var(--asg-radius-sm); width: 70%;"></div>
                        </div>
                    </div>
                    <div class="asg-card loading-card" style="animation: pulse 2s infinite;">
                        <div style="height: 200px; background: var(--asg-gray-200);"></div>
                        <div class="asg-card-body">
                            <div style="height: 1.5rem; background: var(--asg-gray-200); border-radius: var(--asg-radius-sm); margin-bottom: var(--asg-space-3);"></div>
                            <div style="height: 1rem; background: var(--asg-gray-200); border-radius: var(--asg-radius-sm); margin-bottom: var(--asg-space-2);"></div>
                            <div style="height: 1rem; background: var(--asg-gray-200); border-radius: var(--asg-radius-sm); width: 70%;"></div>
                        </div>
                    </div>
                    <div class="asg-card loading-card" style="animation: pulse 2s infinite;">
                        <div style="height: 200px; background: var(--asg-gray-200);"></div>
                        <div class="asg-card-body">
                            <div style="height: 1.5rem; background: var(--asg-gray-200); border-radius: var(--asg-radius-sm); margin-bottom: var(--asg-space-3);"></div>
                            <div style="height: 1rem; background: var(--asg-gray-200); border-radius: var(--asg-radius-sm); margin-bottom: var(--asg-space-2);"></div>
                            <div style="height: 1rem; background: var(--asg-gray-200); border-radius: var(--asg-radius-sm); width: 70%;"></div>
                        </div>
                    </div>
                </div>
                
                <!-- Courses Grid -->
                <div id="coursesGrid" style="
                    display: none;
                    grid-template-columns: repeat(auto-fill, minmax(350px, 1fr)); 
                    gap: var(--asg-space-6);
                "></div>
                
                <!-- Courses List -->
                <div id="coursesList" style="display: none; flex-direction: column; gap: var(--asg-space-4);"></div>
                
                <!-- Empty State -->
                <div id="emptyState" style="display: none; text-align: center; padding: var(--asg-space-16) var(--asg-space-8);">
                    <i class="bi bi-collection" style="font-size: 4rem; color: var(--asg-gray-400); margin-bottom: var(--asg-space-4);"></i>
                    <h3 style="margin: 0 0 var(--asg-space-2) 0; font-size: var(--asg-text-xl); font-weight: var(--asg-font-semibold); color: var(--asg-gray-700);">
                        No se encontraron cursos
                    </h3>
                    <p style="margin: 0 0 var(--asg-space-6) 0; color: var(--asg-gray-500); max-width: 400px; margin-left: auto; margin-right: auto;">
                        No hay cursos que coincidan con los filtros seleccionados. Intenta ajustar los criterios de búsqueda.
                    </p>
                    <a href="https://abilityseminarsgroup.com/new-course/" class="asg-btn asg-btn-primary">
                        <i class="bi bi-plus-circle"></i>
                        Crear Primer Curso
                    </a>
                </div>
                
                <!-- Error State -->
                <div id="errorState" style="display: none; text-align: center; padding: var(--asg-space-16) var(--asg-space-8);">
                    <i class="bi bi-exclamation-triangle" style="font-size: 4rem; color: var(--asg-error); margin-bottom: var(--asg-space-4);"></i>
                    <h3 style="margin: 0 0 var(--asg-space-2) 0; font-size: var(--asg-text-xl); font-weight: var(--asg-font-semibold); color: var(--asg-gray-700);">
                        Error al cargar los cursos
                    </h3>
                    <p style="margin: 0 0 var(--asg-space-6) 0; color: var(--asg-gray-500); max-width: 400px; margin-left: auto; margin-right: auto;">
                        Hubo un problema al cargar los cursos. Por favor, intenta nuevamente.
                    </p>
                    <button class="asg-btn asg-btn-primary" onclick="coursesManager.loadCourses()">
                        <i class="bi bi-arrow-clockwise"></i>
                        Reintentar
                    </button>
                </div>
            </div>
            
            <!-- Pagination -->
            <div id="pagination" style="display: none; margin-top: var(--asg-space-8); text-align: center;">
                <div style="display: inline-flex; align-items: center; gap: var(--asg-space-2);">
                    <button id="prevPage" class="asg-btn asg-btn-secondary" disabled>
                        <i class="bi bi-chevron-left"></i>
                        Anterior
                    </button>
                    
                    <div id="pageNumbers" style="display: flex; gap: var(--asg-space-1);"></div>
                    
                    <button id="nextPage" class="asg-btn asg-btn-secondary">
                        Siguiente
                        <i class="bi bi-chevron-right"></i>
                    </button>
                </div>
                
                <div style="margin-top: var(--asg-space-4); font-size: var(--asg-text-sm); color: var(--asg-gray-600);">
                    <span id="paginationInfo">Mostrando 0 de 0 cursos</span>
                </div>
            </div>
        </div>
    </main>
    
    <!-- Scripts -->
    <script src="./assets/js/asg-components.js"></script>
    <script src="./assets/js/courses-manager-v2.js"></script>
    
    <!-- Styles -->
    <style>
        @keyframes pulse {
            0%, 100% { opacity: 1; }
            50% { opacity: 0.5; }
        }
        
        .view-btn.active {
            background: var(--asg-primary-600) !important;
            color: white !important;
        }
        
        .course-card {
            transition: all var(--asg-transition);
            cursor: pointer;
        }
        
        .course-card:hover {
            transform: translateY(-2px);
            box-shadow: var(--asg-shadow-lg);
        }
        
        @media (max-width: 1024px) {
            .asg-sidebar-toggle {
                display: flex !important;
            }
            
            .btn-text {
                display: none;
            }
        }
        
        @media (max-width: 768px) {
            .asg-content {
                padding: var(--asg-space-4) var(--asg-space-3);
            }
            
            .loading-grid,
            #coursesGrid {
                grid-template-columns: 1fr !important;
            }
        }
    </style>
</body>
</html>
