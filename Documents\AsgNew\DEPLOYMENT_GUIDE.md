# 🚀 Guía de Despliegue - ASG Course Management v2.0

## 📋 **REQUISITOS DEL SISTEMA**

### **Servidor Web**
- ✅ **Apache 2.4+** o **Nginx 1.18+**
- ✅ **PHP 7.4+** (recomendado PHP 8.0+)
- ✅ **MySQL 5.7+** o **MariaDB 10.3+**
- ✅ **WordPress 5.8+**

### **Extensiones PHP Requeridas**
```bash
php-mysql
php-curl
php-gd
php-mbstring
php-xml
php-zip
php-json
```

### **Configuración Recomendada**
```ini
; php.ini
memory_limit = 256M
upload_max_filesize = 10M
post_max_size = 10M
max_execution_time = 300
```

## 🔧 **INSTALACIÓN PASO A PASO**

### **1. Preparación del Entorno**

#### **1.1 Backup del Sistema Actual**
```bash
# Backup de archivos
tar -czf asg_backup_$(date +%Y%m%d).tar.gz /path/to/wordpress/

# Backup de base de datos
mysqldump -u username -p database_name > asg_backup_$(date +%Y%m%d).sql
```

#### **1.2 Verificar Permisos**
```bash
# Permisos para WordPress
chown -R www-data:www-data /var/www/html/
chmod -R 755 /var/www/html/
chmod -R 775 wp-content/uploads/
```

### **2. Instalación de Base de Datos**

#### **2.1 Ejecutar Schema Principal**
```sql
-- Conectar a MySQL
mysql -u username -p database_name

-- Ejecutar schema
SOURCE /path/to/asg-database-schema.sql;

-- Verificar tablas creadas
SHOW TABLES LIKE 'wpic_%';
```

#### **2.2 Verificar Estructura**
```sql
-- Verificar tabla principal
DESCRIBE wpic_courses;

-- Verificar relaciones
SELECT 
    TABLE_NAME,
    COLUMN_NAME,
    CONSTRAINT_NAME,
    REFERENCED_TABLE_NAME,
    REFERENCED_COLUMN_NAME
FROM INFORMATION_SCHEMA.KEY_COLUMN_USAGE
WHERE REFERENCED_TABLE_SCHEMA = 'your_database_name'
AND TABLE_NAME LIKE 'wpic_%';
```

### **3. Instalación de Archivos**

#### **3.1 Subir Archivos del Sistema**
```bash
# Crear estructura de directorios
mkdir -p /var/www/html/asg-admin/
mkdir -p /var/www/html/asg-admin/assets/css/
mkdir -p /var/www/html/asg-admin/assets/js/
mkdir -p /var/www/html/asg-admin/api/
mkdir -p /var/www/html/asg-admin/config/

# Copiar archivos
cp dashboard-v2.html /var/www/html/asg-admin/
cp all-courses-v2.html /var/www/html/asg-admin/
cp new-course-v2.html /var/www/html/asg-admin/
cp edit-course-v2.html /var/www/html/asg-admin/
cp -r assets/ /var/www/html/asg-admin/
cp -r api/ /var/www/html/asg-admin/
cp -r config/ /var/www/html/asg-admin/
```

#### **3.2 Configurar URLs**
```javascript
// Editar config/asg-config.js
window.ASG_CONFIG = {
    urls: {
        baseUrl: 'https://tu-dominio.com',
        apiBaseUrl: 'https://tu-dominio.com/wp-json/asg/v2',
        dashboard: 'https://tu-dominio.com/asg-admin/dashboard-v2.html',
        // ... resto de URLs
    }
};
```

### **4. Configuración de WordPress**

#### **4.1 Activar API REST**
```php
// En functions.php o usar WP Code Snippets
require_once get_template_directory() . '/asg-admin/api/asg-api-v2.php';
require_once get_template_directory() . '/asg-admin/api/asg-api-utilities.php';
```

#### **4.2 Configurar Permalinks**
```
Ir a: WordPress Admin → Configuración → Enlaces permanentes
Seleccionar: "Nombre de la entrada" o estructura personalizada
Guardar cambios
```

#### **4.3 Configurar Permisos de Usuario**
```php
// Agregar capacidades personalizadas
function asg_add_course_capabilities() {
    $role = get_role('administrator');
    $role->add_cap('manage_courses');
    $role->add_cap('edit_courses');
    $role->add_cap('delete_courses');
    $role->add_cap('publish_courses');
}
add_action('init', 'asg_add_course_capabilities');
```

### **5. Configuración de Servidor Web**

#### **5.1 Apache (.htaccess)**
```apache
# En el directorio raíz de WordPress
RewriteEngine On

# Redirecciones para panel admin
RewriteRule ^admin-dashboard/?$ /asg-admin/dashboard-v2.html [L]
RewriteRule ^all-courses/?$ /asg-admin/all-courses-v2.html [L]
RewriteRule ^new-course/?$ /asg-admin/new-course-v2.html [L]
RewriteRule ^edit-course/?$ /asg-admin/edit-course-v2.html [L]

# Seguridad para archivos de configuración
<Files "*.sql">
    Order allow,deny
    Deny from all
</Files>

<Files "*.md">
    Order allow,deny
    Deny from all
</Files>
```

#### **5.2 Nginx**
```nginx
server {
    listen 80;
    server_name tu-dominio.com;
    root /var/www/html;
    index index.php index.html;

    # Redirecciones para panel admin
    location /admin-dashboard/ {
        try_files $uri /asg-admin/dashboard-v2.html;
    }
    
    location /all-courses/ {
        try_files $uri /asg-admin/all-courses-v2.html;
    }
    
    location /new-course/ {
        try_files $uri /asg-admin/new-course-v2.html;
    }
    
    location /edit-course/ {
        try_files $uri /asg-admin/edit-course-v2.html;
    }

    # Bloquear archivos sensibles
    location ~* \.(sql|md)$ {
        deny all;
        return 404;
    }

    # Configuración PHP
    location ~ \.php$ {
        fastcgi_pass unix:/var/run/php/php8.0-fpm.sock;
        fastcgi_index index.php;
        include fastcgi_params;
        fastcgi_param SCRIPT_FILENAME $document_root$fastcgi_script_name;
    }
}
```

## 🧪 **TESTING POST-INSTALACIÓN**

### **1. Verificar Base de Datos**
```sql
-- Verificar que las tablas existen
SELECT COUNT(*) as table_count 
FROM information_schema.tables 
WHERE table_schema = 'your_database' 
AND table_name LIKE 'wpic_%';

-- Debería retornar 6 tablas
```

### **2. Verificar API**
```bash
# Test endpoint público
curl -X GET "https://tu-dominio.com/wp-json/asg/v2/public/courses"

# Test endpoint admin
curl -X GET "https://tu-dominio.com/wp-json/asg/v2/admin/dashboard/stats"
```

### **3. Verificar Frontend**
```bash
# Verificar que las páginas cargan
curl -I "https://tu-dominio.com/admin-dashboard/"
curl -I "https://tu-dominio.com/all-courses/"
curl -I "https://tu-dominio.com/new-course/"
curl -I "https://tu-dominio.com/edit-course/"
```

### **4. Test de Funcionalidades**
1. ✅ **Dashboard**: Verificar carga de métricas
2. ✅ **Gestión**: Probar filtros y búsqueda
3. ✅ **Creación**: Completar formulario
4. ✅ **Edición**: Modificar curso existente
5. ✅ **API**: Verificar respuestas JSON

## 🔒 **CONFIGURACIÓN DE SEGURIDAD**

### **1. SSL/HTTPS**
```bash
# Instalar certificado SSL (Let's Encrypt)
sudo apt install certbot python3-certbot-apache
sudo certbot --apache -d tu-dominio.com
```

### **2. Firewall**
```bash
# UFW (Ubuntu)
sudo ufw allow 22/tcp
sudo ufw allow 80/tcp
sudo ufw allow 443/tcp
sudo ufw enable
```

### **3. WordPress Security**
```php
// En wp-config.php
define('DISALLOW_FILE_EDIT', true);
define('WP_DEBUG', false);
define('WP_DEBUG_LOG', false);

// Claves de seguridad
// Generar en: https://api.wordpress.org/secret-key/1.1/salt/
```

### **4. Backup Automático**
```bash
# Crear script de backup
#!/bin/bash
DATE=$(date +%Y%m%d_%H%M%S)
BACKUP_DIR="/backups/asg"
DB_NAME="your_database"
DB_USER="your_user"
DB_PASS="your_password"

# Crear directorio
mkdir -p $BACKUP_DIR

# Backup de archivos
tar -czf $BACKUP_DIR/files_$DATE.tar.gz /var/www/html/

# Backup de base de datos
mysqldump -u$DB_USER -p$DB_PASS $DB_NAME > $BACKUP_DIR/db_$DATE.sql

# Limpiar backups antiguos (mantener 7 días)
find $BACKUP_DIR -name "*.tar.gz" -mtime +7 -delete
find $BACKUP_DIR -name "*.sql" -mtime +7 -delete
```

## 📊 **MONITOREO Y MANTENIMIENTO**

### **1. Logs a Monitorear**
```bash
# Logs de Apache
tail -f /var/log/apache2/access.log
tail -f /var/log/apache2/error.log

# Logs de PHP
tail -f /var/log/php8.0-fpm.log

# Logs de MySQL
tail -f /var/log/mysql/error.log
```

### **2. Métricas Importantes**
- ✅ **Tiempo de respuesta** de API < 2 segundos
- ✅ **Uso de memoria** PHP < 80%
- ✅ **Conexiones** MySQL < 100 concurrentes
- ✅ **Espacio en disco** > 20% libre

### **3. Mantenimiento Regular**
```bash
# Optimizar base de datos (semanal)
mysqlcheck -u username -p --optimize database_name

# Limpiar cache (si aplica)
wp cache flush

# Actualizar WordPress
wp core update
wp plugin update --all
```

## 🚨 **SOLUCIÓN DE PROBLEMAS**

### **Error: API no responde**
```bash
# Verificar permisos
ls -la /var/www/html/asg-admin/api/

# Verificar logs de PHP
tail -f /var/log/php8.0-fpm.log

# Test manual
php -l /var/www/html/asg-admin/api/asg-api-v2.php
```

### **Error: Base de datos**
```sql
-- Verificar conexión
SHOW PROCESSLIST;

-- Verificar tablas
SHOW TABLE STATUS LIKE 'wpic_%';

-- Reparar si es necesario
REPAIR TABLE wpic_courses;
```

### **Error: Permisos**
```bash
# Restaurar permisos
sudo chown -R www-data:www-data /var/www/html/
sudo chmod -R 755 /var/www/html/
sudo chmod -R 775 /var/www/html/wp-content/uploads/
```

## 📞 **SOPORTE POST-DESPLIEGUE**

### **Contactos de Emergencia**
- 🔧 **Soporte Técnico**: <EMAIL>
- 📱 **Emergencias**: +34 XXX XXX XXX
- 💬 **Chat**: Sistema de tickets interno

### **Documentación Adicional**
- 📚 **Manual de Usuario**: `/docs/user-manual.pdf`
- 🔧 **Guía Técnica**: `/docs/technical-guide.pdf`
- 🐛 **Reporte de Bugs**: Sistema de tickets

---

**✅ Sistema ASG v2.0 desplegado correctamente**  
**📅 Fecha de despliegue**: ___________  
**👤 Responsable**: ___________  
**✍️ Firma**: ___________
