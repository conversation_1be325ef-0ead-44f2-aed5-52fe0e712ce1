/**
 * ========================================
 * EJEMPLO DE INTEGRACIÓN - NEW COURSE
 * ========================================
 * 
 * Descripción: Ejemplo práctico de cómo integrar los endpoints
 * de cursos ASG en el formulario new-course.html
 * 
 * URLs de endpoints utilizadas:
 * - https://abilityseminarsgroup.com/wp-json/asg/v1/courses
 * - https://abilityseminarsgroup.com/wp-json/asg/v1/courses/draft
 * - https://abilityseminarsgroup.com/wp-json/asg/v1/courses/upload-image
 * - https://abilityseminarsgroup.com/wp-json/asg/v1/courses/categories
 * 
 * Autor: ASG Team
 * Fecha: 2025-01-03
 */

document.addEventListener('DOMContentLoaded', function() {
    
    console.log('🚀 Inicializando New Course con endpoints ASG...');
    
    // ===== CONFIGURACIÓN =====
    const API_BASE = 'https://abilityseminarsgroup.com/wp-json/asg/v1';
    
    // ===== ELEMENTOS DEL DOM =====
    const form = document.querySelector('#course-form');
    const saveButton = document.querySelector('#save-course-btn');
    const draftButton = document.querySelector('#save-draft-btn');
    const imageInput = document.querySelector('#course-image');
    const categorySelect = document.querySelector('#course-category');
    
    // ===== INICIALIZACIÓN =====
    init();
    
    /**
     * Inicializar la aplicación
     */
    async function init() {
        try {
            // Cargar categorías al inicio
            await loadCategories();
            
            // Configurar event listeners
            setupEventListeners();
            
            console.log('✅ New Course inicializado correctamente');
            
        } catch (error) {
            console.error('❌ Error inicializando New Course:', error);
        }
    }
    
    /**
     * Cargar categorías desde el endpoint
     */
    async function loadCategories() {
        try {
            console.log('📋 Cargando categorías...');
            
            const response = await fetch(`${API_BASE}/courses/categories`);
            const result = await response.json();
            
            if (result.success && categorySelect) {
                // Limpiar opciones existentes
                categorySelect.innerHTML = '<option value="">Seleccionar categoría...</option>';
                
                // Agregar categorías desde el endpoint
                Object.entries(result.data).forEach(([value, label]) => {
                    const option = document.createElement('option');
                    option.value = value;
                    option.textContent = label;
                    categorySelect.appendChild(option);
                });
                
                console.log('✅ Categorías cargadas:', Object.keys(result.data).length);
            }
            
        } catch (error) {
            console.error('❌ Error cargando categorías:', error);
            
            // Fallback: categorías por defecto
            if (categorySelect) {
                categorySelect.innerHTML = `
                    <option value="">Seleccionar categoría...</option>
                    <option value="business">Negocios</option>
                    <option value="technology">Tecnología</option>
                    <option value="marketing">Marketing</option>
                    <option value="leadership">Liderazgo</option>
                `;
            }
        }
    }
    
    /**
     * Configurar event listeners
     */
    function setupEventListeners() {
        // Botón guardar curso
        if (saveButton) {
            saveButton.addEventListener('click', handleSaveCourse);
        }
        
        // Botón guardar borrador
        if (draftButton) {
            draftButton.addEventListener('click', handleSaveDraft);
        }
        
        // Subida de imagen
        if (imageInput) {
            imageInput.addEventListener('change', handleImageUpload);
        }
        
        // Validación en tiempo real
        setupRealTimeValidation();
    }
    
    /**
     * Manejar guardado de curso
     */
    async function handleSaveCourse(event) {
        event.preventDefault();
        
        try {
            // Mostrar loading
            showButtonLoading(saveButton, 'Guardando curso...');
            
            // Recopilar datos del formulario
            const courseData = collectFormData();
            courseData.status = 'published';
            
            console.log('📝 Enviando datos del curso:', courseData);
            
            // Enviar al endpoint
            const response = await fetch(`${API_BASE}/courses`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify(courseData)
            });
            
            const result = await response.json();
            
            if (result.success) {
                console.log('✅ Curso creado exitosamente:', result.data);
                showSuccessMessage('Curso creado exitosamente');
                
                // Redirigir después de 2 segundos
                setTimeout(() => {
                    window.location.href = 'https://abilityseminarsgroup.com/all-courses/';
                }, 2000);
                
            } else {
                throw new Error(result.error?.message || 'Error creando curso');
            }
            
        } catch (error) {
            console.error('❌ Error guardando curso:', error);
            showErrorMessage('Error al guardar el curso: ' + error.message);
            
        } finally {
            hideButtonLoading(saveButton, 'Guardar Curso');
        }
    }
    
    /**
     * Manejar guardado de borrador
     */
    async function handleSaveDraft(event) {
        event.preventDefault();
        
        try {
            // Mostrar loading
            showButtonLoading(draftButton, 'Guardando borrador...');
            
            // Recopilar datos del formulario
            const courseData = collectFormData();
            courseData.status = 'draft';
            
            console.log('💾 Enviando borrador:', courseData);
            
            // Enviar al endpoint de borrador
            const response = await fetch(`${API_BASE}/courses/draft`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify(courseData)
            });
            
            const result = await response.json();
            
            if (result.success) {
                console.log('✅ Borrador guardado:', result.data);
                showSuccessMessage('Borrador guardado exitosamente');
                
            } else {
                throw new Error(result.error?.message || 'Error guardando borrador');
            }
            
        } catch (error) {
            console.error('❌ Error guardando borrador:', error);
            showErrorMessage('Error al guardar borrador: ' + error.message);
            
        } finally {
            hideButtonLoading(draftButton, 'Guardar Borrador');
        }
    }
    
    /**
     * Manejar subida de imagen
     */
    async function handleImageUpload(event) {
        const file = event.target.files[0];
        
        if (!file) return;
        
        try {
            console.log('📸 Subiendo imagen:', file.name);
            
            // Crear FormData
            const formData = new FormData();
            formData.append('image', file);
            formData.append('type', 'course');
            
            // Enviar al endpoint de subida
            const response = await fetch(`${API_BASE}/courses/upload-image`, {
                method: 'POST',
                body: formData
            });
            
            const result = await response.json();
            
            if (result.success) {
                console.log('✅ Imagen subida:', result.data);
                showSuccessMessage('Imagen subida exitosamente');
                
                // Mostrar preview de la imagen
                showImagePreview(result.data.urls.medium);
                
                // Guardar URL para usar al crear el curso
                window.courseImageUrl = result.data.urls.original;
                
            } else {
                throw new Error(result.error?.message || 'Error subiendo imagen');
            }
            
        } catch (error) {
            console.error('❌ Error subiendo imagen:', error);
            showErrorMessage('Error al subir imagen: ' + error.message);
        }
    }
    
    /**
     * Recopilar datos del formulario
     */
    function collectFormData() {
        const formData = new FormData(form);
        
        return {
            title: formData.get('title') || '',
            description: formData.get('description') || '',
            price: parseFloat(formData.get('price')) || 0,
            category: formData.get('category') || 'business',
            level: formData.get('level') || 'beginner',
            duration: parseFloat(formData.get('duration')) || 0,
            language: formData.get('language') || 'es',
            featured_image_url: window.courseImageUrl || '',
            modules: collectModules(),
            learn_objectives: collectObjectives(),
            benefits: collectBenefits()
        };
    }
    
    /**
     * Recopilar módulos (ejemplo básico)
     */
    function collectModules() {
        const modules = [];
        const moduleElements = document.querySelectorAll('.module-item');
        
        moduleElements.forEach(element => {
            const title = element.querySelector('[name*="module_title"]')?.value;
            const description = element.querySelector('[name*="module_description"]')?.value;
            
            if (title) {
                modules.push({
                    title: title,
                    description: description || '',
                    duration: 0
                });
            }
        });
        
        return modules;
    }
    
    /**
     * Recopilar objetivos de aprendizaje
     */
    function collectObjectives() {
        const objectives = [];
        const objectiveInputs = document.querySelectorAll('[name*="objective"]');
        
        objectiveInputs.forEach(input => {
            if (input.value.trim()) {
                objectives.push(input.value.trim());
            }
        });
        
        return objectives;
    }
    
    /**
     * Recopilar beneficios
     */
    function collectBenefits() {
        const benefits = [];
        const benefitInputs = document.querySelectorAll('[name*="benefit"]');
        
        benefitInputs.forEach(input => {
            if (input.value.trim()) {
                benefits.push(input.value.trim());
            }
        });
        
        return benefits;
    }
    
    /**
     * Configurar validación en tiempo real
     */
    function setupRealTimeValidation() {
        const requiredFields = ['title', 'description', 'price'];
        
        requiredFields.forEach(fieldName => {
            const field = document.querySelector(`[name="${fieldName}"]`);
            if (field) {
                field.addEventListener('blur', () => validateField(fieldName, field.value));
            }
        });
    }
    
    /**
     * Validar campo individual
     */
    async function validateField(fieldName, value) {
        try {
            const tempData = { [fieldName]: value };
            
            const response = await fetch(`${API_BASE}/courses/validate`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify(tempData)
            });
            
            const result = await response.json();
            
            if (!result.success && result.error?.details) {
                showFieldError(fieldName, result.error.details[fieldName]);
            } else {
                clearFieldError(fieldName);
            }
            
        } catch (error) {
            console.error('❌ Error validando campo:', error);
        }
    }
    
    // ===== FUNCIONES DE UI =====
    
    function showButtonLoading(button, text) {
        button.disabled = true;
        button.innerHTML = `<i class="bi bi-hourglass-split me-2"></i>${text}`;
    }
    
    function hideButtonLoading(button, originalText) {
        button.disabled = false;
        button.innerHTML = originalText;
    }
    
    function showSuccessMessage(message) {
        console.log('✅', message);
        // Implementar tu sistema de notificaciones aquí
        alert(message); // Temporal
    }
    
    function showErrorMessage(message) {
        console.error('❌', message);
        // Implementar tu sistema de notificaciones aquí
        alert('Error: ' + message); // Temporal
    }
    
    function showImagePreview(imageUrl) {
        const preview = document.querySelector('#image-preview');
        if (preview) {
            preview.src = imageUrl;
            preview.style.display = 'block';
        }
    }
    
    function showFieldError(fieldName, errorMessage) {
        const errorElement = document.querySelector(`#${fieldName}-error`);
        const inputElement = document.querySelector(`[name="${fieldName}"]`);
        
        if (errorElement) {
            errorElement.textContent = errorMessage;
            errorElement.style.display = 'block';
        }
        
        if (inputElement) {
            inputElement.classList.add('is-invalid');
        }
    }
    
    function clearFieldError(fieldName) {
        const errorElement = document.querySelector(`#${fieldName}-error`);
        const inputElement = document.querySelector(`[name="${fieldName}"]`);
        
        if (errorElement) {
            errorElement.style.display = 'none';
        }
        
        if (inputElement) {
            inputElement.classList.remove('is-invalid');
        }
    }
    
});
