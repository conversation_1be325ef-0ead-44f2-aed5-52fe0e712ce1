<!DOCTYPE html>
<html lang="es">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Editar Curso - AbilitySeminarsGroup</title>
    
    <!-- Preload critical resources -->
    <link rel="preload" href="./assets/css/asg-design-system.css" as="style">
    <link rel="preload" href="./assets/js/asg-components.js" as="script">
    
    <!-- Styles -->
    <link href="./assets/css/asg-design-system.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.11.3/font/bootstrap-icons.css" rel="stylesheet">
    
    <!-- Meta tags -->
    <meta name="description" content="Editar curso - AbilitySeminarsGroup">
    <meta name="robots" content="noindex, nofollow">
    
    <!-- Favicon -->
    <link rel="icon" type="image/x-icon" href="https://abilityseminarsgroup.com/favicon.ico">
</head>
<body>
    <!-- Sidebar -->
    <aside class="asg-sidebar">
        <div class="asg-sidebar-header">
            <img src="https://abilityseminarsgroup.com/wp-content/uploads/2023/09/Asset-6-4-768x355.png" 
                 alt="AbilitySeminarsGroup" 
                 class="asg-sidebar-logo"
                 style="width: 130px; height: auto;">
        </div>
        
        <nav class="asg-sidebar-nav">
            <a href="https://abilityseminarsgroup.com/admin-dashboard/" class="asg-sidebar-item">
                <i class="bi bi-house asg-sidebar-icon"></i>
                <span>Dashboard</span>
            </a>
            
            <a href="https://abilityseminarsgroup.com/all-courses/" class="asg-sidebar-item">
                <i class="bi bi-collection asg-sidebar-icon"></i>
                <span>Todos los Cursos</span>
            </a>
            
            <a href="https://abilityseminarsgroup.com/new-course/" class="asg-sidebar-item">
                <i class="bi bi-plus-circle asg-sidebar-icon"></i>
                <span>Nuevo Curso</span>
            </a>
            
            <div class="asg-sidebar-divider" style="height: 1px; background: var(--asg-gray-200); margin: var(--asg-space-4) var(--asg-space-6);"></div>
            
            <a href="#" class="asg-sidebar-item">
                <i class="bi bi-people asg-sidebar-icon"></i>
                <span>Estudiantes</span>
            </a>
            
            <a href="#" class="asg-sidebar-item">
                <i class="bi bi-bar-chart asg-sidebar-icon"></i>
                <span>Analíticas</span>
            </a>
            
            <a href="#" class="asg-sidebar-item">
                <i class="bi bi-gear asg-sidebar-icon"></i>
                <span>Configuración</span>
            </a>
        </nav>
    </aside>
    
    <!-- Sidebar Overlay (Mobile) -->
    <div class="asg-sidebar-overlay"></div>
    
    <!-- Main Content -->
    <main class="asg-main">
        <!-- Header -->
        <header class="asg-header">
            <div style="display: flex; align-items: center; justify-content: space-between;">
                <div style="display: flex; align-items: center; gap: var(--asg-space-4);">
                    <button class="asg-sidebar-toggle asg-btn asg-btn-secondary" style="display: none;">
                        <i class="bi bi-list"></i>
                    </button>
                    <div>
                        <h1 style="margin: 0; font-size: var(--asg-text-2xl); font-weight: var(--asg-font-semibold); color: var(--asg-gray-900);">
                            <span id="courseTitle">Editar Curso</span>
                        </h1>
                        <div style="display: flex; align-items: center; gap: var(--asg-space-2); margin-top: var(--asg-space-1);">
                            <span id="courseStatus" class="asg-badge asg-badge-gray">Cargando...</span>
                            <span style="font-size: var(--asg-text-sm); color: var(--asg-gray-500);" id="lastSaved">
                                Última modificación: --
                            </span>
                        </div>
                    </div>
                </div>
                
                <div style="display: flex; align-items: center; gap: var(--asg-space-3);">
                    <button class="asg-btn asg-btn-secondary" id="previewBtn">
                        <i class="bi bi-eye"></i>
                        <span class="btn-text">Vista Previa</span>
                    </button>
                    
                    <button class="asg-btn asg-btn-secondary" id="saveBtn">
                        <i class="bi bi-save"></i>
                        <span class="btn-text">Guardar</span>
                    </button>
                    
                    <button class="asg-btn asg-btn-primary" id="publishBtn">
                        <i class="bi bi-check-circle"></i>
                        <span class="btn-text">Publicar</span>
                    </button>
                </div>
            </div>
        </header>
        
        <!-- Content -->
        <div class="asg-content">
            <!-- Loading State -->
            <div id="loadingState" style="text-align: center; padding: var(--asg-space-16);">
                <div style="
                    width: 3rem; 
                    height: 3rem; 
                    border: 3px solid var(--asg-gray-200); 
                    border-top: 3px solid var(--asg-primary-600); 
                    border-radius: 50%; 
                    animation: spin 1s linear infinite; 
                    margin: 0 auto var(--asg-space-4) auto;
                "></div>
                <p style="margin: 0; color: var(--asg-gray-600);">Cargando curso...</p>
            </div>
            
            <!-- Course Editor -->
            <div id="courseEditor" style="display: none;">
                <!-- Tabs Navigation -->
                <div class="asg-card" style="margin-bottom: var(--asg-space-6);">
                    <div class="asg-card-body" style="padding: var(--asg-space-4) var(--asg-space-6);">
                        <div class="editor-tabs" style="display: flex; gap: var(--asg-space-1); border-bottom: 1px solid var(--asg-gray-200); margin-bottom: var(--asg-space-4);">
                            <button class="tab-btn active" data-tab="basic" style="
                                padding: var(--asg-space-3) var(--asg-space-4);
                                border: none;
                                background: none;
                                color: var(--asg-primary-600);
                                border-bottom: 2px solid var(--asg-primary-600);
                                font-weight: var(--asg-font-medium);
                                cursor: pointer;
                                transition: all var(--asg-transition);
                            ">
                                <i class="bi bi-info-circle"></i> Información Básica
                            </button>
                            
                            <button class="tab-btn" data-tab="content" style="
                                padding: var(--asg-space-3) var(--asg-space-4);
                                border: none;
                                background: none;
                                color: var(--asg-gray-600);
                                border-bottom: 2px solid transparent;
                                font-weight: var(--asg-font-medium);
                                cursor: pointer;
                                transition: all var(--asg-transition);
                            ">
                                <i class="bi bi-collection"></i> Módulos y Lecciones
                            </button>
                            
                            <button class="tab-btn" data-tab="objectives" style="
                                padding: var(--asg-space-3) var(--asg-space-4);
                                border: none;
                                background: none;
                                color: var(--asg-gray-600);
                                border-bottom: 2px solid transparent;
                                font-weight: var(--asg-font-medium);
                                cursor: pointer;
                                transition: all var(--asg-transition);
                            ">
                                <i class="bi bi-target"></i> Objetivos y Beneficios
                            </button>
                            
                            <button class="tab-btn" data-tab="settings" style="
                                padding: var(--asg-space-3) var(--asg-space-4);
                                border: none;
                                background: none;
                                color: var(--asg-gray-600);
                                border-bottom: 2px solid transparent;
                                font-weight: var(--asg-font-medium);
                                cursor: pointer;
                                transition: all var(--asg-transition);
                            ">
                                <i class="bi bi-gear"></i> Configuración
                            </button>
                        </div>
                    </div>
                </div>
                
                <!-- Tab Content -->
                <div class="tab-content">
                    <!-- Basic Information Tab -->
                    <div class="tab-panel active" data-tab="basic">
                        <div class="asg-card">
                            <div class="asg-card-header">
                                <h2 style="margin: 0; font-size: var(--asg-text-lg); font-weight: var(--asg-font-semibold);">
                                    Información Básica
                                </h2>
                            </div>
                            <div class="asg-card-body">
                                <div style="display: grid; grid-template-columns: 1fr 300px; gap: var(--asg-space-6);">
                                    <div>
                                        <div class="asg-form-group">
                                            <label for="editCourseName" class="asg-label">Título del Curso</label>
                                            <input type="text" id="editCourseName" class="asg-input" placeholder="Título del curso">
                                        </div>
                                        
                                        <div class="asg-form-group">
                                            <label for="editCourseDescription" class="asg-label">Descripción</label>
                                            <textarea id="editCourseDescription" class="asg-input asg-textarea" rows="4" placeholder="Descripción del curso"></textarea>
                                        </div>
                                        
                                        <div style="display: grid; grid-template-columns: 1fr 1fr 1fr; gap: var(--asg-space-4);">
                                            <div class="asg-form-group">
                                                <label for="editCourseCategory" class="asg-label">Categoría</label>
                                                <select id="editCourseCategory" class="asg-input asg-select">
                                                    <option value="">Seleccionar categoría</option>
                                                    <option value="finanzas">Finanzas</option>
                                                    <option value="marketing">Marketing</option>
                                                    <option value="desarrollo-personal">Desarrollo Personal</option>
                                                    <option value="tecnologia">Tecnología</option>
                                                    <option value="negocios">Negocios</option>
                                                </select>
                                            </div>
                                            
                                            <div class="asg-form-group">
                                                <label for="editCoursePrice" class="asg-label">Precio (€)</label>
                                                <input type="number" id="editCoursePrice" class="asg-input" placeholder="0.00" step="0.01" min="0">
                                            </div>
                                            
                                            <div class="asg-form-group">
                                                <label for="editCourseLanguage" class="asg-label">Idioma</label>
                                                <select id="editCourseLanguage" class="asg-input asg-select">
                                                    <option value="es">Español</option>
                                                    <option value="en">Inglés</option>
                                                    <option value="fr">Francés</option>
                                                    <option value="pt">Portugués</option>
                                                </select>
                                            </div>
                                        </div>
                                    </div>
                                    
                                    <!-- Course Image -->
                                    <div>
                                        <label class="asg-label">Imagen del Curso</label>
                                        <div class="course-image-container" style="
                                            width: 100%;
                                            height: 200px;
                                            border: 2px dashed var(--asg-gray-300);
                                            border-radius: var(--asg-radius);
                                            display: flex;
                                            align-items: center;
                                            justify-content: center;
                                            cursor: pointer;
                                            transition: all var(--asg-transition);
                                            background-size: cover;
                                            background-position: center;
                                        " onclick="document.getElementById('editCourseImageInput').click()">
                                            <div id="imageUploadPlaceholder" style="text-align: center; color: var(--asg-gray-500);">
                                                <i class="bi bi-cloud-upload" style="font-size: 2rem; margin-bottom: var(--asg-space-2);"></i>
                                                <p style="margin: 0; font-size: var(--asg-text-sm);">Subir imagen</p>
                                            </div>
                                        </div>
                                        <input type="file" id="editCourseImageInput" accept="image/*" style="display: none;">
                                        
                                        <div style="margin-top: var(--asg-space-3); display: flex; gap: var(--asg-space-2);">
                                            <button type="button" class="asg-btn asg-btn-sm asg-btn-secondary" onclick="document.getElementById('editCourseImageInput').click()">
                                                <i class="bi bi-upload"></i> Cambiar
                                            </button>
                                            <button type="button" class="asg-btn asg-btn-sm asg-btn-secondary" id="removeImageBtn" style="display: none;">
                                                <i class="bi bi-trash"></i> Eliminar
                                            </button>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <!-- Content Tab -->
                    <div class="tab-panel" data-tab="content" style="display: none;">
                        <div class="asg-card">
                            <div class="asg-card-header">
                                <div style="display: flex; align-items: center; justify-content: space-between;">
                                    <h2 style="margin: 0; font-size: var(--asg-text-lg); font-weight: var(--asg-font-semibold);">
                                        Módulos y Lecciones
                                    </h2>
                                    <button class="asg-btn asg-btn-primary" id="addModuleBtn">
                                        <i class="bi bi-plus-circle"></i> Agregar Módulo
                                    </button>
                                </div>
                            </div>
                            <div class="asg-card-body">
                                <div id="modulesContainer">
                                    <div style="text-align: center; padding: var(--asg-space-8); color: var(--asg-gray-500);">
                                        <i class="bi bi-collection" style="font-size: 3rem; margin-bottom: var(--asg-space-4);"></i>
                                        <p style="margin: 0; font-size: var(--asg-text-lg); font-weight: var(--asg-font-medium);">
                                            No hay módulos creados
                                        </p>
                                        <p style="margin: var(--asg-space-2) 0 0 0; font-size: var(--asg-text-sm);">
                                            Agrega tu primer módulo para comenzar a estructurar el curso
                                        </p>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <!-- Objectives Tab -->
                    <div class="tab-panel" data-tab="objectives" style="display: none;">
                        <div style="display: grid; grid-template-columns: 1fr 1fr; gap: var(--asg-space-6);">
                            <!-- Learning Objectives -->
                            <div class="asg-card">
                                <div class="asg-card-header">
                                    <div style="display: flex; align-items: center; justify-content: space-between;">
                                        <h3 style="margin: 0; font-size: var(--asg-text-base); font-weight: var(--asg-font-semibold);">
                                            Objetivos de Aprendizaje
                                        </h3>
                                        <button class="asg-btn asg-btn-sm asg-btn-primary" id="addObjectiveBtn">
                                            <i class="bi bi-plus"></i> Agregar
                                        </button>
                                    </div>
                                </div>
                                <div class="asg-card-body">
                                    <div id="objectivesContainer">
                                        <p style="text-align: center; color: var(--asg-gray-500); padding: var(--asg-space-6);">
                                            No hay objetivos definidos
                                        </p>
                                    </div>
                                </div>
                            </div>
                            
                            <!-- Course Benefits -->
                            <div class="asg-card">
                                <div class="asg-card-header">
                                    <div style="display: flex; align-items: center; justify-content: space-between;">
                                        <h3 style="margin: 0; font-size: var(--asg-text-base); font-weight: var(--asg-font-semibold);">
                                            Beneficios del Curso
                                        </h3>
                                        <button class="asg-btn asg-btn-sm asg-btn-primary" id="addBenefitBtn">
                                            <i class="bi bi-plus"></i> Agregar
                                        </button>
                                    </div>
                                </div>
                                <div class="asg-card-body">
                                    <div id="benefitsContainer">
                                        <p style="text-align: center; color: var(--asg-gray-500); padding: var(--asg-space-6);">
                                            No hay beneficios definidos
                                        </p>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <!-- Settings Tab -->
                    <div class="tab-panel" data-tab="settings" style="display: none;">
                        <div class="asg-card">
                            <div class="asg-card-header">
                                <h2 style="margin: 0; font-size: var(--asg-text-lg); font-weight: var(--asg-font-semibold);">
                                    Configuración Avanzada
                                </h2>
                            </div>
                            <div class="asg-card-body">
                                <div style="display: grid; grid-template-columns: 1fr 1fr; gap: var(--asg-space-6);">
                                    <div>
                                        <div class="asg-form-group">
                                            <label class="asg-label">Estado del Curso</label>
                                            <select id="editCourseStatus" class="asg-input asg-select">
                                                <option value="draft">Borrador</option>
                                                <option value="published">Publicado</option>
                                                <option value="archived">Archivado</option>
                                            </select>
                                        </div>
                                        
                                        <div class="asg-form-group">
                                            <label class="asg-label">
                                                <input type="checkbox" id="editCourseFeatured" style="margin-right: var(--asg-space-2);">
                                                Curso destacado
                                            </label>
                                        </div>
                                    </div>
                                    
                                    <div>
                                        <div class="asg-form-group">
                                            <label for="editCourseSeoTitle" class="asg-label">Título SEO</label>
                                            <input type="text" id="editCourseSeoTitle" class="asg-input" placeholder="Título optimizado para SEO">
                                        </div>
                                        
                                        <div class="asg-form-group">
                                            <label for="editCourseSeoDescription" class="asg-label">Descripción SEO</label>
                                            <textarea id="editCourseSeoDescription" class="asg-input asg-textarea" rows="3" placeholder="Descripción meta para SEO"></textarea>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- Error State -->
            <div id="errorState" style="display: none; text-align: center; padding: var(--asg-space-16);">
                <i class="bi bi-exclamation-triangle" style="font-size: 4rem; color: var(--asg-error); margin-bottom: var(--asg-space-4);"></i>
                <h3 style="margin: 0 0 var(--asg-space-2) 0; font-size: var(--asg-text-xl); font-weight: var(--asg-font-semibold);">
                    Error al cargar el curso
                </h3>
                <p style="margin: 0 0 var(--asg-space-6) 0; color: var(--asg-gray-600);">
                    No se pudo cargar la información del curso. Verifica que el ID sea correcto.
                </p>
                <a href="https://abilityseminarsgroup.com/all-courses/" class="asg-btn asg-btn-primary">
                    <i class="bi bi-arrow-left"></i> Volver a Cursos
                </a>
            </div>
        </div>
    </main>
    
    <!-- Scripts -->
    <script src="./assets/js/asg-components.js"></script>
    <script src="./assets/js/edit-course-v2.js"></script>
    
    <!-- Styles -->
    <style>
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
        
        .tab-btn.active {
            color: var(--asg-primary-600) !important;
            border-bottom-color: var(--asg-primary-600) !important;
        }
        
        .tab-btn:hover {
            color: var(--asg-primary-600);
        }
        
        .course-image-container:hover {
            border-color: var(--asg-primary-500);
            background-color: var(--asg-primary-50);
        }
        
        @media (max-width: 1024px) {
            .asg-sidebar-toggle {
                display: flex !important;
            }
            
            .btn-text {
                display: none;
            }
            
            .asg-content > div[style*="grid-template-columns"] {
                grid-template-columns: 1fr !important;
            }
        }
    </style>
</body>
</html>
