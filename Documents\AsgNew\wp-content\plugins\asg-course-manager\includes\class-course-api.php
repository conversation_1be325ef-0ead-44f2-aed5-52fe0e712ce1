<?php
/**
 * API REST para gestión de cursos ASG
 * 
 * Maneja todos los endpoints relacionados con cursos
 */

if (!defined('ABSPATH')) {
    exit;
}

class ASG_Course_API {
    
    private $namespace = 'asg/v1';
    private $course_model;
    private $image_handler;
    
    public function __construct() {
        $this->course_model = new ASG_Course_Model();
        $this->image_handler = new ASG_Image_Handler();
    }
    
    /**
     * Registrar todas las rutas de la API
     */
    public function register_routes() {
        // Crear nuevo curso
        register_rest_route($this->namespace, '/courses', [
            'methods' => 'POST',
            'callback' => [$this, 'create_course'],
            'permission_callback' => [$this, 'check_permissions'],
            'args' => $this->get_course_schema()
        ]);
        
        // Guardar borrador
        register_rest_route($this->namespace, '/courses/draft', [
            'methods' => 'POST',
            'callback' => [$this, 'save_draft'],
            'permission_callback' => [$this, 'check_permissions'],
            'args' => $this->get_course_schema()
        ]);
        
        // Subir imagen
        register_rest_route($this->namespace, '/courses/upload-image', [
            'methods' => 'POST',
            'callback' => [$this, 'upload_image'],
            'permission_callback' => [$this, 'check_permissions']
        ]);
        
        // Obtener categorías
        register_rest_route($this->namespace, '/courses/categories', [
            'methods' => 'GET',
            'callback' => [$this, 'get_categories'],
            'permission_callback' => [$this, 'check_permissions']
        ]);
        
        // Validar datos
        register_rest_route($this->namespace, '/courses/validate', [
            'methods' => 'POST',
            'callback' => [$this, 'validate_course'],
            'permission_callback' => [$this, 'check_permissions']
        ]);
    }
    
    /**
     * Crear nuevo curso
     * 
     * @param WP_REST_Request $request
     * @return WP_REST_Response|WP_Error
     */
    public function create_course($request) {
        try {
            $course_data = $request->get_json_params();
            
            // Log de datos recibidos
            error_log('📝 ASG API - Creando curso: ' . json_encode($course_data));
            
            // Validar datos
            $validation = $this->course_model->validate_course_data($course_data);
            if (is_wp_error($validation)) {
                return $this->format_error_response($validation);
            }
            
            // Crear curso
            $result = $this->course_model->create_course($course_data);
            
            if (is_wp_error($result)) {
                return $this->format_error_response($result);
            }
            
            return $this->format_success_response($result['data'], $result['message']);
            
        } catch (Exception $e) {
            error_log('❌ ASG API - Error en create_course: ' . $e->getMessage());
            
            return $this->format_error_response(new WP_Error(
                'internal_error',
                'Error interno del servidor',
                ['status' => 500]
            ));
        }
    }
    
    /**
     * Guardar curso como borrador
     * 
     * @param WP_REST_Request $request
     * @return WP_REST_Response|WP_Error
     */
    public function save_draft($request) {
        try {
            $course_data = $request->get_json_params();
            
            // Log de datos recibidos
            error_log('💾 ASG API - Guardando borrador: ' . json_encode($course_data));
            
            // Guardar como borrador (validación más flexible)
            $result = $this->course_model->save_draft($course_data);
            
            if (is_wp_error($result)) {
                return $this->format_error_response($result);
            }
            
            return $this->format_success_response($result['data'], 'Borrador guardado exitosamente');
            
        } catch (Exception $e) {
            error_log('❌ ASG API - Error en save_draft: ' . $e->getMessage());
            
            return $this->format_error_response(new WP_Error(
                'internal_error',
                'Error interno del servidor',
                ['status' => 500]
            ));
        }
    }
    
    /**
     * Subir imagen
     * 
     * @param WP_REST_Request $request
     * @return WP_REST_Response|WP_Error
     */
    public function upload_image($request) {
        try {
            // Verificar que se subió un archivo
            if (empty($_FILES['image'])) {
                return $this->format_error_response(new WP_Error(
                    'no_file',
                    'No se proporcionó ningún archivo',
                    ['status' => 400]
                ));
            }
            
            $file = $_FILES['image'];
            $type = $request->get_param('type') ?? 'course';
            
            // Log de subida
            error_log('📸 ASG API - Subiendo imagen: ' . $file['name'] . ' (tipo: ' . $type . ')');
            
            // Subir imagen
            $result = $this->image_handler->upload_image($file, $type);
            
            if (is_wp_error($result)) {
                return $this->format_error_response($result);
            }
            
            return $this->format_success_response($result['data'], $result['message']);
            
        } catch (Exception $e) {
            error_log('❌ ASG API - Error en upload_image: ' . $e->getMessage());
            
            return $this->format_error_response(new WP_Error(
                'internal_error',
                'Error interno del servidor',
                ['status' => 500]
            ));
        }
    }
    
    /**
     * Obtener categorías disponibles
     * 
     * @param WP_REST_Request $request
     * @return WP_REST_Response
     */
    public function get_categories($request) {
        $categories = $this->course_model->get_categories();
        return $this->format_success_response($categories, 'Categorías obtenidas exitosamente');
    }
    
    /**
     * Validar datos del curso
     * 
     * @param WP_REST_Request $request
     * @return WP_REST_Response|WP_Error
     */
    public function validate_course($request) {
        $course_data = $request->get_json_params();
        
        $validation = $this->course_model->validate_course_data($course_data);
        
        if (is_wp_error($validation)) {
            return $this->format_error_response($validation);
        }
        
        return $this->format_success_response([], 'Datos válidos');
    }
    
    /**
     * Verificar permisos
     * 
     * @return bool
     */
    public function check_permissions() {
        return current_user_can('manage_options');
    }
    
    /**
     * Formatear respuesta exitosa
     * 
     * @param mixed $data Datos a retornar
     * @param string $message Mensaje de éxito
     * @return WP_REST_Response
     */
    private function format_success_response($data, $message = '') {
        return new WP_REST_Response([
            'success' => true,
            'data' => $data,
            'message' => $message,
            'meta' => [
                'timestamp' => current_time('c'),
                'version' => ASG_PLUGIN_VERSION
            ]
        ], 200);
    }
    
    /**
     * Formatear respuesta de error
     * 
     * @param WP_Error $error Error de WordPress
     * @return WP_REST_Response
     */
    private function format_error_response($error) {
        $error_data = $error->get_error_data();
        $status_code = isset($error_data['status']) ? $error_data['status'] : 500;
        
        return new WP_REST_Response([
            'success' => false,
            'error' => [
                'code' => $error->get_error_code(),
                'message' => $error->get_error_message(),
                'details' => isset($error_data['details']) ? $error_data['details'] : null
            ],
            'meta' => [
                'timestamp' => current_time('c'),
                'version' => ASG_PLUGIN_VERSION
            ]
        ], $status_code);
    }

    /**
     * Obtener esquema de validación para cursos
     *
     * @return array Esquema de validación
     */
    private function get_course_schema() {
        return [
            'title' => [
                'required' => true,
                'type' => 'string',
                'description' => 'Título del curso',
                'sanitize_callback' => 'sanitize_text_field'
            ],
            'description' => [
                'required' => true,
                'type' => 'string',
                'description' => 'Descripción del curso',
                'sanitize_callback' => 'wp_kses_post'
            ],
            'price' => [
                'required' => true,
                'type' => 'number',
                'description' => 'Precio del curso',
                'minimum' => 0
            ],
            'category' => [
                'required' => false,
                'type' => 'string',
                'description' => 'Categoría del curso',
                'default' => 'business',
                'sanitize_callback' => 'sanitize_text_field'
            ],
            'level' => [
                'required' => false,
                'type' => 'string',
                'description' => 'Nivel del curso',
                'enum' => ['beginner', 'intermediate', 'advanced'],
                'default' => 'beginner'
            ],
            'duration' => [
                'required' => false,
                'type' => 'number',
                'description' => 'Duración del curso en horas',
                'minimum' => 0,
                'default' => 0
            ],
            'language' => [
                'required' => false,
                'type' => 'string',
                'description' => 'Idioma del curso',
                'default' => 'es',
                'sanitize_callback' => 'sanitize_text_field'
            ],
            'modules' => [
                'required' => false,
                'type' => 'array',
                'description' => 'Módulos del curso'
            ],
            'learn_objectives' => [
                'required' => false,
                'type' => 'array',
                'description' => 'Objetivos de aprendizaje'
            ],
            'benefits' => [
                'required' => false,
                'type' => 'array',
                'description' => 'Beneficios del curso'
            ],
            'featured_image' => [
                'required' => false,
                'type' => 'integer',
                'description' => 'ID de la imagen destacada'
            ],
            'status' => [
                'required' => false,
                'type' => 'string',
                'description' => 'Estado del curso',
                'enum' => ['draft', 'published', 'archived'],
                'default' => 'draft'
            ]
        ];
    }
}
