# 🔧 ASG Course Management - Code Snippets

## 📋 **GUÍA DE INSTALACIÓN CON WP CODE SNIPPETS**

Esta versión del sistema ASG está optimizada para funcionar con **WP Code Snippets**, combinando HTML, CSS y JavaScript en archivos únicos para facilitar la instalación y mantenimiento.

### 🎯 **VENTAJAS DE ESTA VERSIÓN**

- ✅ **Instalación simplificada** - Solo copiar y pegar
- ✅ **Menos archivos** - Todo integrado en snippets
- ✅ **Fácil mantenimiento** - Edición directa desde WordPress
- ✅ **Backup automático** - WP Code Snippets incluye versionado
- ✅ **Activación/desactivación** - Control granular por funcionalidad

## 📦 **SNIPPETS INCLUIDOS**

### **1. SNIPPET BASE (OBLIGATORIO)**
**Archivo**: `01-asg-base-config.php`  
**Tipo**: PHP Snippet  
**Ubicación**: Ejecutar en todas partes  
**Descripción**: Configuración base, API REST y estilos globales

**Funcionalidades**:
- ✅ API REST completa con endpoints públicos y administrativos
- ✅ Configuración JavaScript global
- ✅ Estilos CSS base del sistema
- ✅ Utilidades y funciones auxiliares

### **2. DASHBOARD ADMINISTRATIVO**
**Archivo**: `02-asg-dashboard.php`  
**Tipo**: PHP Snippet  
**Ubicación**: Solo en admin  
**Descripción**: Dashboard principal con métricas y estadísticas

**Funcionalidades**:
- ✅ Métricas en tiempo real (cursos, ingresos, estadísticas)
- ✅ Cursos recientes con acciones rápidas
- ✅ Animaciones de números y efectos visuales
- ✅ Auto-refresh y datos simulados para desarrollo

**Shortcode**: `[asg_dashboard]`  
**Página admin**: `/wp-admin/admin.php?page=asg-dashboard`

### **3. GESTIÓN DE CURSOS**
**Archivo**: `03-asg-courses-manager.php`  
**Tipo**: PHP Snippet  
**Ubicación**: Solo en admin  
**Descripción**: Gestión completa de cursos con filtros y búsqueda

**Funcionalidades**:
- ✅ Vista grid/list intercambiable
- ✅ Búsqueda en tiempo real con debounce
- ✅ Filtros por estado y categoría
- ✅ Paginación inteligente
- ✅ Acciones: editar, duplicar, eliminar

**Shortcode**: `[asg_courses_manager]`  
**Página admin**: `/wp-admin/admin.php?page=asg-all-courses`

### **4. CREACIÓN DE CURSOS** (Próximamente)
**Archivo**: `04-asg-course-creator.php`  
**Funcionalidades**: Formulario multi-paso para crear cursos

### **5. EDITOR AVANZADO** (Próximamente)
**Archivo**: `05-asg-course-editor.php`  
**Funcionalidades**: Editor completo con gestión de módulos y lecciones

## 🚀 **INSTALACIÓN PASO A PASO**

### **Paso 1: Instalar WP Code Snippets**
```bash
# Desde WordPress Admin
Plugins → Añadir nuevo → Buscar "Code Snippets" → Instalar y Activar
```

### **Paso 2: Crear Base de Datos**
```sql
-- Ejecutar en phpMyAdmin o similar
SOURCE asg-database-schema.sql;
```

### **Paso 3: Instalar Snippet Base**
1. Ir a **Snippets → Añadir nuevo**
2. **Título**: `ASG Base Configuration`
3. **Código**: Copiar contenido de `01-asg-base-config.php`
4. **Tipo**: PHP Snippet
5. **Ubicación**: Ejecutar en todas partes
6. **Guardar y Activar**

### **Paso 4: Instalar Dashboard**
1. Ir a **Snippets → Añadir nuevo**
2. **Título**: `ASG Dashboard`
3. **Código**: Copiar contenido de `02-asg-dashboard.php`
4. **Tipo**: PHP Snippet
5. **Ubicación**: Solo en admin
6. **Guardar y Activar**

### **Paso 5: Instalar Gestión de Cursos**
1. Ir a **Snippets → Añadir nuevo**
2. **Título**: `ASG Courses Manager`
3. **Código**: Copiar contenido de `03-asg-courses-manager.php`
4. **Tipo**: PHP Snippet
5. **Ubicación**: Solo en admin
6. **Guardar y Activar**

### **Paso 6: Verificar Instalación**
1. Ir a **ASG Cursos** en el menú de WordPress Admin
2. Verificar que el dashboard carga correctamente
3. Probar la gestión de cursos

## 🔧 **CONFIGURACIÓN**

### **URLs del Sistema**
El sistema creará automáticamente estas páginas en WordPress Admin:
- **Dashboard**: `/wp-admin/admin.php?page=asg-dashboard`
- **Gestión**: `/wp-admin/admin.php?page=asg-all-courses`

### **Permisos de Usuario**
Por defecto, solo usuarios con capacidad `manage_options` pueden acceder. Para permitir a editores:

```php
// Agregar en functions.php o como snippet adicional
add_action('init', function() {
    $role = get_role('editor');
    if ($role) {
        $role->add_cap('manage_options'); // Solo para ASG
    }
});
```

### **Personalización de Logo**
Editar en el snippet base la línea:
```javascript
logoUrl: 'https://tu-dominio.com/ruta-a-tu-logo.png'
```

## 🧪 **TESTING Y DESARROLLO**

### **Datos de Prueba**
Los snippets incluyen datos simulados para desarrollo:
- 3 cursos de ejemplo con diferentes estados
- Estadísticas realistas para el dashboard
- Imágenes de Unsplash para testing

### **Modo Debug**
Para activar logs detallados, agregar en `wp-config.php`:
```php
define('WP_DEBUG', true);
define('WP_DEBUG_LOG', true);
```

### **API Testing**
Probar endpoints directamente:
```bash
# Estadísticas del dashboard
curl "https://tu-dominio.com/wp-json/asg/v2/admin/dashboard/stats"

# Lista de cursos
curl "https://tu-dominio.com/wp-json/asg/v2/admin/courses"
```

## 🔒 **SEGURIDAD**

### **Validaciones Incluidas**
- ✅ Verificación de permisos en todos los endpoints
- ✅ Sanitización de inputs con funciones WordPress
- ✅ Nonces para peticiones AJAX
- ✅ Escape de outputs para prevenir XSS

### **Recomendaciones**
1. **Backup regular** de snippets desde Code Snippets
2. **Testing en staging** antes de producción
3. **Monitoreo de logs** para detectar errores
4. **Actualización periódica** de WordPress y plugins

## 📊 **MONITOREO**

### **Logs Importantes**
```bash
# Logs de WordPress
tail -f /wp-content/debug.log

# Logs de servidor
tail -f /var/log/apache2/error.log
```

### **Métricas a Vigilar**
- ✅ Tiempo de respuesta de API < 2 segundos
- ✅ Uso de memoria PHP < 80%
- ✅ Errores JavaScript en consola
- ✅ Carga correcta de estilos CSS

## 🚨 **SOLUCIÓN DE PROBLEMAS**

### **Error: "ASG_CONFIG is not defined"**
**Causa**: El snippet base no está activado o hay error de sintaxis  
**Solución**: Verificar que el snippet 1 esté activo y sin errores

### **Error: API endpoints no responden**
**Causa**: Permalinks no configurados o snippet base inactivo  
**Solución**: 
1. Ir a Configuración → Enlaces permanentes → Guardar
2. Verificar que el snippet base esté activo

### **Error: Estilos no se cargan**
**Causa**: Conflicto con tema o plugins  
**Solución**: Agregar `!important` a estilos críticos en el snippet base

### **Error: JavaScript no funciona**
**Causa**: Conflicto con otros scripts o errores de sintaxis  
**Solución**: Revisar consola del navegador y logs de WordPress

## 📞 **SOPORTE**

### **Recursos de Ayuda**
- 📚 **Documentación**: Este archivo README
- 🐛 **Debug**: Activar WP_DEBUG para logs detallados
- 💬 **Comunidad**: Foro de WordPress para Code Snippets
- 📧 **Contacto**: <EMAIL>

### **Información del Sistema**
- **Versión**: 2.0.0 - Code Snippets Edition
- **Compatibilidad**: WordPress 5.8+, PHP 7.4+
- **Dependencias**: WP Code Snippets plugin
- **Base de datos**: MySQL 5.7+ / MariaDB 10.3+

## 🔄 **ACTUALIZACIONES**

### **Proceso de Actualización**
1. **Backup** de snippets actuales
2. **Desactivar** snippets existentes
3. **Copiar** nuevo código
4. **Activar** snippets actualizados
5. **Verificar** funcionamiento

### **Versionado**
Cada snippet incluye comentario con versión:
```php
* Versión: 2.0.0 - CODE SNIPPETS
```

---

**✅ Sistema ASG v2.0 - Code Snippets Edition**  
**📅 Última actualización**: 2025-01-04  
**👤 Desarrollado por**: ASG Team
