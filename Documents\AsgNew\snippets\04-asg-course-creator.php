<?php
/**
 * ========================================
 * ASG CREADOR DE CURSOS - SNIPPET 4
 * ========================================
 * 
 * Descripción: Formulario multi-paso para crear cursos completos
 * Uso: Copiar y pegar en WP Code Snippets
 * Tipo: PHP Snippet
 * Ubicación: Solo en admin
 * 
 * Shortcode: [asg_course_creator]
 * 
 * Autor: ASG Team
 * Fecha: 2025-01-04
 * Versión: 2.0.0 - CODE SNIPPETS
 */

// Prevenir ejecución directa
if (!defined('ABSPATH')) {
    exit;
}

/**
 * ========================================
 * SHORTCODE DEL CREADOR DE CURSOS
 * ========================================
 */
add_shortcode('asg_course_creator', 'asg_render_course_creator');

function asg_render_course_creator($atts) {
    // Solo para usuarios con permisos
    if (!current_user_can('manage_options') && !current_user_can('edit_posts')) {
        return '<p>No tienes permisos para acceder a esta sección.</p>';
    }
    
    ob_start();
    ?>
    
    <!-- CSS del Creador de Cursos -->
    <style>
    .asg-course-creator {
        font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
        max-width: 1200px;
        margin: 0 auto;
        padding: 2rem 1rem;
    }
    
    .asg-creator-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 2rem;
        padding-bottom: 1rem;
        border-bottom: 1px solid var(--asg-gray-200);
    }
    
    .asg-creator-title {
        margin: 0;
        font-size: 2rem;
        font-weight: 600;
        color: var(--asg-gray-900);
    }
    
    .asg-creator-actions {
        display: flex;
        gap: 1rem;
    }
    
    .asg-progress-steps {
        display: flex;
        justify-content: center;
        align-items: center;
        gap: 1rem;
        margin-bottom: 2rem;
        padding: 1.5rem;
        background: white;
        border: 1px solid var(--asg-gray-200);
        border-radius: var(--asg-radius);
        box-shadow: var(--asg-shadow);
    }
    
    .asg-step {
        display: flex;
        align-items: center;
        gap: 0.5rem;
        color: var(--asg-gray-500);
        font-weight: 500;
    }
    
    .asg-step.active {
        color: var(--asg-primary);
    }
    
    .asg-step.completed {
        color: var(--asg-success);
    }
    
    .asg-step-number {
        width: 2rem;
        height: 2rem;
        border-radius: 50%;
        background: var(--asg-gray-200);
        color: var(--asg-gray-600);
        display: flex;
        align-items: center;
        justify-content: center;
        font-weight: 600;
        font-size: 0.875rem;
    }
    
    .asg-step.active .asg-step-number {
        background: var(--asg-primary);
        color: white;
    }
    
    .asg-step.completed .asg-step-number {
        background: var(--asg-success);
        color: white;
    }
    
    .asg-step-divider {
        width: 3rem;
        height: 2px;
        background: var(--asg-gray-200);
    }
    
    .asg-form-container {
        display: grid;
        grid-template-columns: 1fr 300px;
        gap: 2rem;
    }
    
    .asg-form-main {
        background: white;
        border: 1px solid var(--asg-gray-200);
        border-radius: var(--asg-radius);
        box-shadow: var(--asg-shadow);
    }
    
    .asg-form-header {
        padding: 1.5rem;
        border-bottom: 1px solid var(--asg-gray-200);
    }
    
    .asg-form-title {
        margin: 0 0 0.5rem 0;
        font-size: 1.25rem;
        font-weight: 600;
        color: var(--asg-gray-900);
    }
    
    .asg-form-subtitle {
        margin: 0;
        font-size: 0.875rem;
        color: var(--asg-gray-600);
    }
    
    .asg-form-body {
        padding: 1.5rem;
    }
    
    .asg-form-step {
        display: none;
    }
    
    .asg-form-step.active {
        display: block;
    }
    
    .asg-form-group {
        margin-bottom: 1.5rem;
    }
    
    .asg-label {
        display: block;
        margin-bottom: 0.5rem;
        font-size: 0.875rem;
        font-weight: 500;
        color: var(--asg-gray-700);
    }
    
    .asg-input {
        width: 100%;
        padding: 0.75rem;
        border: 1px solid var(--asg-gray-300);
        border-radius: var(--asg-radius);
        font-size: 0.875rem;
        transition: all var(--asg-transition);
        box-sizing: border-box;
    }
    
    .asg-input:focus {
        outline: none;
        border-color: var(--asg-primary);
        box-shadow: 0 0 0 3px rgba(37, 99, 235, 0.1);
    }
    
    .asg-textarea {
        min-height: 100px;
        resize: vertical;
    }
    
    .asg-select {
        background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 20 20'%3e%3cpath stroke='%236b7280' stroke-linecap='round' stroke-linejoin='round' stroke-width='1.5' d='m6 8 4 4 4-4'/%3e%3c/svg%3e");
        background-position: right 0.5rem center;
        background-repeat: no-repeat;
        background-size: 1.5em 1.5em;
        padding-right: 2.5rem;
    }
    
    .asg-form-grid {
        display: grid;
        grid-template-columns: 1fr 1fr;
        gap: 1rem;
    }
    
    .asg-image-upload {
        border: 2px dashed var(--asg-gray-300);
        border-radius: var(--asg-radius);
        padding: 2rem;
        text-align: center;
        cursor: pointer;
        transition: all var(--asg-transition);
    }
    
    .asg-image-upload:hover {
        border-color: var(--asg-primary);
        background-color: var(--asg-gray-50);
    }
    
    .asg-image-preview {
        max-width: 100%;
        max-height: 200px;
        border-radius: var(--asg-radius);
        margin-bottom: 1rem;
    }
    
    .asg-sidebar-preview {
        background: white;
        border: 1px solid var(--asg-gray-200);
        border-radius: var(--asg-radius);
        box-shadow: var(--asg-shadow);
        position: sticky;
        top: 2rem;
    }
    
    .asg-preview-header {
        padding: 1rem;
        border-bottom: 1px solid var(--asg-gray-200);
    }
    
    .asg-preview-title {
        margin: 0;
        font-size: 1rem;
        font-weight: 600;
        color: var(--asg-gray-900);
    }
    
    .asg-preview-body {
        padding: 1rem;
    }
    
    .asg-preview-image {
        width: 100%;
        height: 120px;
        background: var(--asg-gray-200);
        border-radius: var(--asg-radius);
        margin-bottom: 1rem;
        background-size: cover;
        background-position: center;
        display: flex;
        align-items: center;
        justify-content: center;
        color: var(--asg-gray-500);
        font-size: 2rem;
    }
    
    .asg-preview-course-title {
        margin: 0 0 0.5rem 0;
        font-weight: 600;
        color: var(--asg-gray-500);
    }
    
    .asg-preview-description {
        margin: 0 0 1rem 0;
        font-size: 0.875rem;
        color: var(--asg-gray-500);
        line-height: 1.4;
    }
    
    .asg-preview-meta {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 1rem;
    }
    
    .asg-preview-price {
        font-weight: 700;
        color: var(--asg-primary);
    }
    
    .asg-form-navigation {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 1.5rem;
        border-top: 1px solid var(--asg-gray-200);
        background: var(--asg-gray-50);
    }
    
    .asg-nav-buttons {
        display: flex;
        gap: 1rem;
    }
    
    .asg-field-help {
        margin-top: 0.25rem;
        font-size: 0.75rem;
        color: var(--asg-gray-500);
    }
    
    .asg-character-count {
        text-align: right;
        font-size: 0.75rem;
        color: var(--asg-gray-500);
        margin-top: 0.25rem;
    }
    
    @media (max-width: 768px) {
        .asg-course-creator {
            padding: 1rem 0.5rem;
        }
        
        .asg-creator-header {
            flex-direction: column;
            gap: 1rem;
            align-items: stretch;
        }
        
        .asg-progress-steps {
            flex-direction: column;
            gap: 0.5rem;
        }
        
        .asg-step-divider {
            width: 2px;
            height: 1rem;
        }
        
        .asg-form-container {
            grid-template-columns: 1fr;
        }
        
        .asg-sidebar-preview {
            order: -1;
            position: static;
        }
        
        .asg-form-grid {
            grid-template-columns: 1fr;
        }
    }
    </style>
    
    <!-- HTML del Creador de Cursos -->
    <div class="asg-course-creator">
        <div class="asg-creator-header">
            <h1 class="asg-creator-title">➕ Crear Nuevo Curso</h1>
            <div class="asg-creator-actions">
                <button class="asg-btn asg-btn-secondary" onclick="asgSaveDraft()">
                    💾 Guardar Borrador
                </button>
                <a href="/wp-admin/admin.php?page=asg-all-courses" class="asg-btn asg-btn-secondary">
                    ❌ Cancelar
                </a>
            </div>
        </div>
        
        <!-- Pasos del Progreso -->
        <div class="asg-progress-steps">
            <div class="asg-step active" data-step="1">
                <div class="asg-step-number">1</div>
                <span>Información Básica</span>
            </div>
            <div class="asg-step-divider"></div>
            <div class="asg-step" data-step="2">
                <div class="asg-step-number">2</div>
                <span>Contenido</span>
            </div>
            <div class="asg-step-divider"></div>
            <div class="asg-step" data-step="3">
                <div class="asg-step-number">3</div>
                <span>Configuración</span>
            </div>
            <div class="asg-step-divider"></div>
            <div class="asg-step" data-step="4">
                <div class="asg-step-number">4</div>
                <span>Revisión</span>
            </div>
        </div>
        
        <!-- Contenedor del Formulario -->
        <div class="asg-form-container">
            <!-- Formulario Principal -->
            <div class="asg-form-main">
                <form id="courseCreatorForm">
                    <!-- Paso 1: Información Básica -->
                    <div class="asg-form-step active" data-step="1">
                        <div class="asg-form-header">
                            <h2 class="asg-form-title">Información Básica del Curso</h2>
                            <p class="asg-form-subtitle">Proporciona la información fundamental de tu curso</p>
                        </div>
                        <div class="asg-form-body">
                            <div class="asg-form-group">
                                <label for="courseName" class="asg-label">Título del Curso *</label>
                                <input type="text" 
                                       id="courseName" 
                                       name="courseName" 
                                       class="asg-input" 
                                       placeholder="Ej: Como hacerte millonario en 30 días"
                                       required
                                       maxlength="255">
                                <div class="asg-field-help">Un título atractivo y descriptivo ayuda a atraer más estudiantes</div>
                            </div>
                            
                            <div class="asg-form-group">
                                <label for="courseDescription" class="asg-label">Descripción del Curso *</label>
                                <textarea id="courseDescription" 
                                          name="courseDescription" 
                                          class="asg-input asg-textarea" 
                                          placeholder="Describe qué aprenderán los estudiantes en este curso..."
                                          required
                                          maxlength="1000"></textarea>
                                <div class="asg-character-count">
                                    <span id="descriptionCount">0</span>/1000 caracteres
                                </div>
                            </div>
                            
                            <div class="asg-form-grid">
                                <div class="asg-form-group">
                                    <label for="courseCategory" class="asg-label">Categoría *</label>
                                    <select id="courseCategory" name="courseCategory" class="asg-input asg-select" required>
                                        <option value="">Selecciona una categoría</option>
                                        <option value="finanzas">Finanzas</option>
                                        <option value="marketing">Marketing</option>
                                        <option value="desarrollo-personal">Desarrollo Personal</option>
                                        <option value="tecnologia">Tecnología</option>
                                        <option value="negocios">Negocios</option>
                                        <option value="salud">Salud y Bienestar</option>
                                        <option value="educacion">Educación</option>
                                        <option value="arte">Arte y Creatividad</option>
                                    </select>
                                </div>
                                
                                <div class="asg-form-group">
                                    <label for="coursePrice" class="asg-label">Precio (€)</label>
                                    <input type="number" 
                                           id="coursePrice" 
                                           name="coursePrice" 
                                           class="asg-input" 
                                           placeholder="0.00" 
                                           step="0.01" 
                                           min="0">
                                </div>
                            </div>
                            
                            <div class="asg-form-group">
                                <label for="courseImage" class="asg-label">Imagen de Portada</label>
                                <div class="asg-image-upload" onclick="document.getElementById('courseImageInput').click()">
                                    <div id="imagePreview" style="display: none;">
                                        <img id="previewImg" class="asg-image-preview">
                                        <button type="button" class="asg-btn asg-btn-secondary" onclick="event.stopPropagation(); asgRemoveImage()">
                                            🗑️ Eliminar
                                        </button>
                                    </div>
                                    <div id="uploadPlaceholder">
                                        <div style="font-size: 3rem; margin-bottom: 1rem;">📷</div>
                                        <p style="margin: 0; font-weight: 500;">Haz clic para subir una imagen</p>
                                        <p style="margin: 0.5rem 0 0 0; font-size: 0.875rem; color: var(--asg-gray-500);">
                                            JPG, PNG o GIF (máx. 5MB)
                                        </p>
                                    </div>
                                </div>
                                <input type="file" 
                                       id="courseImageInput" 
                                       name="courseImage" 
                                       accept="image/*" 
                                       style="display: none;"
                                       onchange="asgHandleImageUpload(this)">
                            </div>
                        </div>
                    </div>
                    
                    <!-- Paso 2: Contenido (Simplificado para snippet) -->
                    <div class="asg-form-step" data-step="2">
                        <div class="asg-form-header">
                            <h2 class="asg-form-title">Contenido del Curso</h2>
                            <p class="asg-form-subtitle">Define los objetivos y beneficios principales</p>
                        </div>
                        <div class="asg-form-body">
                            <div class="asg-form-group">
                                <label class="asg-label">Objetivos de Aprendizaje</label>
                                <div id="objectivesContainer">
                                    <div class="asg-form-group">
                                        <input type="text" class="asg-input" placeholder="Ej: Dominar las finanzas personales">
                                    </div>
                                </div>
                                <button type="button" class="asg-btn asg-btn-secondary" onclick="asgAddObjective()">
                                    ➕ Agregar Objetivo
                                </button>
                            </div>
                            
                            <div class="asg-form-group">
                                <label class="asg-label">Beneficios del Curso</label>
                                <div id="benefitsContainer">
                                    <div class="asg-form-group">
                                        <input type="text" class="asg-input" placeholder="Ej: Libertad financiera en menos tiempo">
                                    </div>
                                </div>
                                <button type="button" class="asg-btn asg-btn-secondary" onclick="asgAddBenefit()">
                                    ➕ Agregar Beneficio
                                </button>
                            </div>
                        </div>
                    </div>
                    
                    <!-- Paso 3: Configuración -->
                    <div class="asg-form-step" data-step="3">
                        <div class="asg-form-header">
                            <h2 class="asg-form-title">Configuración del Curso</h2>
                            <p class="asg-form-subtitle">Ajustes adicionales y configuración SEO</p>
                        </div>
                        <div class="asg-form-body">
                            <div class="asg-form-grid">
                                <div class="asg-form-group">
                                    <label for="courseLanguage" class="asg-label">Idioma</label>
                                    <select id="courseLanguage" name="courseLanguage" class="asg-input asg-select">
                                        <option value="es" selected>Español</option>
                                        <option value="en">Inglés</option>
                                        <option value="fr">Francés</option>
                                        <option value="pt">Portugués</option>
                                    </select>
                                </div>
                                
                                <div class="asg-form-group">
                                    <label for="courseStatus" class="asg-label">Estado Inicial</label>
                                    <select id="courseStatus" name="courseStatus" class="asg-input asg-select">
                                        <option value="draft" selected>Borrador</option>
                                        <option value="published">Publicado</option>
                                    </select>
                                </div>
                            </div>
                            
                            <div class="asg-form-group">
                                <label>
                                    <input type="checkbox" id="courseFeatured" name="courseFeatured" style="margin-right: 0.5rem;">
                                    Marcar como curso destacado
                                </label>
                            </div>
                        </div>
                    </div>
                    
                    <!-- Paso 4: Revisión -->
                    <div class="asg-form-step" data-step="4">
                        <div class="asg-form-header">
                            <h2 class="asg-form-title">Revisión y Publicación</h2>
                            <p class="asg-form-subtitle">Revisa toda la información antes de crear el curso</p>
                        </div>
                        <div class="asg-form-body">
                            <div id="reviewContent">
                                <p style="text-align: center; color: var(--asg-gray-500); padding: 2rem;">
                                    La información del curso se mostrará aquí para revisión
                                </p>
                            </div>
                        </div>
                    </div>
                </form>
                
                <!-- Navegación del Formulario -->
                <div class="asg-form-navigation">
                    <button type="button" id="prevStepBtn" class="asg-btn asg-btn-secondary" style="display: none;">
                        ← Anterior
                    </button>
                    
                    <div class="asg-nav-buttons">
                        <button type="button" id="nextStepBtn" class="asg-btn asg-btn-primary">
                            Siguiente →
                        </button>
                        <button type="submit" id="createCourseBtn" class="asg-btn asg-btn-primary" style="display: none;">
                            ✅ Crear Curso
                        </button>
                    </div>
                </div>
            </div>
            
            <!-- Sidebar de Preview -->
            <div class="asg-sidebar-preview">
                <div class="asg-preview-header">
                    <h3 class="asg-preview-title">Vista Previa</h3>
                </div>
                <div class="asg-preview-body">
                    <div class="asg-preview-image" id="previewImage">📷</div>
                    <h4 class="asg-preview-course-title" id="previewTitle">Título del curso</h4>
                    <p class="asg-preview-description" id="previewDescription">Descripción del curso...</p>
                    <div class="asg-preview-meta">
                        <span class="asg-badge asg-badge-gray" id="previewCategory">Categoría</span>
                        <span class="asg-preview-price" id="previewPrice">€0.00</span>
                    </div>
                    <div style="font-size: 0.75rem; color: var(--asg-gray-500);">
                        <div>🌐 <span id="previewLanguage">Español</span></div>
                        <div>📊 <span id="previewStatus">Borrador</span></div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- JavaScript del Creador de Cursos -->
    <script>
    // Variables globales
    let asgCreatorData = {
        currentStep: 1,
        totalSteps: 4,
        courseData: {},
        hasUnsavedChanges: false
    };

    // Inicializar creador
    document.addEventListener('DOMContentLoaded', function() {
        asgInitCourseCreator();
    });

    function asgInitCourseCreator() {
        asgSetupCreatorEventListeners();
        asgUpdatePreview();
        asgLoadDraftIfExists();
    }

    function asgSetupCreatorEventListeners() {
        // Navegación de pasos
        const prevBtn = document.getElementById('prevStepBtn');
        const nextBtn = document.getElementById('nextStepBtn');
        const createBtn = document.getElementById('createCourseBtn');

        if (prevBtn) prevBtn.addEventListener('click', asgPreviousStep);
        if (nextBtn) nextBtn.addEventListener('click', asgNextStep);
        if (createBtn) createBtn.addEventListener('click', asgCreateCourse);

        // Campos del formulario para preview en tiempo real
        const formFields = ['courseName', 'courseDescription', 'courseCategory', 'coursePrice', 'courseLanguage', 'courseStatus'];
        formFields.forEach(fieldId => {
            const field = document.getElementById(fieldId);
            if (field) {
                field.addEventListener('input', asgUpdatePreview);
                field.addEventListener('change', asgUpdatePreview);
                field.addEventListener('input', () => asgCreatorData.hasUnsavedChanges = true);
            }
        });

        // Contador de caracteres para descripción
        const descriptionField = document.getElementById('courseDescription');
        if (descriptionField) {
            descriptionField.addEventListener('input', asgUpdateCharacterCount);
        }

        // Prevenir navegación accidental
        window.addEventListener('beforeunload', function(e) {
            if (asgCreatorData.hasUnsavedChanges) {
                e.preventDefault();
                e.returnValue = '';
            }
        });
    }

    function asgUpdateCharacterCount() {
        const field = document.getElementById('courseDescription');
        const counter = document.getElementById('descriptionCount');
        if (field && counter) {
            const length = field.value.length;
            counter.textContent = length;

            // Cambiar color según longitud
            if (length > 900) {
                counter.style.color = 'var(--asg-error)';
            } else if (length > 800) {
                counter.style.color = 'var(--asg-warning)';
            } else {
                counter.style.color = 'var(--asg-gray-500)';
            }
        }
    }

    function asgUpdatePreview() {
        // Actualizar título
        const titleField = document.getElementById('courseName');
        const previewTitle = document.getElementById('previewTitle');
        if (titleField && previewTitle) {
            const title = titleField.value.trim();
            previewTitle.textContent = title || 'Título del curso';
            previewTitle.style.color = title ? 'var(--asg-gray-900)' : 'var(--asg-gray-500)';
        }

        // Actualizar descripción
        const descField = document.getElementById('courseDescription');
        const previewDesc = document.getElementById('previewDescription');
        if (descField && previewDesc) {
            const desc = descField.value.trim();
            previewDesc.textContent = desc || 'Descripción del curso...';
            previewDesc.style.color = desc ? 'var(--asg-gray-600)' : 'var(--asg-gray-500)';
        }

        // Actualizar categoría
        const categoryField = document.getElementById('courseCategory');
        const previewCategory = document.getElementById('previewCategory');
        if (categoryField && previewCategory) {
            const category = categoryField.value;
            const categoryLabels = {
                finanzas: 'Finanzas',
                marketing: 'Marketing',
                'desarrollo-personal': 'Desarrollo Personal',
                tecnologia: 'Tecnología',
                negocios: 'Negocios',
                salud: 'Salud y Bienestar',
                educacion: 'Educación',
                arte: 'Arte y Creatividad'
            };

            previewCategory.textContent = categoryLabels[category] || 'Categoría';
            previewCategory.className = category ? 'asg-badge asg-badge-success' : 'asg-badge asg-badge-gray';
        }

        // Actualizar precio
        const priceField = document.getElementById('coursePrice');
        const previewPrice = document.getElementById('previewPrice');
        if (priceField && previewPrice) {
            const price = parseFloat(priceField.value) || 0;
            previewPrice.textContent = asgFormatCurrency(price);
        }

        // Actualizar idioma
        const languageField = document.getElementById('courseLanguage');
        const previewLanguage = document.getElementById('previewLanguage');
        if (languageField && previewLanguage) {
            const languageLabels = {
                es: 'Español',
                en: 'Inglés',
                fr: 'Francés',
                pt: 'Portugués'
            };
            previewLanguage.textContent = languageLabels[languageField.value] || 'Español';
        }

        // Actualizar estado
        const statusField = document.getElementById('courseStatus');
        const previewStatus = document.getElementById('previewStatus');
        if (statusField && previewStatus) {
            const statusLabels = {
                draft: 'Borrador',
                published: 'Publicado'
            };
            previewStatus.textContent = statusLabels[statusField.value] || 'Borrador';
        }
    }

    function asgNextStep() {
        if (!asgValidateCurrentStep()) {
            return;
        }

        if (asgCreatorData.currentStep < asgCreatorData.totalSteps) {
            asgCreatorData.currentStep++;
            asgUpdateStepDisplay();
            asgUpdateNavigation();
            asgScrollToTop();
        }
    }

    function asgPreviousStep() {
        if (asgCreatorData.currentStep > 1) {
            asgCreatorData.currentStep--;
            asgUpdateStepDisplay();
            asgUpdateNavigation();
            asgScrollToTop();
        }
    }

    function asgValidateCurrentStep() {
        switch (asgCreatorData.currentStep) {
            case 1:
                return asgValidateBasicInfo();
            case 2:
                return true; // Contenido es opcional
            case 3:
                return true; // Configuración es opcional
            case 4:
                return true; // Solo revisión
            default:
                return true;
        }
    }

    function asgValidateBasicInfo() {
        const courseName = document.getElementById('courseName');
        const courseDescription = document.getElementById('courseDescription');
        const courseCategory = document.getElementById('courseCategory');

        if (!courseName.value.trim()) {
            alert('❌ El título del curso es obligatorio');
            courseName.focus();
            return false;
        }

        if (courseName.value.trim().length < 3) {
            alert('❌ El título debe tener al menos 3 caracteres');
            courseName.focus();
            return false;
        }

        if (!courseDescription.value.trim()) {
            alert('❌ La descripción del curso es obligatoria');
            courseDescription.focus();
            return false;
        }

        if (courseDescription.value.trim().length < 10) {
            alert('❌ La descripción debe tener al menos 10 caracteres');
            courseDescription.focus();
            return false;
        }

        if (!courseCategory.value) {
            alert('❌ Debes seleccionar una categoría');
            courseCategory.focus();
            return false;
        }

        return true;
    }

    function asgUpdateStepDisplay() {
        // Actualizar indicadores de pasos
        const steps = document.querySelectorAll('.asg-step');
        steps.forEach((step, index) => {
            const stepNumber = index + 1;
            step.classList.remove('active', 'completed');

            if (stepNumber === asgCreatorData.currentStep) {
                step.classList.add('active');
            } else if (stepNumber < asgCreatorData.currentStep) {
                step.classList.add('completed');
                const stepNumberEl = step.querySelector('.asg-step-number');
                if (stepNumberEl) {
                    stepNumberEl.textContent = '✓';
                }
            } else {
                const stepNumberEl = step.querySelector('.asg-step-number');
                if (stepNumberEl) {
                    stepNumberEl.textContent = stepNumber;
                }
            }
        });

        // Mostrar/ocultar pasos del formulario
        const formSteps = document.querySelectorAll('.asg-form-step');
        formSteps.forEach((step, index) => {
            if (index + 1 === asgCreatorData.currentStep) {
                step.classList.add('active');
            } else {
                step.classList.remove('active');
            }
        });

        // Actualizar contenido de revisión en el paso 4
        if (asgCreatorData.currentStep === 4) {
            asgUpdateReviewContent();
        }
    }

    function asgUpdateNavigation() {
        const prevBtn = document.getElementById('prevStepBtn');
        const nextBtn = document.getElementById('nextStepBtn');
        const createBtn = document.getElementById('createCourseBtn');

        // Botón anterior
        if (prevBtn) {
            prevBtn.style.display = asgCreatorData.currentStep > 1 ? 'block' : 'none';
        }

        // Botón siguiente
        if (nextBtn) {
            nextBtn.style.display = asgCreatorData.currentStep < asgCreatorData.totalSteps ? 'block' : 'none';
        }

        // Botón crear
        if (createBtn) {
            createBtn.style.display = asgCreatorData.currentStep === asgCreatorData.totalSteps ? 'block' : 'none';
        }
    }

    function asgUpdateReviewContent() {
        const reviewContainer = document.getElementById('reviewContent');
        if (!reviewContainer) return;

        const formData = asgCollectFormData();

        reviewContainer.innerHTML = `
            <div style="space-y: 1rem;">
                <div style="border: 1px solid var(--asg-gray-200); border-radius: var(--asg-radius); padding: 1rem; margin-bottom: 1rem;">
                    <h4 style="margin: 0 0 0.5rem 0; color: var(--asg-gray-900);">📝 Información Básica</h4>
                    <p><strong>Título:</strong> ${formData.name || 'Sin título'}</p>
                    <p><strong>Descripción:</strong> ${formData.description || 'Sin descripción'}</p>
                    <p><strong>Categoría:</strong> ${asgGetCategoryLabel(formData.category)}</p>
                    <p><strong>Precio:</strong> ${asgFormatCurrency(formData.price)}</p>
                </div>

                <div style="border: 1px solid var(--asg-gray-200); border-radius: var(--asg-radius); padding: 1rem; margin-bottom: 1rem;">
                    <h4 style="margin: 0 0 0.5rem 0; color: var(--asg-gray-900);">⚙️ Configuración</h4>
                    <p><strong>Idioma:</strong> ${asgGetLanguageLabel(formData.language)}</p>
                    <p><strong>Estado:</strong> ${asgGetStatusLabel(formData.status)}</p>
                    <p><strong>Destacado:</strong> ${formData.featured ? 'Sí' : 'No'}</p>
                </div>

                <div style="background: var(--asg-gray-50); border-radius: var(--asg-radius); padding: 1rem; text-align: center;">
                    <p style="margin: 0; color: var(--asg-gray-600);">
                        ✅ Todo listo para crear el curso
                    </p>
                </div>
            </div>
        `;
    }

    function asgCollectFormData() {
        return {
            name: document.getElementById('courseName')?.value || '',
            description: document.getElementById('courseDescription')?.value || '',
            category: document.getElementById('courseCategory')?.value || '',
            price: parseFloat(document.getElementById('coursePrice')?.value) || 0,
            language: document.getElementById('courseLanguage')?.value || 'es',
            status: document.getElementById('courseStatus')?.value || 'draft',
            featured: document.getElementById('courseFeatured')?.checked || false
        };
    }

    function asgScrollToTop() {
        document.querySelector('.asg-course-creator').scrollIntoView({
            behavior: 'smooth',
            block: 'start'
        });
    }

    // Funciones de utilidad
    function asgGetCategoryLabel(category) {
        const categories = {
            finanzas: 'Finanzas',
            marketing: 'Marketing',
            'desarrollo-personal': 'Desarrollo Personal',
            tecnologia: 'Tecnología',
            negocios: 'Negocios',
            salud: 'Salud y Bienestar',
            educacion: 'Educación',
            arte: 'Arte y Creatividad'
        };
        return categories[category] || 'Sin categoría';
    }

    function asgGetLanguageLabel(language) {
        const languages = {
            es: 'Español',
            en: 'Inglés',
            fr: 'Francés',
            pt: 'Portugués'
        };
        return languages[language] || 'Español';
    }

    function asgGetStatusLabel(status) {
        const statuses = {
            draft: 'Borrador',
            published: 'Publicado'
        };
        return statuses[status] || 'Borrador';
    }

    function asgFormatCurrency(amount) {
        return new Intl.NumberFormat('es-ES', {
            style: 'currency',
            currency: 'EUR'
        }).format(amount || 0);
    }

    // Funciones de imagen
    function asgHandleImageUpload(input) {
        const file = input.files[0];
        if (!file) return;

        // Validar archivo
        if (file.size > 5 * 1024 * 1024) {
            alert('❌ La imagen no puede superar los 5MB');
            input.value = '';
            return;
        }

        if (!file.type.startsWith('image/')) {
            alert('❌ Solo se permiten archivos de imagen');
            input.value = '';
            return;
        }

        const reader = new FileReader();
        reader.onload = function(e) {
            const imagePreview = document.getElementById('imagePreview');
            const uploadPlaceholder = document.getElementById('uploadPlaceholder');
            const previewImg = document.getElementById('previewImg');
            const previewImage = document.getElementById('previewImage');

            if (imagePreview && uploadPlaceholder && previewImg) {
                previewImg.src = e.target.result;
                imagePreview.style.display = 'block';
                uploadPlaceholder.style.display = 'none';

                // Actualizar preview del sidebar
                if (previewImage) {
                    previewImage.style.backgroundImage = `url(${e.target.result})`;
                    previewImage.style.backgroundSize = 'cover';
                    previewImage.innerHTML = '';
                }

                asgCreatorData.hasUnsavedChanges = true;
            }
        };

        reader.readAsDataURL(file);
    }

    function asgRemoveImage() {
        const imagePreview = document.getElementById('imagePreview');
        const uploadPlaceholder = document.getElementById('uploadPlaceholder');
        const courseImageInput = document.getElementById('courseImageInput');
        const previewImage = document.getElementById('previewImage');

        if (imagePreview && uploadPlaceholder && courseImageInput) {
            imagePreview.style.display = 'none';
            uploadPlaceholder.style.display = 'block';
            courseImageInput.value = '';

            // Resetear preview del sidebar
            if (previewImage) {
                previewImage.style.backgroundImage = '';
                previewImage.innerHTML = '📷';
            }

            asgCreatorData.hasUnsavedChanges = true;
        }
    }

    // Funciones de contenido
    function asgAddObjective() {
        const container = document.getElementById('objectivesContainer');
        if (container) {
            const newObjective = document.createElement('div');
            newObjective.className = 'asg-form-group';
            newObjective.innerHTML = `
                <div style="display: flex; gap: 0.5rem;">
                    <input type="text" class="asg-input" placeholder="Nuevo objetivo de aprendizaje">
                    <button type="button" class="asg-btn asg-btn-secondary" onclick="this.parentElement.parentElement.remove()">
                        🗑️
                    </button>
                </div>
            `;
            container.appendChild(newObjective);
            asgCreatorData.hasUnsavedChanges = true;
        }
    }

    function asgAddBenefit() {
        const container = document.getElementById('benefitsContainer');
        if (container) {
            const newBenefit = document.createElement('div');
            newBenefit.className = 'asg-form-group';
            newBenefit.innerHTML = `
                <div style="display: flex; gap: 0.5rem;">
                    <input type="text" class="asg-input" placeholder="Nuevo beneficio del curso">
                    <button type="button" class="asg-btn asg-btn-secondary" onclick="this.parentElement.parentElement.remove()">
                        🗑️
                    </button>
                </div>
            `;
            container.appendChild(newBenefit);
            asgCreatorData.hasUnsavedChanges = true;
        }
    }

    // Funciones principales
    function asgSaveDraft() {
        const formData = asgCollectFormData();

        // Guardar en localStorage como backup
        localStorage.setItem('asg_course_draft', JSON.stringify({
            data: formData,
            step: asgCreatorData.currentStep,
            timestamp: new Date().toISOString()
        }));

        asgCreatorData.hasUnsavedChanges = false;
        alert('💾 Borrador guardado correctamente');
    }

    function asgLoadDraftIfExists() {
        const draft = localStorage.getItem('asg_course_draft');
        if (draft) {
            try {
                const draftData = JSON.parse(draft);
                if (confirm('📝 Se encontró un borrador guardado. ¿Quieres cargarlo?')) {
                    asgLoadDraftData(draftData.data);
                    asgCreatorData.currentStep = draftData.step || 1;
                    asgUpdateStepDisplay();
                    asgUpdateNavigation();
                    asgUpdatePreview();
                }
            } catch (error) {
                console.error('Error loading draft:', error);
                localStorage.removeItem('asg_course_draft');
            }
        }
    }

    function asgLoadDraftData(data) {
        if (data.name) document.getElementById('courseName').value = data.name;
        if (data.description) document.getElementById('courseDescription').value = data.description;
        if (data.category) document.getElementById('courseCategory').value = data.category;
        if (data.price) document.getElementById('coursePrice').value = data.price;
        if (data.language) document.getElementById('courseLanguage').value = data.language;
        if (data.status) document.getElementById('courseStatus').value = data.status;
        if (data.featured) document.getElementById('courseFeatured').checked = data.featured;

        asgUpdateCharacterCount();
    }

    async function asgCreateCourse() {
        if (!asgValidateCurrentStep()) {
            return;
        }

        const formData = asgCollectFormData();

        if (confirm('✅ ¿Estás seguro de que quieres crear este curso?')) {
            try {
                // Simular creación del curso
                alert('🎉 ¡Curso creado exitosamente!');

                // Limpiar borrador
                localStorage.removeItem('asg_course_draft');
                asgCreatorData.hasUnsavedChanges = false;

                // Redirigir a gestión de cursos
                setTimeout(() => {
                    window.location.href = '/wp-admin/admin.php?page=asg-all-courses';
                }, 1000);

            } catch (error) {
                console.error('Error creating course:', error);
                alert('❌ Error al crear el curso. Inténtalo de nuevo.');
            }
        }
    }
    </script>

    <?php
    return ob_get_clean();
}

/**
 * ========================================
 * PÁGINA DE ADMINISTRACIÓN
 * ========================================
 */
add_action('admin_menu', function() {
    add_submenu_page(
        'asg-dashboard',
        'Crear Curso',
        'Nuevo Curso',
        'manage_options',
        'asg-new-course',
        'asg_admin_new_course_page'
    );
});

function asg_admin_new_course_page() {
    echo '<div class="wrap">';
    echo do_shortcode('[asg_course_creator]');
    echo '</div>';
}
?>
