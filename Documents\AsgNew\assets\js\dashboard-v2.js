/**
 * ========================================
 * DASHBOARD v2.0 - LÓGICA PRINCIPAL
 * ========================================
 * 
 * Descripción: Lógica específica del dashboard moderno
 * Incluye: Carga de estadísticas, cursos recientes, acciones rápidas
 * 
 * Autor: ASG Team
 * Fecha: 2025-01-04
 * Versión: 2.0.0 - DASHBOARD MODERNO
 */

/**
 * ========================================
 * CLASE PRINCIPAL DEL DASHBOARD
 * ========================================
 */
class ASGDashboard {
    constructor() {
        this.apiUrl = ASG.config.apiBaseUrl;
        this.stats = {
            totalCourses: 0,
            publishedCourses: 0,
            draftCourses: 0,
            totalRevenue: 0,
            avgPrice: 0
        };
        this.recentCourses = [];
        this.refreshInterval = null;
        
        this.init();
    }
    
    init() {
        console.log('🏠 Inicializando Dashboard v2.0...');
        
        this.setupEventListeners();
        this.loadDashboardData();
        this.startAutoRefresh();
        
        console.log('✅ Dashboard inicializado correctamente');
    }
    
    setupEventListeners() {
        // Botón de actualizar
        const refreshBtn = document.getElementById('refreshBtn');
        if (refreshBtn) {
            refreshBtn.addEventListener('click', () => {
                this.refreshDashboard();
            });
        }
        
        // Botones de acciones rápidas
        const importBtn = document.getElementById('importBtn');
        if (importBtn) {
            importBtn.addEventListener('click', () => {
                this.handleImport();
            });
        }
        
        const exportBtn = document.getElementById('exportBtn');
        if (exportBtn) {
            exportBtn.addEventListener('click', () => {
                this.handleExport();
            });
        }
        
        // Escuchar eventos de visibilidad para pausar/reanudar auto-refresh
        document.addEventListener('visibilitychange', () => {
            if (document.hidden) {
                this.stopAutoRefresh();
            } else {
                this.startAutoRefresh();
                this.loadDashboardData(); // Actualizar al volver a la pestaña
            }
        });
    }
    
    async loadDashboardData() {
        try {
            ASG.loading().show('dashboard-load');
            
            // Cargar estadísticas y cursos en paralelo
            const [statsData, coursesData] = await Promise.all([
                this.loadStats(),
                this.loadRecentCourses()
            ]);
            
            this.updateStatsUI(statsData);
            this.updateRecentCoursesUI(coursesData);
            
            ASG.notifications().success('Dashboard actualizado correctamente');
            
        } catch (error) {
            console.error('Error cargando datos del dashboard:', error);
            ASG.notifications().error('Error al cargar los datos del dashboard');
            this.showErrorState();
        } finally {
            ASG.loading().hide('dashboard-load');
        }
    }
    
    async loadStats() {
        try {
            // Intentar cargar desde API real
            const response = await fetch(`${this.apiUrl}/admin/dashboard/stats`);
            
            if (response.ok) {
                const data = await response.json();
                return data.data;
            } else {
                throw new Error('API no disponible');
            }
        } catch (error) {
            console.warn('API no disponible, usando datos simulados:', error);
            return this.generateMockStats();
        }
    }
    
    async loadRecentCourses() {
        try {
            // Intentar cargar desde API real
            const response = await fetch(`${this.apiUrl}/admin/courses?per_page=5&sort=recent`);
            
            if (response.ok) {
                const data = await response.json();
                return data.data;
            } else {
                throw new Error('API no disponible');
            }
        } catch (error) {
            console.warn('API no disponible, usando datos simulados:', error);
            return this.generateMockCourses();
        }
    }
    
    generateMockStats() {
        // Datos simulados para desarrollo
        return {
            totalCourses: 12,
            publishedCourses: 8,
            draftCourses: 4,
            archivedCourses: 0,
            totalRevenue: 2399.92,
            avgPrice: 199.99,
            coursesChange: 25, // Porcentaje de cambio vs mes anterior
            studentsCount: 156,
            completionRate: 78.5
        };
    }
    
    generateMockCourses() {
        // Cursos simulados para desarrollo
        return [
            {
                id_course: 1,
                code_course: 'course_1',
                name_course: 'Como hacerte millonario?',
                description_course: 'Aprende las estrategias más efectivas para generar riqueza y alcanzar la libertad financiera.',
                cover_img: 'https://images.unsplash.com/photo-1579621970563-ebec7560ff3e?w=400&h=300&fit=crop',
                price_course: 299.99,
                status_course: 'published',
                category_course: 'finanzas',
                created_at: '2024-12-15 10:30:00',
                module_count: 8,
                lesson_count: 24
            },
            {
                id_course: 2,
                code_course: 'course_2',
                name_course: 'Marketing Digital Avanzado',
                description_course: 'Domina las técnicas más avanzadas del marketing digital y haz crecer tu negocio online.',
                cover_img: 'https://images.unsplash.com/photo-1460925895917-afdab827c52f?w=400&h=300&fit=crop',
                price_course: 199.99,
                status_course: 'published',
                category_course: 'marketing',
                created_at: '2024-12-10 14:20:00',
                module_count: 6,
                lesson_count: 18
            },
            {
                id_course: 3,
                code_course: 'course_3',
                name_course: 'Desarrollo Personal y Liderazgo',
                description_course: 'Desarrolla tus habilidades de liderazgo y crecimiento personal para alcanzar el éxito.',
                cover_img: 'https://images.unsplash.com/photo-1552664730-d307ca884978?w=400&h=300&fit=crop',
                price_course: 149.99,
                status_course: 'draft',
                category_course: 'desarrollo-personal',
                created_at: '2024-12-08 09:15:00',
                module_count: 5,
                lesson_count: 15
            }
        ];
    }
    
    updateStatsUI(stats) {
        // Actualizar números principales
        this.animateNumber('totalCourses', stats.totalCourses);
        this.animateNumber('publishedCourses', stats.publishedCourses);
        this.animateNumber('draftCourses', stats.draftCourses);
        
        // Actualizar ingresos con formato de moneda
        const totalRevenueEl = document.getElementById('totalRevenue');
        if (totalRevenueEl) {
            this.animateNumber('totalRevenue', stats.totalRevenue, (value) => {
                return ASG.utils.formatCurrency(value);
            });
        }
        
        // Actualizar porcentajes y badges
        const publishedPercentage = stats.totalCourses > 0 ? 
            Math.round((stats.publishedCourses / stats.totalCourses) * 100) : 0;
        const draftPercentage = stats.totalCourses > 0 ? 
            Math.round((stats.draftCourses / stats.totalCourses) * 100) : 0;
        
        this.updateElement('publishedPercentage', `${publishedPercentage}%`);
        this.updateElement('draftPercentage', `${draftPercentage}%`);
        this.updateElement('avgPrice', ASG.utils.formatCurrency(stats.avgPrice));
        
        // Actualizar cambio vs mes anterior
        const changeEl = document.getElementById('coursesChange');
        if (changeEl && stats.coursesChange !== undefined) {
            const changeText = stats.coursesChange > 0 ? `+${stats.coursesChange}%` : `${stats.coursesChange}%`;
            changeEl.textContent = changeText;
            
            // Cambiar color según el cambio
            changeEl.className = stats.coursesChange > 0 ? 'asg-badge asg-badge-success' : 
                                stats.coursesChange < 0 ? 'asg-badge asg-badge-error' : 
                                'asg-badge asg-badge-gray';
        }
        
        this.stats = stats;
    }
    
    updateRecentCoursesUI(courses) {
        const container = document.getElementById('recentCourses');
        if (!container) return;
        
        if (!courses || courses.length === 0) {
            container.innerHTML = `
                <div style="text-align: center; padding: var(--asg-space-8); color: var(--asg-gray-500);">
                    <i class="bi bi-collection" style="font-size: 3rem; margin-bottom: var(--asg-space-4);"></i>
                    <p style="margin: 0; font-size: var(--asg-text-lg); font-weight: var(--asg-font-medium);">
                        No hay cursos recientes
                    </p>
                    <p style="margin: var(--asg-space-2) 0 0 0; font-size: var(--asg-text-sm);">
                        Crea tu primer curso para comenzar
                    </p>
                </div>
            `;
            return;
        }
        
        container.innerHTML = courses.map(course => this.createCourseCard(course)).join('');
        this.recentCourses = courses;
    }
    
    createCourseCard(course) {
        const statusBadge = this.getStatusBadge(course.status_course);
        const formattedDate = ASG.utils.formatDate(course.created_at);
        const editUrl = `https://abilityseminarsgroup.com/edit-course/?id=${course.code_course}`;
        
        return `
            <div class="course-card" style="
                display: flex; 
                align-items: center; 
                gap: var(--asg-space-4); 
                padding: var(--asg-space-4); 
                border: 1px solid var(--asg-gray-200); 
                border-radius: var(--asg-radius); 
                transition: all var(--asg-transition);
                cursor: pointer;
            " onclick="window.location.href='${editUrl}'">
                <div style="
                    width: 4rem; 
                    height: 4rem; 
                    background-image: url('${course.cover_img || 'https://images.unsplash.com/photo-1516321318423-f06f85e504b3?w=400&h=300&fit=crop'}'); 
                    background-size: cover; 
                    background-position: center; 
                    border-radius: var(--asg-radius); 
                    flex-shrink: 0;
                "></div>
                
                <div style="flex: 1; min-width: 0;">
                    <div style="display: flex; align-items: center; gap: var(--asg-space-2); margin-bottom: var(--asg-space-1);">
                        <h3 style="
                            margin: 0; 
                            font-size: var(--asg-text-base); 
                            font-weight: var(--asg-font-semibold); 
                            color: var(--asg-gray-900);
                            overflow: hidden;
                            text-overflow: ellipsis;
                            white-space: nowrap;
                        ">${course.name_course}</h3>
                        ${statusBadge}
                    </div>
                    
                    <p style="
                        margin: 0; 
                        font-size: var(--asg-text-sm); 
                        color: var(--asg-gray-600);
                        overflow: hidden;
                        text-overflow: ellipsis;
                        white-space: nowrap;
                    ">${course.description_course}</p>
                    
                    <div style="
                        display: flex; 
                        align-items: center; 
                        gap: var(--asg-space-4); 
                        margin-top: var(--asg-space-2); 
                        font-size: var(--asg-text-xs); 
                        color: var(--asg-gray-500);
                    ">
                        <span><i class="bi bi-calendar"></i> ${formattedDate}</span>
                        <span><i class="bi bi-collection"></i> ${course.module_count || 0} módulos</span>
                        <span><i class="bi bi-play-circle"></i> ${course.lesson_count || 0} lecciones</span>
                    </div>
                </div>
                
                <div style="display: flex; flex-direction: column; align-items: flex-end; gap: var(--asg-space-2);">
                    <span style="
                        font-size: var(--asg-text-lg); 
                        font-weight: var(--asg-font-bold); 
                        color: var(--asg-primary-600);
                    ">${ASG.utils.formatCurrency(course.price_course)}</span>
                    
                    <button class="asg-btn asg-btn-sm asg-btn-secondary" onclick="event.stopPropagation(); window.location.href='${editUrl}'">
                        <i class="bi bi-pencil"></i>
                        Editar
                    </button>
                </div>
            </div>
        `;
    }
    
    getStatusBadge(status) {
        const badges = {
            published: '<span class="asg-badge asg-badge-success">Publicado</span>',
            draft: '<span class="asg-badge asg-badge-warning">Borrador</span>',
            archived: '<span class="asg-badge asg-badge-gray">Archivado</span>'
        };
        
        return badges[status] || badges.draft;
    }
    
    animateNumber(elementId, targetValue, formatter = null) {
        const element = document.getElementById(elementId);
        if (!element) return;
        
        const startValue = 0;
        const duration = 1000; // 1 segundo
        const startTime = performance.now();
        
        const animate = (currentTime) => {
            const elapsed = currentTime - startTime;
            const progress = Math.min(elapsed / duration, 1);
            
            // Easing function (ease-out)
            const easeOut = 1 - Math.pow(1 - progress, 3);
            const currentValue = startValue + (targetValue - startValue) * easeOut;
            
            if (formatter) {
                element.textContent = formatter(currentValue);
            } else {
                element.textContent = Math.round(currentValue);
            }
            
            if (progress < 1) {
                requestAnimationFrame(animate);
            }
        };
        
        requestAnimationFrame(animate);
    }
    
    updateElement(elementId, value) {
        const element = document.getElementById(elementId);
        if (element) {
            element.textContent = value;
        }
    }
    
    refreshDashboard() {
        ASG.notifications().info('Actualizando dashboard...');
        this.loadDashboardData();
    }
    
    startAutoRefresh() {
        // Actualizar cada 5 minutos
        this.refreshInterval = setInterval(() => {
            this.loadDashboardData();
        }, 5 * 60 * 1000);
    }
    
    stopAutoRefresh() {
        if (this.refreshInterval) {
            clearInterval(this.refreshInterval);
            this.refreshInterval = null;
        }
    }
    
    handleImport() {
        ASG.notifications().info('Función de importación en desarrollo');
        // TODO: Implementar importación de contenido
    }
    
    handleExport() {
        ASG.notifications().info('Preparando exportación...');
        
        // Simular exportación
        setTimeout(() => {
            const data = {
                stats: this.stats,
                courses: this.recentCourses,
                exportDate: new Date().toISOString()
            };
            
            const blob = new Blob([JSON.stringify(data, null, 2)], { type: 'application/json' });
            const url = URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = `asg-dashboard-export-${new Date().toISOString().split('T')[0]}.json`;
            document.body.appendChild(a);
            a.click();
            document.body.removeChild(a);
            URL.revokeObjectURL(url);
            
            ASG.notifications().success('Datos exportados correctamente');
        }, 1000);
    }
    
    showErrorState() {
        // Mostrar estado de error en las estadísticas
        const elements = ['totalCourses', 'publishedCourses', 'draftCourses', 'totalRevenue'];
        elements.forEach(id => {
            const element = document.getElementById(id);
            if (element) {
                element.textContent = 'Error';
                element.style.color = 'var(--asg-error)';
            }
        });
        
        // Mostrar estado de error en cursos recientes
        const container = document.getElementById('recentCourses');
        if (container) {
            container.innerHTML = `
                <div style="text-align: center; padding: var(--asg-space-8); color: var(--asg-error);">
                    <i class="bi bi-exclamation-triangle" style="font-size: 3rem; margin-bottom: var(--asg-space-4);"></i>
                    <p style="margin: 0; font-size: var(--asg-text-lg); font-weight: var(--asg-font-medium);">
                        Error al cargar los datos
                    </p>
                    <button class="asg-btn asg-btn-primary" onclick="dashboard.refreshDashboard()" style="margin-top: var(--asg-space-4);">
                        Reintentar
                    </button>
                </div>
            `;
        }
    }
}

/**
 * ========================================
 * INICIALIZACIÓN
 * ========================================
 */
let dashboard = null;

document.addEventListener('DOMContentLoaded', function() {
    // Esperar a que los componentes base estén listos
    setTimeout(() => {
        dashboard = new ASGDashboard();
        
        // Hacer disponible globalmente para debugging
        window.ASGDashboard = dashboard;
    }, 100);
});
