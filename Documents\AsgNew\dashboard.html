<!--
         * ========================================
         * DASHBOARD PRINCIPAL - ABILITYSEMINARSGROUP
         * ========================================
         *
         * Descripción: Panel principal de administración con navegación,
         * tarjetas de acceso rápido y gestión responsive del sidebar.
         *
         * Funcionalidades:
         * - Dashboard responsive con sidebar colapsable
         * - Tarjetas de navegación rápida
         * - Gestión de menús desplegables
         * - Navegación optimizada para móvil y desktop
         *
         * Autor: <PERSON>.M
         * Fecha: 2025
         * Versión: 1.0
   
    -->

<!DOCTYPE html>
<html lang="es">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Admin Dashboard</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.11.3/font/bootstrap-icons.css">
    <link href="https://fonts.googleapis.com/css2?family=Inter:ital,opsz,wght@0,14..32,100..900;1,14..32,100..900&display=swap" rel="stylesheet">
    
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background-color: #f8f9fa;
            overflow-x: hidden;
        }

        /* Navbar Styles */
        .navbar {
            background-color: #1e3a5f;
            height: 70px;
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            z-index: 1030;
            padding: 0 1rem;
        }

        .navbar .btn {
            color: white;
            border: none;
            background: none;
            padding: 0.5rem;
            border-radius: 4px;
            transition: background-color 0.2s ease;
        }

        .navbar .btn:hover {
            background-color: rgba(255, 255, 255, 0.1);
        }

        .navbar .btn:focus {
            box-shadow: none;
            outline: none;
        }

        /* .navbar-brand img filter removed for real logo */

        .avatar {
            width: 35px;
            height: 35px;
            background-color: #4299e1;
            font-weight: bold;
            font-size: 1rem;
        }

        /* Sidebar Styles - Reducido y sin overflow */
        .sidebar {
            position: fixed;
            top: 0;
            left: 0;
            width: 220px;
            height: 100vh;
            background-color: #1e3a5f;
            color: white;
            transform: translateX(-100%);
            transition: all 0.3s ease;
            z-index: 1040;
            padding-top: 70px;
        }

        .sidebar.show {
            transform: translateX(0);
        }

        .sidebar.collapsed {
            width: 55px;
        }

        .sidebar.collapsed .menu-text {
            display: none;
        }

        .sidebar.collapsed .dropdown {
            display: none;
        }

        .sidebar.collapsed:not(.desktop-hidden):hover {
            width: 220px;
        }

        .sidebar.collapsed:not(.desktop-hidden):hover .menu-text {
            display: inline;
        }

        .sidebar.collapsed:not(.desktop-hidden):hover .dropdown {
            display: inline;
        }

        /* Disable all hover effects when sidebar is hidden by hamburger button */
        .sidebar.desktop-hidden:hover {
            transform: translateX(-100%) !important;
            width: 220px !important;
        }

        .sidebar-nav {
            padding: 0.5rem 0;
            height: calc(100vh - 70px);
            overflow: visible;
        }

        .sidebar-parent > a {
            padding: 0.6rem 1rem;
            display: flex;
            align-items: center;
            color: #b8d4f0;
            text-decoration: none;
            transition: all 0.3s ease;
            border-left: 3px solid transparent;
            font-size: 0.9rem;
        }

        .sidebar-parent > a:hover,
        .sidebar-parent.active > a {
            background-color: #2c5282;
            color: white;
            border-left-color: #4299e1;
        }

        .sidebar-children {
            list-style: none;
            padding: 0;
            margin: 0;
            background-color: #2c5282;
            max-height: 0;
            overflow: hidden;
            transition: max-height 0.3s ease;
        }

        .sidebar-parent.expanded .sidebar-children {
            max-height: 200px;
        }

        .sidebar-children li a {
            padding: 0.4rem 1rem 0.4rem 2.5rem;
            display: flex;
            align-items: center;
            color: #b8d4f0;
            text-decoration: none;
            transition: all 0.3s ease;
            font-size: 0.85rem;
        }

        .sidebar-children li a:hover {
            background-color: #3182ce;
            color: white;
        }

        /* Body Content */
        .body-content {
            margin-left: 0;
            margin-top: 70px;
            transition: margin-left 0.3s ease;
            min-height: calc(100vh - 70px);
        }

        .body-content.collapsed {
            margin-left: 55px;
        }

        .page-header {
            background-color: #1e3a5f;
            color: white;
            padding: 1rem 2rem;
            margin-bottom: 0;
            position: sticky;
            top: 70px;
            z-index: 1020;
        }

        /* Welcome Section */
        .welcome-section {
            text-align: center;
            margin: 2rem 0;
            padding-top: 1rem;
        }

        .welcome-title {
            font-size: 2.5rem;
            color: #1e3a5f;
            margin-bottom: 0.5rem;
            font-weight: 600;
        }

        .welcome-subtitle {
            font-size: 1.1rem;
            color: #666;
        }

        /* Cards Grid */
        .cards-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 2rem;
            max-width: 1200px;
            margin: 0 auto;
            padding: 0 2rem;
        }

        .dashboard-card {
            background: linear-gradient(135deg, #f6d55c, #ed9121);
            padding: 2rem;
            border-radius: 12px;
            text-align: center;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
            transition: transform 0.3s ease, box-shadow 0.3s ease;
            cursor: pointer;
            border: none;
        }

        .dashboard-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 8px 15px rgba(0, 0, 0, 0.2);
        }

        .card-icon {
            font-size: 3rem;
            color: #1e3a5f;
            margin-bottom: 1rem;
        }

        .card-title {
            font-size: 1.3rem;
            color: #1e3a5f;
            margin-bottom: 0.75rem;
            font-weight: 600;
        }

        .card-description {
            color: #5a5a5a;
            font-size: 0.95rem;
            line-height: 1.4;
        }

        /* Estilos para estadísticas */
        .stats-section {
            margin-bottom: 2rem;
        }

        .stat-card {
            background: white;
            border-radius: 12px;
            padding: 1.5rem;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
            display: flex;
            align-items: center;
            gap: 1rem;
            transition: transform 0.3s ease, box-shadow 0.3s ease;
        }

        .stat-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
        }

        .stat-icon {
            width: 60px;
            height: 60px;
            border-radius: 12px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 1.5rem;
            color: white;
        }

        .stat-card:nth-child(1) .stat-icon {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        }

        .stat-card:nth-child(2) .stat-icon {
            background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
        }

        .stat-card:nth-child(3) .stat-icon {
            background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
        }

        .stat-card:nth-child(4) .stat-icon {
            background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);
        }

        .stat-info h3 {
            margin: 0;
            font-size: 2rem;
            font-weight: 700;
            color: #2c3e50;
        }

        .stat-info p {
            margin: 0;
            color: #7f8c8d;
            font-size: 0.9rem;
            font-weight: 500;
        }

        /* Overlay */
        .sidebar-overlay {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0, 0, 0, 0.5);
            z-index: 1035;
            display: none;
        }

        .sidebar-overlay.show {
            display: block;
        }

        /* Responsive */
        @media (min-width: 992px) {
            .sidebar:not(.desktop-hidden) {
                transform: translateX(0);
                position: fixed;
            }

            .sidebar.desktop-hidden {
                transform: translateX(-100%);
            }

            .body-content {
                margin-left: 220px;
                transition: margin-left 0.3s ease;
            }

            .body-content.sidebar-hidden {
                margin-left: 0;
            }

            .sidebar-overlay {
                display: none !important;
            }
        }

        @media (max-width: 991px) {
            .cards-grid {
                grid-template-columns: 1fr;
                padding: 0 1rem;
            }
            
            .welcome-title {
                font-size: 2rem;
            }
            
            .page-header {
                padding: 1rem;
            }
        }

        @media (min-width: 992px) and (max-width: 1200px) {
            .sidebar.auto-collapsed {
                width: 55px;
            }
            
            .sidebar.auto-collapsed .menu-text {
                display: none;
            }
            
            .sidebar.auto-collapsed .dropdown {
                display: none;
            }
            
            .body-content.auto-collapsed {
                margin-left: 55px;
            }
        }
    </style>
</head>
<body>
    <!-- Navbar -->
    <nav class="navbar">
        <div class="container-fluid d-flex align-items-center">
            <div class="d-flex align-items-center">
                <button class="btn fs-5 me-3" id="toggleSidebar">☰</button>
                <a class="navbar-brand p-0 pb-1" href="#">
                    <img src="https://abilityseminarsgroup.com/wp-content/uploads/2023/09/Asset-6-4-768x355.png" alt="Logo" style="width:130px;height:auto">
                </a>
            </div>

            <div class="ms-auto d-none d-md-flex align-items-center">
                <div class="dropdown me-3">
                    <i id="confi" class="bi bi-gear fs-5" data-bs-toggle="dropdown" role="button" aria-expanded="false" style="cursor: pointer;"></i>
                    <ul class="dropdown-menu dropdown-menu-end" aria-labelledby="confi">
                        <li><a class="dropdown-item" href="#"><i class="bi bi-person-circle fs-5 me-2"></i>Mi perfil</a></li>
                        <li><a class="dropdown-item" href="#"><i class="bi bi-shield-lock fs-5 me-2"></i>Contraseña</a></li>
                    </ul>
                </div>

                <figure class="m-0 me-4 ms-2 d-flex align-items-center">
                    <div class="avatar rounded-circle d-flex justify-content-center align-items-center text-light">
                        A
                    </div>
                    <p class="ms-2 mb-0 text-white">anthony sosa</p>
                    <i class="bi bi-chevron-down ms-2 text-white"></i>
                </figure>
            </div>
        </div>
    </nav>

    <!-- Sidebar Overlay -->
    <div class="sidebar-overlay" id="sidebarOverlay"></div>

    <!-- Sidebar -->
    <div id="sidebar" class="sidebar text-white">
        <div class="w-100 d-flex justify-content-end align-items-center" style="height: 50px; padding-top: 0.5rem;">
            <button id="toggleSidebar2" class="btn btn-sm text-white fs-6">☰</button>
        </div>
        
        <ul class="sidebar-nav list-unstyled">
            <li class="sidebar-parent active">
                <a href="#" class="d-block text-white text-decoration-none">
                    <i class="bi bi-house fs-6 me-2"></i><span class="menu-text">Dashboard</span>
                </a>
            </li>
            
            <li class="sidebar-parent">
                <a href="#" class="d-block text-white text-decoration-none">
                    <i class="bi bi-credit-card-2-back fs-6 me-2"></i><span class="menu-text">Sales<i class="ms-2 bi bi-chevron-down dropdown"></i></span>
                </a>
                <ul class="sidebar-children">
                    <li>
                        <a href="#" class="">
                            <i class="bi-cart-check fs-6 me-2"></i><span class="menu-text">Offers</span>
                        </a>
                    </li>
                    <li>
                        <a href="#" class="">
                            <i class="bi bi-credit-card-fill fs-6 me-2"></i><span class="menu-text">Payments</span>
                        </a>
                    </li>
                </ul>
            </li>
            
            <li class="sidebar-parent">
                <a href="#" class="d-block text-white text-decoration-none">
                    <i class="bi bi-envelope fs-6 me-2"></i><span class="menu-text">Marketing<i class="ms-2 bi bi-chevron-down dropdown"></i></span>
                </a>
                <ul class="sidebar-children">
                    <li>
                        <a href="#" class="">
                            <i class="bi bi-envelope-fill fs-6 me-2"></i><span class="menu-text">Email Campaigns</span>
                        </a>
                    </li>
                </ul>
            </li>
            
            <li class="sidebar-parent">
                <a href="#" class="d-block text-white text-decoration-none">
                    <i class="bi bi-book fs-6 me-2"></i><span class="menu-text">Courses<i class="ms-2 bi bi-chevron-down dropdown"></i></span>
                </a>
                <ul class="sidebar-children">
                    <li>
                        <a href="https://abilityseminarsgroup.com/new-course/" class="new-course-link">
                            <i class="bi bi-plus-circle fs-6 me-2"></i><span class="menu-text">New Courses</span>
                        </a>
                    </li>
                    <li>
                        <a href="https://abilityseminarsgroup.com/all-courses/" class="">
                            <i class="bi bi-box-fill fs-6 me-2"></i><span class="menu-text">All Courses</span>
                        </a>
                    </li>
                </ul>
            </li>
            
            <li class="sidebar-parent">
                <a href="#" class="d-block text-white text-decoration-none">
                    <i class="bi bi-people-fill fs-6 me-2"></i><span class="menu-text">Students<i class="ms-2 bi bi-chevron-down dropdown"></i></span>
                </a>
                <ul class="sidebar-children">
                    <li>
                        <a href="#" class="">
                            <i class="bi bi-people fs-6 me-2"></i><span class="menu-text">All Students</span>
                        </a>
                    </li>
                    <li>
                        <a href="#" class="">
                            <i class="bi bi-clipboard-data fs-6 me-2"></i><span class="menu-text">Student Progress</span>
                        </a>
                    </li>
                    <li>
                        <a href="#" class="">
                            <i class="bi bi-chat-text-fill fs-6 me-2"></i><span class="menu-text">Student Message</span>
                        </a>
                    </li>
                </ul>
            </li>
            
            <li class="sidebar-parent">
                <a href="#" class="d-block text-white text-decoration-none">
                    <i class="bi bi-bar-chart-fill fs-6 me-2"></i><span class="menu-text">Analytics</span>
                </a>
            </li>
            
            <li class="sidebar-parent">
                <a href="#" class="d-block text-white text-decoration-none">
                    <i class="bi bi-gear fs-6 me-2"></i><span class="menu-text">Setting<i class="ms-2 bi bi-chevron-down dropdown"></i></span>
                </a>
                <ul class="sidebar-children">
                    <li>
                        <a href="#" class="">
                            <i class="bi bi-person-fill-gear fs-6 me-2"></i><span class="menu-text">Manage Users</span>
                        </a>
                    </li>
                    <li>
                        <a href="#" class="">
                            <i class="bi-file-earmark-text fs-6 me-2"></i><span class="menu-text">Manage Courses</span>
                        </a>
                    </li>
                </ul>
            </li>
            
            <li class="sidebar-parent">
                <a href="#" class="d-block text-white text-decoration-none">
                    <i class="bi bi-box-arrow-left fs-6 me-2"></i><span class="menu-text">Cerrar sesión</span>
                </a>
            </li>
        </ul>
    </div>

    <!-- Main Content -->
    <section class="body-content">
        <header class="page-header d-flex p-2 align-items-center">
            <h5 class="mb-0 px-3 py-2 text-white">Home</h5>
        </header>
        
        <section class="main-content">
            <div class="welcome-section">
                <h2 class="welcome-title">Welcome back, anthony</h2>
                <p class="welcome-subtitle">Let's get started!</p>
            </div>

            <!-- Estadísticas del Dashboard -->
            <div class="stats-section mb-4">
                <div class="row">
                    <div class="col-md-3">
                        <div class="stat-card">
                            <div class="stat-icon">
                                <i class="bi bi-book"></i>
                            </div>
                            <div class="stat-info">
                                <h3 id="totalCourses">-</h3>
                                <p>Total Courses</p>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="stat-card">
                            <div class="stat-icon">
                                <i class="bi bi-check-circle"></i>
                            </div>
                            <div class="stat-info">
                                <h3 id="publishedCourses">-</h3>
                                <p>Published</p>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="stat-card">
                            <div class="stat-icon">
                                <i class="bi bi-pencil-square"></i>
                            </div>
                            <div class="stat-info">
                                <h3 id="draftCourses">-</h3>
                                <p>Drafts</p>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="stat-card">
                            <div class="stat-icon">
                                <i class="bi bi-currency-dollar"></i>
                            </div>
                            <div class="stat-info">
                                <h3 id="totalRevenue">-</h3>
                                <p>Total Revenue</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="cards-grid">
                <div class="dashboard-card" onclick="window.location.href='https://abilityseminarsgroup.com/new-course/'">
                    <div class="card-icon">
                        <i class="bi bi-book-open"></i>
                    </div>
                    <h3 class="card-title">Create New Course</h3>
                    <p class="card-description">Learn ways to increase your earnings.</p>
                </div>

                <div class="dashboard-card">
                    <div class="card-icon">
                        <i class="bi bi-graduation-cap"></i>
                    </div>
                    <h3 class="card-title">See students Progress</h3>
                    <p class="card-description">Find tips to spend money wisely.</p>
                </div>

                <div class="dashboard-card">
                    <div class="card-icon">
                        <i class="bi bi-bar-chart-line"></i>
                    </div>
                    <h3 class="card-title">See Course Analytics</h3>
                    <p class="card-description">Get control over your finances.</p>
                </div>

                <div class="dashboard-card">
                    <div class="card-icon">
                        <i class="bi bi-person-plus"></i>
                    </div>
                    <h3 class="card-title">Create New Users</h3>
                    <p class="card-description">Learn ways to increase your earnings.</p>
                </div>

                <div class="dashboard-card" onclick="window.location.href='https://abilityseminarsgroup.com/all-courses/'">
                    <div class="card-icon">
                        <i class="bi bi-grid-3x3-gap"></i>
                    </div>
                    <h3 class="card-title">All Courses</h3>
                    <p class="card-description">Find tips to spend money wisely.</p>
                </div>

                <div class="dashboard-card">
                    <div class="card-icon">
                        <i class="bi bi-briefcase"></i>
                    </div>
                    <h3 class="card-title">Manage Courses</h3>
                    <p class="card-description">Get control over your finances.</p>
                </div>
            </div>
        </section>
    </section>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.3/dist/js/bootstrap.bundle.min.js"></script>
    
    <script>
   
        // ========================================
        // VARIABLES GLOBALES
        // ========================================

        /**
         * Configuración del dashboard
         * @type {Object}
         */
        const DashboardConfig = {
            breakpoints: {
                mobile: 992,
                desktop: 1200
            },
            animations: {
                sidebarTransition: '0.3s ease',
                cardHover: '0.2s ease'
            }
        };

        // ========================================
        // INICIALIZACIÓN DEL SISTEMA
        // ========================================

        document.addEventListener('DOMContentLoaded', async function() {
            console.log('🚀 Iniciando Dashboard Principal...');

            // Elementos del DOM principales
            const sidebar = document.getElementById('sidebar');
            const sidebarOverlay = document.getElementById('sidebarOverlay');
            const bodyContent = document.querySelector('.body-content');
            const toggleSidebar = document.getElementById('toggleSidebar');
            const toggleSidebar2 = document.getElementById('toggleSidebar2');

            // Inicializar componentes del sistema
            DashboardSidebarManager.init(sidebar, sidebarOverlay, bodyContent, toggleSidebar, toggleSidebar2);
            DashboardNavigationManager.init();

            // Cargar estadísticas desde el backend
            await loadDashboardStats();

            console.log('✅ Dashboard inicializado correctamente');
        });

        /**
         * Cargar estadísticas del dashboard desde el backend
         */
        async function loadDashboardStats() {
            try {
                console.log('📊 Cargando estadísticas del dashboard...');

                // Mostrar loading en las estadísticas
                document.getElementById('totalCourses').textContent = '...';
                document.getElementById('publishedCourses').textContent = '...';
                document.getElementById('draftCourses').textContent = '...';
                document.getElementById('totalRevenue').textContent = '...';

                // Llamar al endpoint de estadísticas
                const response = await fetch('https://abilityseminarsgroup.com/wp-json/asg/v1/dashboard/stats');
                const result = await response.json();

                if (result.success && result.data) {
                    const stats = result.data;

                    // Actualizar estadísticas en la UI
                    document.getElementById('totalCourses').textContent = stats.total_courses || 0;
                    document.getElementById('publishedCourses').textContent = stats.published_courses || 0;
                    document.getElementById('draftCourses').textContent = stats.draft_courses || 0;
                    document.getElementById('totalRevenue').textContent = `$${(stats.total_revenue || 0).toFixed(2)}`;

                    console.log('✅ Estadísticas cargadas:', stats);

                } else {
                    throw new Error(result.error?.message || 'Error cargando estadísticas');
                }

            } catch (error) {
                console.error('❌ Error cargando estadísticas:', error);

                // Mostrar valores por defecto en caso de error
                document.getElementById('totalCourses').textContent = '0';
                document.getElementById('publishedCourses').textContent = '0';
                document.getElementById('draftCourses').textContent = '0';
                document.getElementById('totalRevenue').textContent = '$0.00';

                // Mostrar mensaje de error discreto
                showNotification('No se pudieron cargar las estadísticas', 'warning');
            }
        }

        /**
         * Mostrar notificación
         */
        function showNotification(message, type = 'info') {
            const notification = document.createElement('div');
            notification.className = `alert alert-${type} position-fixed`;
            notification.style.cssText = 'top: 20px; right: 20px; z-index: 9999; min-width: 300px;';
            notification.innerHTML = `
                <i class="bi bi-${type === 'success' ? 'check-circle' : type === 'warning' ? 'exclamation-triangle' : 'info-circle'} me-2"></i>
                ${message}
                <button type="button" class="btn-close" onclick="this.parentElement.remove()"></button>
            `;

            document.body.appendChild(notification);

            setTimeout(() => {
                if (notification.parentElement) {
                    notification.remove();
                }
            }, 5000);
        }

        // ========================================
        // GESTOR DEL SIDEBAR DEL DASHBOARD
        // ========================================

        /**
         * Maneja toda la funcionalidad del sidebar específica del dashboard
         */
        const DashboardSidebarManager = {

            /**
             * Inicializa el gestor del sidebar del dashboard
             * @param {HTMLElement} sidebar - Elemento del sidebar
             * @param {HTMLElement} overlay - Overlay para móvil
             * @param {HTMLElement} bodyContent - Contenido principal
             * @param {HTMLElement} toggleBtn1 - Botón toggle principal
             * @param {HTMLElement} toggleBtn2 - Botón toggle secundario
             */
            init(sidebar, overlay, bodyContent, toggleBtn1, toggleBtn2) {
                this.sidebar = sidebar;
                this.overlay = overlay;
                this.bodyContent = bodyContent;

                // Configurar eventos de toggle
                toggleBtn1.addEventListener('click', () => this.toggle());
                toggleBtn2.addEventListener('click', () => this.toggleDesktopOrCloseMobile());

                // Configurar overlay para cerrar en móvil
                overlay.addEventListener('click', () => this.closeMobile());

                console.log('✅ Dashboard Sidebar Manager inicializado');
            },

            /**
             * Alterna la visibilidad del sidebar según el dispositivo
             */
            toggle() {
                if (window.innerWidth >= DashboardConfig.breakpoints.mobile) {
                    // Comportamiento desktop
                    this.sidebar.classList.toggle('desktop-hidden');
                    this.bodyContent.classList.toggle('sidebar-hidden');
                    console.log('🖥️ Sidebar toggle - Desktop mode');
                } else {
                    // Comportamiento móvil
                    this.sidebar.classList.toggle('show');
                    this.overlay.classList.toggle('show');
                    console.log('📱 Sidebar toggle - Mobile mode');
                }
            },

            /**
             * Toggle en desktop o cierre en móvil (botón secundario)
             */
            toggleDesktopOrCloseMobile() {
                if (window.innerWidth >= DashboardConfig.breakpoints.mobile) {
                    // Desktop: usar la misma lógica que el botón principal
                    this.sidebar.classList.toggle('desktop-hidden');
                    this.bodyContent.classList.toggle('sidebar-hidden');
                    console.log('🖥️ Sidebar toggle secondary - Desktop mode');
                } else {
                    // Móvil: cerrar el sidebar
                    this.closeMobile();
                }
            },

            /**
             * Cierra el sidebar en modo móvil
             */
            closeMobile() {
                this.sidebar.classList.remove('show');
                this.overlay.classList.remove('show');
                console.log('📱 Sidebar cerrado - Mobile mode');
            }
        };

        // ========================================
        // GESTOR DE NAVEGACIÓN DEL DASHBOARD
        // ========================================

        /**
         * Maneja la navegación y menús del dashboard
         */
        const DashboardNavigationManager = {

            /**
             * Inicializa el gestor de navegación
             */
            init() {
                this.setupSubmenuToggles();
                this.setupNavigationClicks();
                console.log('✅ Dashboard Navigation Manager inicializado');
            },

            /**
             * Configura los toggles de submenús
             */
            setupSubmenuToggles() {
                const sidebarParents = document.querySelectorAll('.sidebar-parent');

                sidebarParents.forEach(parent => {
                    const link = parent.querySelector('a');
                    const hasChildren = parent.querySelector('.sidebar-children');

                    if (hasChildren) {
                        link.addEventListener('click', (e) => {
                            e.preventDefault();
                            parent.classList.toggle('expanded');
                            console.log('📂 Submenu toggle:', link.textContent.trim());
                        });
                    }
                });
            },

            /**
             * Configura los clics de navegación
             */
            setupNavigationClicks() {
                const sidebarParents = document.querySelectorAll('.sidebar-parent');
                const navLinks = document.querySelectorAll('.sidebar-parent > a');

                navLinks.forEach(link => {
                    link.addEventListener('click', function(e) {
                        const hasChildren = this.parentElement.querySelector('.sidebar-children');

                        if (!hasChildren) {
                            e.preventDefault();

                            // Remover clase active de todos los padres
                            sidebarParents.forEach(p => p.classList.remove('active'));

                            // Agregar clase active al padre clickeado
                            this.parentElement.classList.add('active');

                            // Cerrar sidebar en móvil después de selección
                            if (window.innerWidth < DashboardConfig.breakpoints.mobile) {
                                DashboardSidebarManager.closeMobile();
                            }

                            console.log('🎯 Navegación:', this.textContent.trim());
                        }
                    });
                });
            }
        };

            // Auto-collapse sidebar on medium screens
            function checkScreenSize() {
                const width = window.innerWidth;
                if (width >= 992 && width <= 1200) {
                    sidebar.classList.add('auto-collapsed');
                    bodyContent.classList.add('auto-collapsed');
                } else if (width > 1200) {
                    sidebar.classList.remove('auto-collapsed');
                    bodyContent.classList.remove('auto-collapsed');
                }
            }

            // Check screen size on load and resize
            checkScreenSize();
            window.addEventListener('resize', checkScreenSize);

        // ========================================
        // EVENTOS GLOBALES DEL DASHBOARD
        // ========================================

        /**
         * Maneja cambios de tamaño de ventana para responsive
         */
        window.addEventListener('resize', function() {
            if (window.innerWidth >= DashboardConfig.breakpoints.mobile) {
                // Modo desktop: limpiar clases de móvil
                const sidebar = document.getElementById('sidebar');
                const overlay = document.getElementById('sidebarOverlay');
                sidebar.classList.remove('show');
                overlay.classList.remove('show');
            } else {
                // Modo móvil: limpiar clases de desktop
                const sidebar = document.getElementById('sidebar');
                const bodyContent = document.querySelector('.body-content');
                sidebar.classList.remove('desktop-hidden');
                bodyContent.classList.remove('sidebar-hidden');
            }
        });

        // ========================================
        // FIN DEL DASHBOARD
        // ========================================

        console.log('🎉 Dashboard Principal cargado completamente');
    </script>
</body>
</html>