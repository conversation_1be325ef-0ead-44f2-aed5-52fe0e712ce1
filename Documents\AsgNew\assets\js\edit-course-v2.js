/**
 * ========================================
 * EDIT COURSE v2.0 - EDITOR AVANZADO
 * ========================================
 * 
 * Descripción: Editor completo de cursos con gestión visual
 * Incluye: Tabs, módulos, lecciones, objetivos, configuración
 * 
 * Autor: ASG Team
 * Fecha: 2025-01-04
 * Versión: 2.0.0 - EDITOR MODERNO
 */

/**
 * ========================================
 * CLASE PRINCIPAL DEL EDITOR DE CURSOS
 * ========================================
 */
class ASGEditCourse {
    constructor() {
        this.apiUrl = ASG.config.apiBaseUrl;
        this.courseId = null;
        this.courseData = null;
        this.currentTab = 'basic';
        this.hasUnsavedChanges = false;
        this.autoSaveInterval = null;
        
        // Elementos DOM
        this.elements = {};
        
        this.init();
    }
    
    init() {
        console.log('✏️ Inicializando Edit Course v2.0...');
        
        this.extractCourseId();
        this.cacheElements();
        this.setupEventListeners();
        this.loadCourse();
        this.setupAutoSave();
        
        console.log('✅ Edit Course inicializado correctamente');
    }
    
    extractCourseId() {
        const urlParams = new URLSearchParams(window.location.search);
        this.courseId = urlParams.get('id');
        
        if (!this.courseId) {
            this.showError('No se proporcionó un ID de curso válido');
            return;
        }
    }
    
    cacheElements() {
        this.elements = {
            // States
            loadingState: document.getElementById('loadingState'),
            courseEditor: document.getElementById('courseEditor'),
            errorState: document.getElementById('errorState'),
            
            // Header elements
            courseTitle: document.getElementById('courseTitle'),
            courseStatus: document.getElementById('courseStatus'),
            lastSaved: document.getElementById('lastSaved'),
            
            // Action buttons
            previewBtn: document.getElementById('previewBtn'),
            saveBtn: document.getElementById('saveBtn'),
            publishBtn: document.getElementById('publishBtn'),
            
            // Tabs
            tabButtons: document.querySelectorAll('.tab-btn'),
            tabPanels: document.querySelectorAll('.tab-panel'),
            
            // Basic info form fields
            editCourseName: document.getElementById('editCourseName'),
            editCourseDescription: document.getElementById('editCourseDescription'),
            editCourseCategory: document.getElementById('editCourseCategory'),
            editCoursePrice: document.getElementById('editCoursePrice'),
            editCourseLanguage: document.getElementById('editCourseLanguage'),
            editCourseImageInput: document.getElementById('editCourseImageInput'),
            courseImageContainer: document.querySelector('.course-image-container'),
            imageUploadPlaceholder: document.getElementById('imageUploadPlaceholder'),
            removeImageBtn: document.getElementById('removeImageBtn'),
            
            // Content elements
            addModuleBtn: document.getElementById('addModuleBtn'),
            modulesContainer: document.getElementById('modulesContainer'),
            
            // Objectives and benefits
            addObjectiveBtn: document.getElementById('addObjectiveBtn'),
            objectivesContainer: document.getElementById('objectivesContainer'),
            addBenefitBtn: document.getElementById('addBenefitBtn'),
            benefitsContainer: document.getElementById('benefitsContainer'),
            
            // Settings
            editCourseStatus: document.getElementById('editCourseStatus'),
            editCourseFeatured: document.getElementById('editCourseFeatured'),
            editCourseSeoTitle: document.getElementById('editCourseSeoTitle'),
            editCourseSeoDescription: document.getElementById('editCourseSeoDescription')
        };
    }
    
    setupEventListeners() {
        // Tab navigation
        this.elements.tabButtons.forEach(btn => {
            btn.addEventListener('click', (e) => {
                const tab = e.currentTarget.dataset.tab;
                this.switchTab(tab);
            });
        });
        
        // Action buttons
        if (this.elements.previewBtn) {
            this.elements.previewBtn.addEventListener('click', () => this.previewCourse());
        }
        
        if (this.elements.saveBtn) {
            this.elements.saveBtn.addEventListener('click', () => this.saveCourse());
        }
        
        if (this.elements.publishBtn) {
            this.elements.publishBtn.addEventListener('click', () => this.publishCourse());
        }
        
        // Form field listeners for change detection
        const formFields = [
            'editCourseName', 'editCourseDescription', 'editCourseCategory',
            'editCoursePrice', 'editCourseLanguage', 'editCourseStatus',
            'editCourseFeatured', 'editCourseSeoTitle', 'editCourseSeoDescription'
        ];
        
        formFields.forEach(fieldName => {
            const field = this.elements[fieldName];
            if (field) {
                field.addEventListener('input', () => this.markAsChanged());
                field.addEventListener('change', () => this.markAsChanged());
            }
        });
        
        // Image upload
        if (this.elements.editCourseImageInput) {
            this.elements.editCourseImageInput.addEventListener('change', (e) => {
                this.handleImageUpload(e.target.files[0]);
            });
        }
        
        if (this.elements.removeImageBtn) {
            this.elements.removeImageBtn.addEventListener('click', () => {
                this.removeImage();
            });
        }
        
        // Content management
        if (this.elements.addModuleBtn) {
            this.elements.addModuleBtn.addEventListener('click', () => this.addModule());
        }
        
        if (this.elements.addObjectiveBtn) {
            this.elements.addObjectiveBtn.addEventListener('click', () => this.addObjective());
        }
        
        if (this.elements.addBenefitBtn) {
            this.elements.addBenefitBtn.addEventListener('click', () => this.addBenefit());
        }
        
        // Prevent accidental navigation
        window.addEventListener('beforeunload', (e) => {
            if (this.hasUnsavedChanges) {
                e.preventDefault();
                e.returnValue = '';
            }
        });
    }
    
    async loadCourse() {
        try {
            this.showLoading();
            
            // Intentar cargar desde API real
            const response = await fetch(`${this.apiUrl}/admin/courses/${this.courseId}`);
            
            let courseData;
            if (response.ok) {
                const data = await response.json();
                courseData = data.data;
            } else {
                throw new Error('API no disponible');
            }
            
            this.courseData = courseData;
            this.populateForm();
            this.showEditor();
            
            ASG.notifications().success('Curso cargado correctamente');
            
        } catch (error) {
            console.warn('API no disponible, usando datos simulados:', error);
            this.courseData = this.generateMockCourseData();
            this.populateForm();
            this.showEditor();
            
            ASG.notifications().info('Mostrando datos de ejemplo (API no disponible)');
        }
    }
    
    generateMockCourseData() {
        // Datos simulados basados en el courseId
        const mockCourses = {
            'course_1': {
                course: {
                    id_course: 1,
                    code_course: 'course_1',
                    name_course: 'Como hacerte millonario?',
                    description_course: 'Aprende las estrategias más efectivas para generar riqueza y alcanzar la libertad financiera.',
                    cover_img: 'https://images.unsplash.com/photo-1579621970563-ebec7560ff3e?w=400&h=300&fit=crop',
                    price_course: 299.99,
                    status_course: 'published',
                    category_course: 'finanzas',
                    language_course: 'es',
                    featured_course: 1,
                    seo_title: 'Curso de Finanzas Personales - Hazte Millonario',
                    seo_description: 'Aprende estrategias probadas para generar riqueza y alcanzar la libertad financiera.',
                    created_at: '2024-12-15 10:30:00',
                    updated_at: '2024-12-20 15:45:00'
                },
                modules: [
                    {
                        id_modules: 1,
                        code_module: 'module_1_1',
                        title_module: 'Fundamentos de las Finanzas',
                        description_module: 'Conceptos básicos que todo millonario debe conocer',
                        duration_module: 120,
                        order_module: 0,
                        lessons: [
                            {
                                id_lesson: 1,
                                code_lesson: 'module_1_1_lesson_1',
                                title_lesson: 'Introducción a las finanzas personales',
                                description_lesson: 'Conceptos básicos y mentalidad millonaria',
                                duration_lesson: 30,
                                order_lesson: 0
                            }
                        ]
                    }
                ],
                objectives: [
                    {
                        id_learn: 1,
                        code_learn: 'objective_1_1',
                        name_list: 'Dominar los fundamentos de las finanzas personales',
                        order_learn: 0
                    },
                    {
                        id_learn: 2,
                        code_learn: 'objective_1_2',
                        name_list: 'Crear múltiples fuentes de ingresos',
                        order_learn: 1
                    }
                ],
                benefits: [
                    {
                        id_benefit: 1,
                        code_benefit: 'benefit_1_1',
                        name_list: 'Libertad financiera en menos tiempo',
                        order_benefit: 0
                    },
                    {
                        id_benefit: 2,
                        code_benefit: 'benefit_1_2',
                        name_list: 'Estrategias probadas por millonarios',
                        order_benefit: 1
                    }
                ]
            }
        };
        
        return mockCourses[this.courseId] || mockCourses['course_1'];
    }
    
    populateForm() {
        if (!this.courseData || !this.courseData.course) return;
        
        const course = this.courseData.course;
        
        // Update header
        if (this.elements.courseTitle) {
            this.elements.courseTitle.textContent = course.name_course;
        }
        
        if (this.elements.courseStatus) {
            this.updateStatusBadge(course.status_course);
        }
        
        if (this.elements.lastSaved) {
            this.elements.lastSaved.textContent = `Última modificación: ${ASG.utils.formatDate(course.updated_at)}`;
        }
        
        // Populate basic info fields
        if (this.elements.editCourseName) this.elements.editCourseName.value = course.name_course || '';
        if (this.elements.editCourseDescription) this.elements.editCourseDescription.value = course.description_course || '';
        if (this.elements.editCourseCategory) this.elements.editCourseCategory.value = course.category_course || '';
        if (this.elements.editCoursePrice) this.elements.editCoursePrice.value = course.price_course || '';
        if (this.elements.editCourseLanguage) this.elements.editCourseLanguage.value = course.language_course || 'es';
        
        // Settings
        if (this.elements.editCourseStatus) this.elements.editCourseStatus.value = course.status_course || 'draft';
        if (this.elements.editCourseFeatured) this.elements.editCourseFeatured.checked = course.featured_course == 1;
        if (this.elements.editCourseSeoTitle) this.elements.editCourseSeoTitle.value = course.seo_title || '';
        if (this.elements.editCourseSeoDescription) this.elements.editCourseSeoDescription.value = course.seo_description || '';
        
        // Load image
        if (course.cover_img) {
            this.displayImage(course.cover_img);
        }
        
        // Load modules
        this.renderModules();
        
        // Load objectives and benefits
        this.renderObjectives();
        this.renderBenefits();
        
        // Reset change flag
        this.hasUnsavedChanges = false;
    }
    
    updateStatusBadge(status) {
        if (!this.elements.courseStatus) return;
        
        const statusConfig = {
            published: { text: 'Publicado', class: 'asg-badge-success' },
            draft: { text: 'Borrador', class: 'asg-badge-warning' },
            archived: { text: 'Archivado', class: 'asg-badge-gray' }
        };
        
        const config = statusConfig[status] || statusConfig.draft;
        this.elements.courseStatus.textContent = config.text;
        this.elements.courseStatus.className = `asg-badge ${config.class}`;
    }
    
    displayImage(imageUrl) {
        if (this.elements.courseImageContainer) {
            this.elements.courseImageContainer.style.backgroundImage = `url(${imageUrl})`;
            this.elements.courseImageContainer.style.backgroundSize = 'cover';
            this.elements.courseImageContainer.style.backgroundPosition = 'center';
            
            if (this.elements.imageUploadPlaceholder) {
                this.elements.imageUploadPlaceholder.style.display = 'none';
            }
            
            if (this.elements.removeImageBtn) {
                this.elements.removeImageBtn.style.display = 'inline-flex';
            }
        }
    }
    
    removeImage() {
        if (this.elements.courseImageContainer) {
            this.elements.courseImageContainer.style.backgroundImage = '';
            
            if (this.elements.imageUploadPlaceholder) {
                this.elements.imageUploadPlaceholder.style.display = 'block';
            }
            
            if (this.elements.removeImageBtn) {
                this.elements.removeImageBtn.style.display = 'none';
            }
            
            if (this.elements.editCourseImageInput) {
                this.elements.editCourseImageInput.value = '';
            }
            
            this.markAsChanged();
        }
    }
    
    handleImageUpload(file) {
        if (!file) return;
        
        // Validate file
        if (file.size > 5 * 1024 * 1024) {
            ASG.notifications().error('La imagen no puede superar los 5MB');
            return;
        }
        
        if (!file.type.startsWith('image/')) {
            ASG.notifications().error('Solo se permiten archivos de imagen');
            return;
        }
        
        const reader = new FileReader();
        reader.onload = (e) => {
            this.displayImage(e.target.result);
            this.markAsChanged();
        };
        reader.readAsDataURL(file);
    }
    
    switchTab(tabName) {
        if (tabName === this.currentTab) return;
        
        this.currentTab = tabName;
        
        // Update tab buttons
        this.elements.tabButtons.forEach(btn => {
            if (btn.dataset.tab === tabName) {
                btn.classList.add('active');
                btn.style.color = 'var(--asg-primary-600)';
                btn.style.borderBottomColor = 'var(--asg-primary-600)';
            } else {
                btn.classList.remove('active');
                btn.style.color = 'var(--asg-gray-600)';
                btn.style.borderBottomColor = 'transparent';
            }
        });
        
        // Update tab panels
        this.elements.tabPanels.forEach(panel => {
            if (panel.dataset.tab === tabName) {
                panel.style.display = 'block';
                panel.classList.add('active');
            } else {
                panel.style.display = 'none';
                panel.classList.remove('active');
            }
        });
    }
    
    renderModules() {
        if (!this.elements.modulesContainer) return;
        
        const modules = this.courseData.modules || [];
        
        if (modules.length === 0) {
            this.elements.modulesContainer.innerHTML = `
                <div style="text-align: center; padding: var(--asg-space-8); color: var(--asg-gray-500);">
                    <i class="bi bi-collection" style="font-size: 3rem; margin-bottom: var(--asg-space-4);"></i>
                    <p style="margin: 0; font-size: var(--asg-text-lg); font-weight: var(--asg-font-medium);">
                        No hay módulos creados
                    </p>
                    <p style="margin: var(--asg-space-2) 0 0 0; font-size: var(--asg-text-sm);">
                        Agrega tu primer módulo para comenzar a estructurar el curso
                    </p>
                </div>
            `;
            return;
        }
        
        this.elements.modulesContainer.innerHTML = modules.map((module, index) => `
            <div class="module-item asg-card" style="margin-bottom: var(--asg-space-4);">
                <div class="asg-card-header">
                    <div style="display: flex; align-items: center; justify-content: space-between;">
                        <div style="display: flex; align-items: center; gap: var(--asg-space-3);">
                            <span style="
                                width: 2rem; 
                                height: 2rem; 
                                background: var(--asg-primary-100); 
                                color: var(--asg-primary-600); 
                                border-radius: 50%; 
                                display: flex; 
                                align-items: center; 
                                justify-content: center; 
                                font-weight: var(--asg-font-semibold);
                                font-size: var(--asg-text-sm);
                            ">${index + 1}</span>
                            <h4 style="margin: 0; font-size: var(--asg-text-base); font-weight: var(--asg-font-semibold);">
                                ${module.title_module}
                            </h4>
                        </div>
                        <div style="display: flex; gap: var(--asg-space-2);">
                            <button class="asg-btn asg-btn-sm asg-btn-secondary" onclick="editCourse.editModule('${module.code_module}')">
                                <i class="bi bi-pencil"></i>
                            </button>
                            <button class="asg-btn asg-btn-sm asg-btn-secondary" onclick="editCourse.deleteModule('${module.code_module}')">
                                <i class="bi bi-trash"></i>
                            </button>
                        </div>
                    </div>
                </div>
                <div class="asg-card-body">
                    <p style="margin: 0 0 var(--asg-space-3) 0; color: var(--asg-gray-600);">
                        ${module.description_module || 'Sin descripción'}
                    </p>
                    <div style="display: flex; align-items: center; gap: var(--asg-space-4); font-size: var(--asg-text-sm); color: var(--asg-gray-500);">
                        <span><i class="bi bi-clock"></i> ${module.duration_module || 0} min</span>
                        <span><i class="bi bi-play-circle"></i> ${module.lessons?.length || 0} lecciones</span>
                    </div>
                </div>
            </div>
        `).join('');
    }
    
    renderObjectives() {
        if (!this.elements.objectivesContainer) return;
        
        const objectives = this.courseData.objectives || [];
        
        if (objectives.length === 0) {
            this.elements.objectivesContainer.innerHTML = `
                <p style="text-align: center; color: var(--asg-gray-500); padding: var(--asg-space-6);">
                    No hay objetivos definidos
                </p>
            `;
            return;
        }
        
        this.elements.objectivesContainer.innerHTML = objectives.map(objective => `
            <div class="objective-item" style="
                display: flex; 
                align-items: center; 
                gap: var(--asg-space-3); 
                padding: var(--asg-space-3); 
                border: 1px solid var(--asg-gray-200); 
                border-radius: var(--asg-radius); 
                margin-bottom: var(--asg-space-2);
            ">
                <i class="bi bi-check-circle" style="color: var(--asg-success); flex-shrink: 0;"></i>
                <span style="flex: 1;">${objective.name_list}</span>
                <button class="asg-btn asg-btn-sm asg-btn-secondary" onclick="editCourse.deleteObjective('${objective.code_learn}')" style="padding: var(--asg-space-1);">
                    <i class="bi bi-trash"></i>
                </button>
            </div>
        `).join('');
    }
    
    renderBenefits() {
        if (!this.elements.benefitsContainer) return;
        
        const benefits = this.courseData.benefits || [];
        
        if (benefits.length === 0) {
            this.elements.benefitsContainer.innerHTML = `
                <p style="text-align: center; color: var(--asg-gray-500); padding: var(--asg-space-6);">
                    No hay beneficios definidos
                </p>
            `;
            return;
        }
        
        this.elements.benefitsContainer.innerHTML = benefits.map(benefit => `
            <div class="benefit-item" style="
                display: flex; 
                align-items: center; 
                gap: var(--asg-space-3); 
                padding: var(--asg-space-3); 
                border: 1px solid var(--asg-gray-200); 
                border-radius: var(--asg-radius); 
                margin-bottom: var(--asg-space-2);
            ">
                <i class="bi bi-star-fill" style="color: var(--asg-warning); flex-shrink: 0;"></i>
                <span style="flex: 1;">${benefit.name_list}</span>
                <button class="asg-btn asg-btn-sm asg-btn-secondary" onclick="editCourse.deleteBenefit('${benefit.code_benefit}')" style="padding: var(--asg-space-1);">
                    <i class="bi bi-trash"></i>
                </button>
            </div>
        `).join('');
    }

    // Action methods
    markAsChanged() {
        this.hasUnsavedChanges = true;

        // Update save button to indicate changes
        if (this.elements.saveBtn) {
            this.elements.saveBtn.innerHTML = '<i class="bi bi-save"></i> <span class="btn-text">Guardar *</span>';
            this.elements.saveBtn.classList.add('asg-btn-warning');
            this.elements.saveBtn.classList.remove('asg-btn-secondary');
        }
    }

    setupAutoSave() {
        // Auto-save every 2 minutes
        this.autoSaveInterval = setInterval(() => {
            if (this.hasUnsavedChanges) {
                this.autoSave();
            }
        }, 120000);
    }

    autoSave() {
        console.log('💾 Auto-guardado...');
        // In a real implementation, this would save to the server
        // For now, just save to localStorage
        localStorage.setItem(`asg_course_edit_${this.courseId}`, JSON.stringify({
            data: this.collectFormData(),
            timestamp: new Date().toISOString()
        }));
    }

    collectFormData() {
        return {
            name_course: this.elements.editCourseName?.value || '',
            description_course: this.elements.editCourseDescription?.value || '',
            category_course: this.elements.editCourseCategory?.value || '',
            price_course: parseFloat(this.elements.editCoursePrice?.value) || 0,
            language_course: this.elements.editCourseLanguage?.value || 'es',
            status_course: this.elements.editCourseStatus?.value || 'draft',
            featured_course: this.elements.editCourseFeatured?.checked ? 1 : 0,
            seo_title: this.elements.editCourseSeoTitle?.value || '',
            seo_description: this.elements.editCourseSeoDescription?.value || ''
        };
    }

    async saveCourse() {
        try {
            ASG.loading().show('save-course');

            const formData = this.collectFormData();

            // Simulate API call
            await new Promise(resolve => setTimeout(resolve, 1000));

            // Update course data
            Object.assign(this.courseData.course, formData);
            this.courseData.course.updated_at = new Date().toISOString();

            // Update UI
            this.elements.courseTitle.textContent = formData.name_course;
            this.updateStatusBadge(formData.status_course);
            this.elements.lastSaved.textContent = `Última modificación: ${ASG.utils.formatDate(this.courseData.course.updated_at)}`;

            // Reset change indicators
            this.hasUnsavedChanges = false;
            if (this.elements.saveBtn) {
                this.elements.saveBtn.innerHTML = '<i class="bi bi-save"></i> <span class="btn-text">Guardar</span>';
                this.elements.saveBtn.classList.remove('asg-btn-warning');
                this.elements.saveBtn.classList.add('asg-btn-secondary');
            }

            ASG.notifications().success('Curso guardado correctamente');

        } catch (error) {
            console.error('Error saving course:', error);
            ASG.notifications().error('Error al guardar el curso');
        } finally {
            ASG.loading().hide('save-course');
        }
    }

    async publishCourse() {
        const formData = this.collectFormData();

        if (!formData.name_course.trim()) {
            ASG.notifications().error('El título del curso es obligatorio');
            this.switchTab('basic');
            this.elements.editCourseName?.focus();
            return;
        }

        if (!formData.description_course.trim()) {
            ASG.notifications().error('La descripción del curso es obligatoria');
            this.switchTab('basic');
            this.elements.editCourseDescription?.focus();
            return;
        }

        if (!formData.category_course) {
            ASG.notifications().error('Debes seleccionar una categoría');
            this.switchTab('basic');
            this.elements.editCourseCategory?.focus();
            return;
        }

        if (confirm('¿Estás seguro de que quieres publicar este curso?')) {
            try {
                ASG.loading().show('publish-course');

                // Set status to published
                formData.status_course = 'published';
                if (this.elements.editCourseStatus) {
                    this.elements.editCourseStatus.value = 'published';
                }

                // Save the course
                await this.saveCourse();

                ASG.notifications().success('¡Curso publicado exitosamente!');

            } catch (error) {
                console.error('Error publishing course:', error);
                ASG.notifications().error('Error al publicar el curso');
            } finally {
                ASG.loading().hide('publish-course');
            }
        }
    }

    previewCourse() {
        const previewUrl = `/course?id=${this.courseId}`;
        window.open(previewUrl, '_blank');
    }

    // Content management methods
    addModule() {
        const title = prompt('Título del módulo:');
        if (!title) return;

        const description = prompt('Descripción del módulo (opcional):') || '';

        const newModule = {
            id_modules: Date.now(),
            code_module: `module_${this.courseId.split('_')[1]}_${Date.now()}`,
            title_module: title,
            description_module: description,
            duration_module: 0,
            order_module: this.courseData.modules?.length || 0,
            lessons: []
        };

        if (!this.courseData.modules) {
            this.courseData.modules = [];
        }

        this.courseData.modules.push(newModule);
        this.renderModules();
        this.markAsChanged();

        ASG.notifications().success('Módulo agregado correctamente');
    }

    editModule(moduleCode) {
        const module = this.courseData.modules?.find(m => m.code_module === moduleCode);
        if (!module) return;

        const newTitle = prompt('Título del módulo:', module.title_module);
        if (newTitle === null) return;

        const newDescription = prompt('Descripción del módulo:', module.description_module || '');
        if (newDescription === null) return;

        module.title_module = newTitle;
        module.description_module = newDescription;

        this.renderModules();
        this.markAsChanged();

        ASG.notifications().success('Módulo actualizado correctamente');
    }

    deleteModule(moduleCode) {
        const module = this.courseData.modules?.find(m => m.code_module === moduleCode);
        if (!module) return;

        if (confirm(`¿Estás seguro de que quieres eliminar el módulo "${module.title_module}"?`)) {
            this.courseData.modules = this.courseData.modules.filter(m => m.code_module !== moduleCode);
            this.renderModules();
            this.markAsChanged();

            ASG.notifications().success('Módulo eliminado correctamente');
        }
    }

    addObjective() {
        const objective = prompt('Objetivo de aprendizaje:');
        if (!objective) return;

        const newObjective = {
            id_learn: Date.now(),
            code_learn: `objective_${this.courseId.split('_')[1]}_${Date.now()}`,
            name_list: objective,
            order_learn: this.courseData.objectives?.length || 0
        };

        if (!this.courseData.objectives) {
            this.courseData.objectives = [];
        }

        this.courseData.objectives.push(newObjective);
        this.renderObjectives();
        this.markAsChanged();

        ASG.notifications().success('Objetivo agregado correctamente');
    }

    deleteObjective(objectiveCode) {
        const objective = this.courseData.objectives?.find(o => o.code_learn === objectiveCode);
        if (!objective) return;

        if (confirm(`¿Estás seguro de que quieres eliminar este objetivo?`)) {
            this.courseData.objectives = this.courseData.objectives.filter(o => o.code_learn !== objectiveCode);
            this.renderObjectives();
            this.markAsChanged();

            ASG.notifications().success('Objetivo eliminado correctamente');
        }
    }

    addBenefit() {
        const benefit = prompt('Beneficio del curso:');
        if (!benefit) return;

        const newBenefit = {
            id_benefit: Date.now(),
            code_benefit: `benefit_${this.courseId.split('_')[1]}_${Date.now()}`,
            name_list: benefit,
            order_benefit: this.courseData.benefits?.length || 0
        };

        if (!this.courseData.benefits) {
            this.courseData.benefits = [];
        }

        this.courseData.benefits.push(newBenefit);
        this.renderBenefits();
        this.markAsChanged();

        ASG.notifications().success('Beneficio agregado correctamente');
    }

    deleteBenefit(benefitCode) {
        const benefit = this.courseData.benefits?.find(b => b.code_benefit === benefitCode);
        if (!benefit) return;

        if (confirm(`¿Estás seguro de que quieres eliminar este beneficio?`)) {
            this.courseData.benefits = this.courseData.benefits.filter(b => b.code_benefit !== benefitCode);
            this.renderBenefits();
            this.markAsChanged();

            ASG.notifications().success('Beneficio eliminado correctamente');
        }
    }

    // State management
    showLoading() {
        this.elements.loadingState.style.display = 'block';
        this.elements.courseEditor.style.display = 'none';
        this.elements.errorState.style.display = 'none';
    }

    showEditor() {
        this.elements.loadingState.style.display = 'none';
        this.elements.courseEditor.style.display = 'block';
        this.elements.errorState.style.display = 'none';
    }

    showError(message) {
        this.elements.loadingState.style.display = 'none';
        this.elements.courseEditor.style.display = 'none';
        this.elements.errorState.style.display = 'block';

        ASG.notifications().error(message);
    }

    // Cleanup
    destroy() {
        if (this.autoSaveInterval) {
            clearInterval(this.autoSaveInterval);
        }
    }
}

/**
 * ========================================
 * INICIALIZACIÓN
 * ========================================
 */
let editCourse = null;

document.addEventListener('DOMContentLoaded', function() {
    // Esperar a que los componentes base estén listos
    setTimeout(() => {
        editCourse = new ASGEditCourse();

        // Hacer disponible globalmente
        window.editCourse = editCourse;
    }, 100);
});

// Cleanup on page unload
window.addEventListener('beforeunload', () => {
    if (editCourse) {
        editCourse.destroy();
    }
});
