/**
 * ========================================
 * ENDPOINTS API - SISTEMA DE CURSOS ASG
 * ========================================
 * 
 * Descripción: Endpoints REST para la gestión completa de cursos
 * de AbilitySeminarsGroup. Incluye creación, edición, listado y
 * gestión de imágenes.
 * 
 * Estructura: Router + Functions en un solo archivo
 * Base de datos: vhbnlkte_wp803 (prefijo: wpic_)
 * 
 * Autor: ASG Team
 * Fecha: 2025-01-03
 * Versión: 1.0.2 - FIXED FOR EXISTING TABLES
 */

// Prevenir acceso directo
if (!defined('ABSPATH')) {
    exit;
}

/**
 * ========================================
 * REGISTRO DE POST TYPE Y TABLAS
 * ========================================
 */

// Registrar post type 'course'
add_action('init', function() {
    register_post_type('course', array(
        'labels' => array(
            'name' => 'Cursos',
            'singular_name' => 'Curso'
        ),
        'public' => true,
        'has_archive' => true,
        'supports' => array('title', 'editor', 'thumbnail'),
        'show_in_rest' => true
    ));
});

/**
 * ========================================
 * REGISTRO DE RUTAS (ROUTER)
 * ========================================
 */
add_action('rest_api_init', function () {
    
    // ===== NEW COURSE ENDPOINTS =====
    
    // Crear nuevo curso (PÚBLICO)
    register_rest_route('asg/v1', '/courses', array(
        'methods' => 'POST',
        'callback' => 'asg_create_course',
        'permission_callback' => '__return_true', // Hacer público
    ));
    
    // Guardar borrador
    register_rest_route('asg/v1', '/courses/draft', array(
        'methods' => 'POST',
        'callback' => 'asg_save_draft',
        'permission_callback' => 'asg_check_permissions',
    ));
    
    // Subir imagen de curso (PÚBLICO)
    register_rest_route('asg/v1', '/courses/upload-image', array(
        'methods' => 'POST',
        'callback' => 'asg_upload_course_image',
        'permission_callback' => '__return_true', // Hacer público
    ));
    
    // Obtener categorías disponibles (PÚBLICO - sin autenticación)
    register_rest_route('asg/v1', '/courses/categories', array(
        'methods' => 'GET',
        'callback' => 'asg_get_categories',
        'permission_callback' => '__return_true', // Hacer público
    ));
    
    // Validar datos de curso
    register_rest_route('asg/v1', '/courses/validate', array(
        'methods' => 'POST',
        'callback' => 'asg_validate_course',
        'permission_callback' => 'asg_check_permissions',
    ));
    
    // ===== ALL COURSES ENDPOINTS =====
    
    // Listar todos los cursos (PÚBLICO)
    register_rest_route('asg/v1', '/courses', array(
        'methods' => 'GET',
        'callback' => 'asg_get_all_courses',
        'permission_callback' => '__return_true', // Hacer público
    ));
    
    // Buscar cursos
    register_rest_route('asg/v1', '/courses/search', array(
        'methods' => 'GET',
        'callback' => 'asg_search_courses',
        'permission_callback' => 'asg_check_permissions',
    ));
    
    // Eliminar curso
    register_rest_route('asg/v1', '/courses/(?P<id>\d+)', array(
        'methods' => 'DELETE',
        'callback' => 'asg_delete_course',
        'permission_callback' => 'asg_check_permissions',
    ));
    
    // ===== EDIT COURSE ENDPOINTS =====
    
    // Obtener curso específico (PÚBLICO)
    register_rest_route('asg/v1', '/courses/(?P<id>\d+)', array(
        'methods' => 'GET',
        'callback' => 'asg_get_course',
        'permission_callback' => '__return_true', // Hacer público
    ));
    
    // Actualizar curso completo
    register_rest_route('asg/v1', '/courses/(?P<id>\d+)', array(
        'methods' => 'PUT',
        'callback' => 'asg_update_course',
        'permission_callback' => 'asg_check_permissions',
    ));
    
    // ===== DASHBOARD ENDPOINTS =====

    // Estadísticas del dashboard (PÚBLICO)
    register_rest_route('asg/v1', '/dashboard/stats', array(
        'methods' => 'GET',
        'callback' => 'asg_get_dashboard_stats',
        'permission_callback' => '__return_true', // Hacer público
    ));

    // Información del sistema (PÚBLICO)
    register_rest_route('asg/v1', '/info', array(
        'methods' => 'GET',
        'callback' => 'asg_get_system_info',
        'permission_callback' => '__return_true', // Hacer público
    ));

    // ===== FASE 2: ALL COURSES - ENDPOINTS IMPORTANTES =====

    // Filtrar cursos por múltiples criterios
    register_rest_route('asg/v1', '/courses/filter', array(
        'methods' => 'GET',
        'callback' => 'asg_filter_courses',
        'permission_callback' => '__return_true',
    ));

    // Obtener cursos paginados
    register_rest_route('asg/v1', '/courses/paginated', array(
        'methods' => 'GET',
        'callback' => 'asg_get_courses_paginated',
        'permission_callback' => '__return_true',
    ));

    // Cambiar estado de múltiples cursos (bulk action)
    register_rest_route('asg/v1', '/courses/bulk-status', array(
        'methods' => 'PUT',
        'callback' => 'asg_bulk_update_status',
        'permission_callback' => 'asg_check_permissions',
    ));

    // Eliminar múltiples cursos (bulk delete)
    register_rest_route('asg/v1', '/courses/bulk-delete', array(
        'methods' => 'DELETE',
        'callback' => 'asg_bulk_delete_courses',
        'permission_callback' => 'asg_check_permissions',
    ));

    // ===== FASE 3: EDIT COURSE - ENDPOINTS AVANZADOS =====

    // Gestión específica de módulos
    register_rest_route('asg/v1', '/courses/(?P<id>\d+)/modules', array(
        'methods' => 'GET',
        'callback' => 'asg_get_course_modules_only',
        'permission_callback' => '__return_true',
    ));

    register_rest_route('asg/v1', '/courses/(?P<id>\d+)/modules', array(
        'methods' => 'PUT',
        'callback' => 'asg_update_course_modules_only',
        'permission_callback' => 'asg_check_permissions',
    ));

    // Agregar un módulo específico
    register_rest_route('asg/v1', '/courses/(?P<id>\d+)/modules', array(
        'methods' => 'POST',
        'callback' => 'asg_add_course_module',
        'permission_callback' => 'asg_check_permissions',
    ));

    // Eliminar un módulo específico
    register_rest_route('asg/v1', '/courses/(?P<course_id>\d+)/modules/(?P<module_id>\d+)', array(
        'methods' => 'DELETE',
        'callback' => 'asg_delete_course_module',
        'permission_callback' => 'asg_check_permissions',
    ));

    // Reordenar módulos
    register_rest_route('asg/v1', '/courses/(?P<id>\d+)/modules/reorder', array(
        'methods' => 'PUT',
        'callback' => 'asg_reorder_course_modules',
        'permission_callback' => 'asg_check_permissions',
    ));

    // Gestión de objetivos de aprendizaje
    register_rest_route('asg/v1', '/courses/(?P<id>\d+)/objectives', array(
        'methods' => 'PUT',
        'callback' => 'asg_update_course_objectives',
        'permission_callback' => 'asg_check_permissions',
    ));

    // Gestión de beneficios
    register_rest_route('asg/v1', '/courses/(?P<id>\d+)/benefits', array(
        'methods' => 'PUT',
        'callback' => 'asg_update_course_benefits',
        'permission_callback' => 'asg_check_permissions',
    ));

    // Duplicar curso
    register_rest_route('asg/v1', '/courses/(?P<id>\d+)/duplicate', array(
        'methods' => 'POST',
        'callback' => 'asg_duplicate_course',
        'permission_callback' => 'asg_check_permissions',
    ));

    // Cambiar estado específico de un curso
    register_rest_route('asg/v1', '/courses/(?P<id>\d+)/status', array(
        'methods' => 'PUT',
        'callback' => 'asg_update_course_status',
        'permission_callback' => 'asg_check_permissions',
    ));

    // Obtener historial de cambios del curso
    register_rest_route('asg/v1', '/courses/(?P<id>\d+)/history', array(
        'methods' => 'GET',
        'callback' => 'asg_get_course_history',
        'permission_callback' => 'asg_check_permissions',
    ));
});

/**
 * ========================================
 * FUNCIONES DE ENDPOINTS
 * ========================================
 */

/**
 * Crear nuevo curso
 * 
 * @param WP_REST_Request $request
 * @return WP_REST_Response
 */
function asg_create_course($request) {
    global $wpdb;
    
    try {
        // Log de inicio
        error_log('ASG: Iniciando creación de curso');
        
        // Obtener datos del request
        $data = $request->get_json_params();
        
        if (empty($data)) {
            $data = $request->get_params();
        }
        
        error_log('ASG: Datos recibidos: ' . print_r($data, true));
        
        // Validar datos requeridos
        $required_fields = ['title', 'description', 'category'];
        foreach ($required_fields as $field) {
            if (empty($data[$field])) {
                return new WP_REST_Response(array(
                    'success' => false,
                    'error' => array(
                        'code' => 'missing_field',
                        'message' => "Field '{$field}' is required"
                    )
                ), 400);
            }
        }
        
        // Iniciar transacción
        $wpdb->query('START TRANSACTION');

        // Generar código único del curso
        $course_code = 'course_' . strtolower(str_replace(' ', '_', sanitize_text_field($data['title']))) . '_' . time();

        error_log('ASG: Código de curso generado: ' . $course_code);

        // Insertar directamente en tabla de cursos (sin WordPress posts)
        $table_courses = $wpdb->prefix . 'courses';
        
        $course_data = array(
            'post_id' => $post_id,
            'price' => floatval($data['price'] ?? 0),
            'category' => sanitize_text_field($data['category']),
            'duration' => floatval($data['duration'] ?? 0),
            'language' => sanitize_text_field($data['language'] ?? 'es'),
            'status' => 'published',
            'created_at' => current_time('mysql'),
            'updated_at' => current_time('mysql')
        );
        
        $result = $wpdb->insert(
            $table_courses,
            $course_data,
            array('%d', '%f', '%s', '%f', '%s', '%s', '%s', '%s')
        );
        
        if ($result === false) {
            throw new Exception('Error inserting course data: ' . $wpdb->last_error);
        }
        
        $course_id = $wpdb->insert_id;
        error_log('ASG: Curso creado con ID: ' . $course_id);
        
        // 3. Insertar módulos si existen
        if (!empty($data['modules']) && is_array($data['modules'])) {
            asg_insert_course_modules($course_id, $data['modules']);
        }
        
        // 4. Insertar objetivos de aprendizaje si existen
        if (!empty($data['learn_objectives']) && is_array($data['learn_objectives'])) {
            asg_insert_learn_objectives($course_id, $data['learn_objectives']);
        }
        
        // 5. Insertar beneficios si existen
        if (!empty($data['benefits']) && is_array($data['benefits'])) {
            asg_insert_course_benefits($course_id, $data['benefits']);
        }
        
        // 6. Establecer imagen destacada si existe
        if (!empty($data['featured_image_url'])) {
            // Aquí podrías crear un attachment y establecerlo como featured image
            update_post_meta($post_id, '_course_featured_image', esc_url_raw($data['featured_image_url']));
        }
        
        // Confirmar transacción
        $wpdb->query('COMMIT');
        
        // Obtener el curso completo creado
        $created_course = asg_get_course_by_id($course_id);
        
        error_log('ASG: Curso creado exitosamente - ID: ' . $course_id);
        
        return new WP_REST_Response(array(
            'success' => true,
            'message' => 'Course created successfully',
            'data' => $created_course
        ), 201);
        
    } catch (Exception $e) {
        // Rollback en caso de error
        $wpdb->query('ROLLBACK');
        
        error_log('ASG: Error creando curso: ' . $e->getMessage());
        error_log('ASG: Stack trace: ' . $e->getTraceAsString());
        
        return new WP_REST_Response(array(
            'success' => false,
            'error' => array(
                'code' => 'server_error',
                'message' => 'Internal server error: ' . $e->getMessage()
            )
        ), 500);
    }
}

/**
 * Guardar curso como borrador
 * 
 * @param WP_REST_Request $request
 * @return WP_REST_Response
 */
function asg_save_draft($request) {
    global $wpdb;
    
    try {
        error_log('ASG: Guardando borrador');
        
        $data = $request->get_json_params();
        if (empty($data)) {
            $data = $request->get_params();
        }
        
        // Validar título mínimo para borrador
        if (empty($data['title'])) {
            return new WP_REST_Response(array(
                'success' => false,
                'error' => array(
                    'code' => 'missing_title',
                    'message' => 'Title is required for draft'
                )
            ), 400);
        }
        
        // Iniciar transacción
        $wpdb->query('START TRANSACTION');
        
        // 1. Crear post como borrador
        $post_data = array(
            'post_title' => sanitize_text_field($data['title']),
            'post_content' => sanitize_textarea_field($data['description'] ?? ''),
            'post_status' => 'draft',
            'post_type' => 'course',
            'post_author' => get_current_user_id() ?: 1
        );
        
        $post_id = wp_insert_post($post_data);
        
        if (is_wp_error($post_id)) {
            throw new Exception('Error creating draft post: ' . $post_id->get_error_message());
        }
        
        // 2. Insertar en tabla de cursos como borrador
        $table_courses = $wpdb->prefix . 'courses';
        
        $course_data = array(
            'post_id' => $post_id,
            'price' => floatval($data['price'] ?? 0),
            'category' => sanitize_text_field($data['category'] ?? 'business'),
            'duration' => floatval($data['duration'] ?? 0),
            'language' => sanitize_text_field($data['language'] ?? 'es'),
            'status' => 'draft',
            'created_at' => current_time('mysql'),
            'updated_at' => current_time('mysql')
        );
        
        $result = $wpdb->insert(
            $table_courses,
            $course_data,
            array('%d', '%f', '%s', '%f', '%s', '%s', '%s', '%s')
        );
        
        if ($result === false) {
            throw new Exception('Error inserting draft data: ' . $wpdb->last_error);
        }
        
        $course_id = $wpdb->insert_id;
        
        // Confirmar transacción
        $wpdb->query('COMMIT');
        
        return new WP_REST_Response(array(
            'success' => true,
            'message' => 'Draft saved successfully',
            'data' => array(
                'course_id' => $course_id,
                'post_id' => $post_id
            )
        ), 201);
        
    } catch (Exception $e) {
        $wpdb->query('ROLLBACK');
        
        error_log('ASG: Error guardando borrador: ' . $e->getMessage());
        
        return new WP_REST_Response(array(
            'success' => false,
            'error' => array(
                'code' => 'server_error',
                'message' => 'Internal server error: ' . $e->getMessage()
            )
        ), 500);
    }
}

/**
 * Subir imagen de curso
 *
 * @param WP_REST_Request $request
 * @return WP_REST_Response
 */
function asg_upload_course_image($request) {
    try {
        error_log('ASG: Iniciando subida de imagen');
        
        // Verificar si se subió un archivo
        $files = $request->get_file_params();
        if (empty($files['image'])) {
            return new WP_REST_Response(array(
                'success' => false,
                'error' => array(
                    'code' => 'no_file',
                    'message' => 'No image file provided'
                )
            ), 400);
        }
        
        $file = $files['image'];
        error_log('ASG: Archivo recibido: ' . print_r($file, true));
        
        // Validar tipo de archivo
        $allowed_types = array('image/jpeg', 'image/jpg', 'image/png', 'image/webp');
        if (!in_array($file['type'], $allowed_types)) {
            return new WP_REST_Response(array(
                'success' => false,
                'error' => array(
                    'code' => 'invalid_file_type',
                    'message' => 'Only JPG, PNG and WebP images are allowed'
                )
            ), 400);
        }
        
        // Validar tamaño (5MB máximo)
        if ($file['size'] > 5 * 1024 * 1024) {
            return new WP_REST_Response(array(
                'success' => false,
                'error' => array(
                    'code' => 'file_too_large',
                    'message' => 'File size must be less than 5MB'
                )
            ), 400);
        }
        
        // Configurar WordPress para manejar la subida
        if (!function_exists('wp_handle_upload')) {
            require_once(ABSPATH . 'wp-admin/includes/file.php');
        }
        
        // Configurar la subida
        $upload_overrides = array(
            'test_form' => false,
            'unique_filename_callback' => function($dir, $name, $ext) {
                return 'course_' . time() . '_' . wp_generate_password(8, false) . $ext;
            }
        );
        
        // Subir archivo
        $uploaded_file = wp_handle_upload($file, $upload_overrides);
        
        if (isset($uploaded_file['error'])) {
            error_log('ASG: Error subiendo archivo: ' . $uploaded_file['error']);
            return new WP_REST_Response(array(
                'success' => false,
                'error' => array(
                    'code' => 'upload_error',
                    'message' => $uploaded_file['error']
                )
            ), 500);
        }
        
        error_log('ASG: Archivo subido exitosamente: ' . $uploaded_file['url']);
        
        // Crear diferentes tamaños si es necesario
        $image_sizes = array(
            'original' => $uploaded_file['url'],
            'thumbnail' => $uploaded_file['url'], // Por ahora usar la original
            'medium' => $uploaded_file['url']     // Por ahora usar la original
        );
        
        return new WP_REST_Response(array(
            'success' => true,
            'message' => 'Image uploaded successfully',
            'data' => array(
                'urls' => $image_sizes,
                'file_info' => array(
                    'name' => basename($uploaded_file['file']),
                    'size' => $file['size'],
                    'type' => $file['type']
                )
            )
        ), 200);
        
    } catch (Exception $e) {
        error_log('ASG: Excepción en upload_image: ' . $e->getMessage());
        return new WP_REST_Response(array(
            'success' => false,
            'error' => array(
                'code' => 'server_error',
                'message' => 'Internal server error: ' . $e->getMessage()
            )
        ), 500);
    }
}

/**
 * Obtener categorías disponibles
 *
 * @param WP_REST_Request $request
 * @return WP_REST_Response
 */
function asg_get_categories($request) {
    try {
        error_log('ASG: Obteniendo categorías');
        
        // Categorías predefinidas
        $categories = array(
            'business' => 'Business & Management',
            'technology' => 'Technology & IT',
            'marketing' => 'Marketing & Sales',
            'leadership' => 'Leadership & Development',
            'finance' => 'Finance & Accounting',
            'healthcare' => 'Healthcare & Medical',
            'education' => 'Education & Training',
            'design' => 'Design & Creative',
            'communication' => 'Communication Skills',
            'productivity' => 'Productivity & Organization'
        );
        
        return new WP_REST_Response(array(
            'success' => true,
            'data' => $categories
        ), 200);
        
    } catch (Exception $e) {
        error_log('ASG: Excepción en get_categories: ' . $e->getMessage());
        return new WP_REST_Response(array(
            'success' => false,
            'error' => array(
                'code' => 'server_error',
                'message' => 'Internal server error: ' . $e->getMessage()
            )
        ), 500);
    }
}

/**
 * Validar datos de curso
 *
 * @param WP_REST_Request $request
 * @return WP_REST_Response
 */
function asg_validate_course($request) {
    try {
        $data = $request->get_json_params();
        if (empty($data)) {
            $data = $request->get_params();
        }
        
        $errors = array();
        
        // Validar título
        if (empty($data['title'])) {
            $errors['title'] = 'Title is required';
        } elseif (strlen($data['title']) < 3) {
            $errors['title'] = 'Title must be at least 3 characters';
        } elseif (strlen($data['title']) > 100) {
            $errors['title'] = 'Title must be less than 100 characters';
        }
        
        // Validar descripción
        if (empty($data['description'])) {
            $errors['description'] = 'Description is required';
        } elseif (strlen($data['description']) < 10) {
            $errors['description'] = 'Description must be at least 10 characters';
        } elseif (strlen($data['description']) > 1000) {
            $errors['description'] = 'Description must be less than 1000 characters';
        }
        
        // Validar categoría
        if (empty($data['category'])) {
            $errors['category'] = 'Category is required';
        }
        
        // Validar precio
        if (isset($data['price'])) {
            $price = floatval($data['price']);
            if ($price < 0) {
                $errors['price'] = 'Price cannot be negative';
            } elseif ($price > 10000) {
                $errors['price'] = 'Price cannot exceed $10,000';
            }
        }
        
        // Validar duración
        if (isset($data['duration'])) {
            $duration = floatval($data['duration']);
            if ($duration < 0) {
                $errors['duration'] = 'Duration cannot be negative';
            } elseif ($duration > 1000) {
                $errors['duration'] = 'Duration cannot exceed 1000 hours';
            }
        }
        
        if (!empty($errors)) {
            return new WP_REST_Response(array(
                'success' => false,
                'errors' => $errors
            ), 400);
        }
        
        return new WP_REST_Response(array(
            'success' => true,
            'message' => 'Validation passed'
        ), 200);
        
    } catch (Exception $e) {
        error_log('ASG: Excepción en validate_course: ' . $e->getMessage());
        return new WP_REST_Response(array(
            'success' => false,
            'error' => array(
                'code' => 'server_error',
                'message' => 'Internal server error: ' . $e->getMessage()
            )
        ), 500);
    }
}

/**
 * Listar todos los cursos
 *
 * @param WP_REST_Request $request
 * @return WP_REST_Response
 */
function asg_get_all_courses($request) {
    global $wpdb;
    
    try {
        error_log('ASG: Obteniendo todos los cursos');
        
        // Parámetros de paginación
        $page = intval($request->get_param('page') ?? 1);
        $per_page = intval($request->get_param('per_page') ?? 10);
        $offset = ($page - 1) * $per_page;
        
        // Parámetros de filtrado
        $category = sanitize_text_field($request->get_param('category') ?? '');
        $status = sanitize_text_field($request->get_param('status') ?? 'published');
        $search = sanitize_text_field($request->get_param('search') ?? '');
        
        $table_courses = $wpdb->prefix . 'courses';

        // Construir WHERE clause
        $where_conditions = array("status_course = %s");
        $where_values = array($status);

        if (!empty($category)) {
            $where_conditions[] = "category_course = %s";
            $where_values[] = $category;
        }

        if (!empty($search)) {
            $where_conditions[] = "(name_course LIKE %s OR description_course LIKE %s)";
            $where_values[] = '%' . $wpdb->esc_like($search) . '%';
            $where_values[] = '%' . $wpdb->esc_like($search) . '%';
        }
        
        $where_clause = implode(' AND ', $where_conditions);
        
        // Obtener total de registros
        $total_query = "
            SELECT COUNT(*)
            FROM {$table_courses}
            WHERE {$where_clause}
        ";
        $total = $wpdb->get_var($wpdb->prepare($total_query, $where_values));
        
        // Obtener cursos
        $courses_query = "
            SELECT
                id_course as course_id,
                code_course,
                name_course as title,
                description_course as description,
                price_course as price,
                category_course as category,
                duration_course as duration,
                language_course as language,
                status_course as status,
                created_at,
                updated_at
            FROM {$table_courses}
            WHERE {$where_clause}
            ORDER BY created_at DESC
            LIMIT %d OFFSET %d
        ";
        $query_values = array_merge($where_values, array($per_page, $offset));
        
        $courses = $wpdb->get_results($wpdb->prepare($courses_query, $query_values), ARRAY_A);
        
        // Procesar cursos para incluir datos adicionales
        foreach ($courses as &$course) {
            $course['price'] = floatval($course['price']);
            $course['duration'] = floatval($course['duration']);
            $course['course_id'] = intval($course['course_id']);
            $course['post_id'] = intval($course['post_id']);
            
            // Obtener módulos, objetivos y beneficios
            $course['modules'] = asg_get_course_modules($course['course_id']);
            $course['learn_objectives'] = asg_get_course_objectives($course['course_id']);
            $course['benefits'] = asg_get_course_benefits($course['course_id']);
        }
        
        return new WP_REST_Response(array(
            'success' => true,
            'data' => $courses,
            'pagination' => array(
                'page' => $page,
                'per_page' => $per_page,
                'total' => intval($total),
                'total_pages' => ceil($total / $per_page)
            )
        ), 200);
        
    } catch (Exception $e) {
        error_log('ASG: Excepción en get_all_courses: ' . $e->getMessage());
        return new WP_REST_Response(array(
            'success' => false,
            'error' => array(
                'code' => 'server_error',
                'message' => 'Internal server error: ' . $e->getMessage()
            )
        ), 500);
    }
}

/**
 * Obtener curso específico
 * 
 * @param WP_REST_Request $request
 * @return WP_REST_Response
 */
function asg_get_course($request) {
    global $wpdb;
    
    try {
        $course_id = intval($request['id']);
        error_log('ASG: Obteniendo curso ID: ' . $course_id);
        
        if ($course_id <= 0) {
            return new WP_REST_Response(array(
                'success' => false,
                'error' => array(
                    'code' => 'invalid_id',
                    'message' => 'Invalid course ID'
                )
            ), 400);
        }
        
        $course = asg_get_course_by_id($course_id);
        
        if (!$course) {
            return new WP_REST_Response(array(
                'success' => false,
                'error' => array(
                    'code' => 'course_not_found',
                    'message' => 'Course not found'
                )
            ), 404);
        }
        
        return new WP_REST_Response(array(
            'success' => true,
            'data' => $course
        ), 200);
        
    } catch (Exception $e) {
        error_log('ASG: Excepción en get_course: ' . $e->getMessage());
        return new WP_REST_Response(array(
            'success' => false,
            'error' => array(
                'code' => 'server_error',
                'message' => 'Internal server error: ' . $e->getMessage()
            )
        ), 500);
    }
}

/**
 * Actualizar curso
 * 
 * @param WP_REST_Request $request
 * @return WP_REST_Response
 */
function asg_update_course($request) {
    global $wpdb;
    
    try {
        $course_id = intval($request['id']);
        error_log('ASG: Actualizando curso ID: ' . $course_id);
        
        if ($course_id <= 0) {
            return new WP_REST_Response(array(
                'success' => false,
                'error' => array(
                    'code' => 'invalid_id',
                    'message' => 'Invalid course ID'
                )
            ), 400);
        }
        
        // Obtener datos del request
        $data = $request->get_json_params();
        if (empty($data)) {
            $data = $request->get_params();
        }
        
        $table_courses = $wpdb->prefix . 'courses';
        
        // Verificar que el curso existe y obtener post_id
        $existing_course = $wpdb->get_row($wpdb->prepare(
            "SELECT id, post_id FROM {$table_courses} WHERE id = %d",
            $course_id
        ));
        
        if (!$existing_course) {
            return new WP_REST_Response(array(
                'success' => false,
                'error' => array(
                    'code' => 'course_not_found',
                    'message' => 'Course not found'
                )
            ), 404);
        }
        
        // Iniciar transacción
        $wpdb->query('START TRANSACTION');
        
        // Actualizar post de WordPress si hay cambios en título o descripción
        if (isset($data['title']) || isset($data['description'])) {
            $post_update = array('ID' => $existing_course->post_id);
            
            if (isset($data['title'])) {
                $post_update['post_title'] = sanitize_text_field($data['title']);
            }
            
            if (isset($data['description'])) {
                $post_update['post_content'] = sanitize_textarea_field($data['description']);
            }
            
            $post_result = wp_update_post($post_update);
            
            if (is_wp_error($post_result)) {
                throw new Exception('Error updating WordPress post: ' . $post_result->get_error_message());
            }
        }
        
        // Preparar datos para actualizar en tabla de cursos
        $update_data = array();
        $update_format = array();
        
        if (isset($data['price'])) {
            $update_data['price'] = floatval($data['price']);
            $update_format[] = '%f';
        }
        
        if (isset($data['category'])) {
            $update_data['category'] = sanitize_text_field($data['category']);
            $update_format[] = '%s';
        }
        
        if (isset($data['duration'])) {
            $update_data['duration'] = floatval($data['duration']);
            $update_format[] = '%f';
        }
        
        if (isset($data['language'])) {
            $update_data['language'] = sanitize_text_field($data['language']);
            $update_format[] = '%s';
        }
        
        if (isset($data['status'])) {
            $update_data['status'] = sanitize_text_field($data['status']);
            $update_format[] = '%s';
        }
        
        // Siempre actualizar updated_at
        $update_data['updated_at'] = current_time('mysql');
        $update_format[] = '%s';
        
        if (count($update_data) > 1) { // Más que solo updated_at
            // Actualizar en la base de datos
            $result = $wpdb->update(
                $table_courses,
                $update_data,
                array('id' => $course_id),
                $update_format,
                array('%d')
            );
            
            if ($result === false) {
                throw new Exception('Error updating course: ' . $wpdb->last_error);
            }
        }
        
        // Actualizar módulos si se proporcionan
        if (isset($data['modules']) && is_array($data['modules'])) {
            asg_update_course_modules($course_id, $data['modules']);
        }
        
        // Actualizar objetivos si se proporcionan
        if (isset($data['learn_objectives']) && is_array($data['learn_objectives'])) {
            asg_update_course_objectives($course_id, $data['learn_objectives']);
        }
        
        // Actualizar beneficios si se proporcionan
        if (isset($data['benefits']) && is_array($data['benefits'])) {
            asg_update_course_benefits($course_id, $data['benefits']);
        }
        
        // Confirmar transacción
        $wpdb->query('COMMIT');
        
        // Obtener el curso actualizado
        $updated_course = asg_get_course_by_id($course_id);
        
        return new WP_REST_Response(array(
            'success' => true,
            'message' => 'Course updated successfully',
            'data' => $updated_course
        ), 200);
        
    } catch (Exception $e) {
        $wpdb->query('ROLLBACK');
        
        error_log('ASG: Excepción en update_course: ' . $e->getMessage());
        return new WP_REST_Response(array(
            'success' => false,
            'error' => array(
                'code' => 'server_error',
                'message' => 'Internal server error: ' . $e->getMessage()
            )
        ), 500);
    }
}

/**
 * Eliminar curso
 * 
 * @param WP_REST_Request $request
 * @return WP_REST_Response
 */
function asg_delete_course($request) {
    global $wpdb;
    
    try {
        $course_id = intval($request['id']);
        error_log('ASG: Eliminando curso ID: ' . $course_id);
        
        if ($course_id <= 0) {
            return new WP_REST_Response(array(
                'success' => false,
                'error' => array(
                    'code' => 'invalid_id',
                    'message' => 'Invalid course ID'
                )
            ), 400);
        }
        
        $table_courses = $wpdb->prefix . 'courses';
        
        // Verificar que el curso existe
        $existing_course = $wpdb->get_row($wpdb->prepare(
            "SELECT id, post_id FROM {$table_courses} WHERE id = %d",
            $course_id
        ));
        
        if (!$existing_course) {
            return new WP_REST_Response(array(
                'success' => false,
                'error' => array(
                    'code' => 'course_not_found',
                    'message' => 'Course not found'
                )
            ), 404);
        }
        
        // Soft delete - cambiar status a archived
        $result = $wpdb->update(
            $table_courses,
            array(
                'status' => 'archived',
                'updated_at' => current_time('mysql')
            ),
            array('id' => $course_id),
            array('%s', '%s'),
            array('%d')
        );
        
        if ($result === false) {
            error_log('ASG: Error eliminando curso: ' . $wpdb->last_error);
            return new WP_REST_Response(array(
                'success' => false,
                'error' => array(
                    'code' => 'database_error',
                    'message' => 'Error deleting course: ' . $wpdb->last_error
                )
            ), 500);
        }
        
        // También cambiar el post a draft
        wp_update_post(array(
            'ID' => $existing_course->post_id,
            'post_status' => 'draft'
        ));
        
        return new WP_REST_Response(array(
            'success' => true,
            'message' => 'Course deleted successfully'
        ), 200);
        
    } catch (Exception $e) {
        error_log('ASG: Excepción en delete_course: ' . $e->getMessage());
        return new WP_REST_Response(array(
            'success' => false,
            'error' => array(
                'code' => 'server_error',
                'message' => 'Internal server error: ' . $e->getMessage()
            )
        ), 500);
    }
}

/**
 * Obtener estadísticas del dashboard
 * 
 * @param WP_REST_Request $request
 * @return WP_REST_Response
 */
function asg_get_dashboard_stats($request) {
    global $wpdb;
    
    try {
        error_log('ASG: Obteniendo estadísticas del dashboard');
        
        $table_courses = $wpdb->prefix . 'courses';
        
        // Total de cursos publicados
        $total_courses = $wpdb->get_var("SELECT COUNT(*) FROM {$table_courses} WHERE status = 'published'");
        
        // Cursos por categoría
        $courses_by_category = $wpdb->get_results(
            "SELECT category, COUNT(*) as count FROM {$table_courses} WHERE status = 'published' GROUP BY category",
            ARRAY_A
        );
        
        // Cursos creados este mes
        $courses_this_month = $wpdb->get_var($wpdb->prepare(
            "SELECT COUNT(*) FROM {$table_courses} WHERE status = 'published' AND created_at >= %s",
            date('Y-m-01')
        ));
        
        // Cursos gratuitos vs pagos
        $free_courses = $wpdb->get_var("SELECT COUNT(*) FROM {$table_courses} WHERE status = 'published' AND price = 0");
        $paid_courses = $wpdb->get_var("SELECT COUNT(*) FROM {$table_courses} WHERE status = 'published' AND price > 0");
        
        // Borradores
        $draft_courses = $wpdb->get_var("SELECT COUNT(*) FROM {$table_courses} WHERE status = 'draft'");
        
        // Últimos cursos creados
        $recent_courses = $wpdb->get_results($wpdb->prepare(
            "SELECT id, title, category, price, created_at
             FROM {$table_courses}
             WHERE status = 'published'
             ORDER BY created_at DESC
             LIMIT %d",
            5
        ), ARRAY_A);
        
        return new WP_REST_Response(array(
            'success' => true,
            'data' => array(
                'total_courses' => intval($total_courses),
                'courses_this_month' => intval($courses_this_month),
                'free_courses' => intval($free_courses),
                'paid_courses' => intval($paid_courses),
                'draft_courses' => intval($draft_courses),
                'courses_by_category' => $courses_by_category,
                'recent_courses' => $recent_courses
            )
        ), 200);
        
    } catch (Exception $e) {
        error_log('ASG: Excepción en get_dashboard_stats: ' . $e->getMessage());
        return new WP_REST_Response(array(
            'success' => false,
            'error' => array(
                'code' => 'server_error',
                'message' => 'Internal server error: ' . $e->getMessage()
            )
        ), 500);
    }
}

/**
 * Buscar cursos
 * 
 * @param WP_REST_Request $request
 * @return WP_REST_Response
 */
function asg_search_courses($request) {
    global $wpdb;
    
    try {
        $search_term = sanitize_text_field($request->get_param('q') ?? '');
        $category = sanitize_text_field($request->get_param('category') ?? '');
        $limit = intval($request->get_param('limit') ?? 10);
        
        if (empty($search_term)) {
            return new WP_REST_Response(array(
                'success' => false,
                'error' => array(
                    'code' => 'missing_search_term',
                    'message' => 'Search term is required'
                )
            ), 400);
        }
        
        $table_courses = $wpdb->prefix . 'courses';
        $table_posts = $wpdb->prefix . 'posts';
        
        // Construir query
        $where_conditions = array("c.status = 'published'", "p.post_type = 'course'");
        $where_values = array();
        
        // Búsqueda por texto
        $where_conditions[] = "(p.post_title LIKE %s OR p.post_content LIKE %s)";
        $search_like = '%' . $wpdb->esc_like($search_term) . '%';
        $where_values[] = $search_like;
        $where_values[] = $search_like;
        
        // Filtro por categoría
        if (!empty($category)) {
            $where_conditions[] = "c.category = %s";
            $where_values[] = $category;
        }
        
        $where_clause = implode(' AND ', $where_conditions);
        $where_values[] = $limit;
        
        $query = "
            SELECT 
                c.id as course_id,
                c.post_id,
                p.post_title as title,
                p.post_content as description,
                c.price,
                c.category,
                c.duration,
                c.language,
                c.status,
                c.created_at,
                c.updated_at
            FROM {$table_courses} c 
            INNER JOIN {$table_posts} p ON c.post_id = p.ID 
            WHERE {$where_clause} 
            ORDER BY c.created_at DESC 
            LIMIT %d
        ";
        
        $courses = $wpdb->get_results($wpdb->prepare($query, $where_values), ARRAY_A);
        
        // Procesar resultados
        foreach ($courses as &$course) {
            $course['price'] = floatval($course['price']);
            $course['duration'] = floatval($course['duration']);
            $course['course_id'] = intval($course['course_id']);
            $course['post_id'] = intval($course['post_id']);
            
            // Obtener datos adicionales
            $course['modules'] = asg_get_course_modules($course['course_id']);
            $course['learn_objectives'] = asg_get_course_objectives($course['course_id']);
            $course['benefits'] = asg_get_course_benefits($course['course_id']);
        }
        
        return new WP_REST_Response(array(
            'success' => true,
            'data' => $courses,
            'search_term' => $search_term,
            'total_results' => count($courses)
        ), 200);
        
    } catch (Exception $e) {
        error_log('ASG: Excepción en search_courses: ' . $e->getMessage());
        return new WP_REST_Response(array(
            'success' => false,
            'error' => array(
                'code' => 'server_error',
                'message' => 'Internal server error: ' . $e->getMessage()
            )
        ), 500);
    }
}

/**
 * ========================================
 * FUNCIONES DE UTILIDAD
 * ========================================
 */

/**
 * Verificar permisos (simplificado para desarrollo)
 * 
 * @param WP_REST_Request $request
 * @return bool
 */
function asg_check_permissions($request) {
    // Por ahora permitir todo para desarrollo
    // En producción, implementar verificación real de permisos
    return true;
}

/**
 * Obtener curso completo por ID
 * 
 * @param int $course_id
 * @return array|null
 */
function asg_get_course_by_id($course_id) {
    global $wpdb;
    
    $table_courses = $wpdb->prefix . 'asg_courses';
    $table_posts = $wpdb->prefix . 'posts';
    
    $course = $wpdb->get_row($wpdb->prepare(
        "SELECT 
            c.id as course_id,
            c.post_id,
            p.post_title as title,
            p.post_content as description,
            c.price,
            c.category,
            c.duration,
            c.language,
            c.status,
            c.created_at,
            c.updated_at
        FROM {$table_courses} c 
        INNER JOIN {$table_posts} p ON c.post_id = p.ID 
        WHERE c.id = %d",
        $course_id
    ), ARRAY_A);
    
    if (!$course) {
        return null;
    }
    
    // Procesar datos
    $course['price'] = floatval($course['price']);
    $course['duration'] = floatval($course['duration']);
    $course['course_id'] = intval($course['course_id']);
    $course['post_id'] = intval($course['post_id']);
    
    // Obtener datos relacionados
    $course['modules'] = asg_get_course_modules($course_id);
    $course['learn_objectives'] = asg_get_course_objectives($course_id);
    $course['benefits'] = asg_get_course_benefits($course_id);
    
    return $course;
}

/**
 * Insertar módulos del curso
 * 
 * @param int $course_id
 * @param array $modules
 */
function asg_insert_course_modules($course_id, $modules) {
    global $wpdb;
    
    $table_modules = $wpdb->prefix . 'modules';
    
    foreach ($modules as $index => $module) {
        if (!empty($module['title'])) {
            $wpdb->insert(
                $table_modules,
                array(
                    'course_id' => $course_id,
                    'title' => sanitize_text_field($module['title']),
                    'description' => sanitize_textarea_field($module['description'] ?? ''),
                    'duration' => intval($module['duration'] ?? 0),
                    'order_index' => $index,
                    'image_url' => esc_url_raw($module['image_url'] ?? ''),
                    'created_at' => current_time('mysql')
                ),
                array('%d', '%s', '%s', '%d', '%d', '%s', '%s')
            );
        }
    }
    
    error_log('ASG: Insertados ' . count($modules) . ' módulos para curso ' . $course_id);
}

/**
 * ========================================
 * FASE 2: FUNCIONES PARA ALL COURSES
 * ========================================
 */

/**
 * Filtrar cursos por múltiples criterios
 */
function asg_filter_courses($request) {
    global $wpdb;

    try {
        $table_courses = $wpdb->prefix . 'asg_courses';
        $table_posts = $wpdb->prefix . 'posts';

        // Parámetros de filtrado
        $category = sanitize_text_field($request->get_param('category') ?? '');
        $status = sanitize_text_field($request->get_param('status') ?? '');
        $price_min = floatval($request->get_param('price_min') ?? 0);
        $price_max = floatval($request->get_param('price_max') ?? 999999);
        $duration_min = floatval($request->get_param('duration_min') ?? 0);
        $duration_max = floatval($request->get_param('duration_max') ?? 999999);
        $language = sanitize_text_field($request->get_param('language') ?? '');
        $search = sanitize_text_field($request->get_param('search') ?? '');
        $sort_by = sanitize_text_field($request->get_param('sort_by') ?? 'created_at');
        $sort_order = sanitize_text_field($request->get_param('sort_order') ?? 'DESC');

        // Construir WHERE clause
        $where_conditions = array("p.post_type = 'course'");
        $where_values = array();

        if (!empty($category)) {
            $where_conditions[] = "c.category = %s";
            $where_values[] = $category;
        }

        if (!empty($status)) {
            $where_conditions[] = "c.status = %s";
            $where_values[] = $status;
        }

        if ($price_min > 0 || $price_max < 999999) {
            $where_conditions[] = "c.price BETWEEN %f AND %f";
            $where_values[] = $price_min;
            $where_values[] = $price_max;
        }

        if ($duration_min > 0 || $duration_max < 999999) {
            $where_conditions[] = "c.duration BETWEEN %f AND %f";
            $where_values[] = $duration_min;
            $where_values[] = $duration_max;
        }

        if (!empty($language)) {
            $where_conditions[] = "c.language = %s";
            $where_values[] = $language;
        }

        if (!empty($search)) {
            $where_conditions[] = "(p.post_title LIKE %s OR p.post_content LIKE %s)";
            $where_values[] = '%' . $search . '%';
            $where_values[] = '%' . $search . '%';
        }

        $where_clause = implode(' AND ', $where_conditions);

        // Validar sort_by
        $allowed_sort_fields = array('created_at', 'updated_at', 'title', 'price', 'duration');
        if (!in_array($sort_by, $allowed_sort_fields)) {
            $sort_by = 'created_at';
        }

        $sort_order = strtoupper($sort_order) === 'ASC' ? 'ASC' : 'DESC';

        $query = "
            SELECT
                c.id as course_id,
                c.post_id,
                p.post_title as title,
                p.post_content as description,
                c.price,
                c.category,
                c.duration,
                c.language,
                c.status,
                c.created_at,
                c.updated_at
            FROM {$table_courses} c
            INNER JOIN {$table_posts} p ON c.post_id = p.ID
            WHERE {$where_clause}
            ORDER BY c.{$sort_by} {$sort_order}
        ";

        if (!empty($where_values)) {
            $courses = $wpdb->get_results($wpdb->prepare($query, $where_values), ARRAY_A);
        } else {
            $courses = $wpdb->get_results($query, ARRAY_A);
        }

        // Procesar resultados
        foreach ($courses as &$course) {
            $course['course_id'] = intval($course['course_id']);
            $course['post_id'] = intval($course['post_id']);
            $course['price'] = floatval($course['price']);
            $course['duration'] = floatval($course['duration']);

            // Obtener imagen destacada
            $course['featured_image_url'] = get_the_post_thumbnail_url($course['post_id'], 'medium');

            // Contar módulos
            $module_count = $wpdb->get_var($wpdb->prepare(
                "SELECT COUNT(*) FROM {$wpdb->prefix}modules WHERE course_id = %d",
                $course['course_id']
            ));
            $course['module_count'] = intval($module_count);
        }

        return new WP_REST_Response(array(
            'success' => true,
            'data' => $courses,
            'total' => count($courses),
            'filters_applied' => array(
                'category' => $category,
                'status' => $status,
                'price_range' => array($price_min, $price_max),
                'duration_range' => array($duration_min, $duration_max),
                'language' => $language,
                'search' => $search,
                'sort_by' => $sort_by,
                'sort_order' => $sort_order
            )
        ), 200);

    } catch (Exception $e) {
        error_log('ASG Error en asg_filter_courses: ' . $e->getMessage());
        return new WP_REST_Response(array(
            'success' => false,
            'error' => array(
                'code' => 'FILTER_ERROR',
                'message' => 'Error filtering courses: ' . $e->getMessage()
            )
        ), 500);
    }
}

/**
 * Obtener cursos con paginación
 */
function asg_get_courses_paginated($request) {
    global $wpdb;

    try {
        $table_courses = $wpdb->prefix . 'asg_courses';
        $table_posts = $wpdb->prefix . 'posts';

        // Parámetros de paginación
        $page = intval($request->get_param('page') ?? 1);
        $per_page = intval($request->get_param('per_page') ?? 10);
        $status = sanitize_text_field($request->get_param('status') ?? 'published');

        // Validar parámetros
        $page = max(1, $page);
        $per_page = min(100, max(1, $per_page)); // Máximo 100 por página

        $offset = ($page - 1) * $per_page;

        // Contar total de cursos
        $total_query = "
            SELECT COUNT(*)
            FROM {$table_courses} c
            INNER JOIN {$table_posts} p ON c.post_id = p.ID
            WHERE c.status = %s AND p.post_type = 'course'
        ";

        $total_courses = $wpdb->get_var($wpdb->prepare($total_query, $status));
        $total_pages = ceil($total_courses / $per_page);

        // Obtener cursos paginados
        $courses_query = "
            SELECT
                c.id as course_id,
                c.post_id,
                p.post_title as title,
                p.post_content as description,
                c.price,
                c.category,
                c.duration,
                c.language,
                c.status,
                c.created_at,
                c.updated_at
            FROM {$table_courses} c
            INNER JOIN {$table_posts} p ON c.post_id = p.ID
            WHERE c.status = %s AND p.post_type = 'course'
            ORDER BY c.created_at DESC
            LIMIT %d OFFSET %d
        ";

        $courses = $wpdb->get_results($wpdb->prepare(
            $courses_query,
            $status,
            $per_page,
            $offset
        ), ARRAY_A);

        // Procesar resultados
        foreach ($courses as &$course) {
            $course['course_id'] = intval($course['course_id']);
            $course['post_id'] = intval($course['post_id']);
            $course['price'] = floatval($course['price']);
            $course['duration'] = floatval($course['duration']);

            // Obtener imagen destacada
            $course['featured_image_url'] = get_the_post_thumbnail_url($course['post_id'], 'medium');

            // Contar módulos
            $module_count = $wpdb->get_var($wpdb->prepare(
                "SELECT COUNT(*) FROM {$wpdb->prefix}modules WHERE course_id = %d",
                $course['course_id']
            ));
            $course['module_count'] = intval($module_count);
        }

        return new WP_REST_Response(array(
            'success' => true,
            'data' => $courses,
            'pagination' => array(
                'current_page' => $page,
                'per_page' => $per_page,
                'total_items' => intval($total_courses),
                'total_pages' => $total_pages,
                'has_next' => $page < $total_pages,
                'has_prev' => $page > 1
            )
        ), 200);

    } catch (Exception $e) {
        error_log('ASG Error en asg_get_courses_paginated: ' . $e->getMessage());
        return new WP_REST_Response(array(
            'success' => false,
            'error' => array(
                'code' => 'PAGINATION_ERROR',
                'message' => 'Error getting paginated courses: ' . $e->getMessage()
            )
        ), 500);
    }
}

/**
 * Actualizar estado de múltiples cursos (bulk action)
 */
function asg_bulk_update_status($request) {
    global $wpdb;

    try {
        $data = $request->get_json_params();

        if (empty($data['course_ids']) || !is_array($data['course_ids'])) {
            return new WP_REST_Response(array(
                'success' => false,
                'error' => array(
                    'code' => 'MISSING_COURSE_IDS',
                    'message' => 'Course IDs array is required'
                )
            ), 400);
        }

        if (empty($data['status'])) {
            return new WP_REST_Response(array(
                'success' => false,
                'error' => array(
                    'code' => 'MISSING_STATUS',
                    'message' => 'Status is required'
                )
            ), 400);
        }

        $course_ids = array_map('intval', $data['course_ids']);
        $new_status = sanitize_text_field($data['status']);

        // Validar estado
        $allowed_statuses = array('draft', 'published', 'archived');
        if (!in_array($new_status, $allowed_statuses)) {
            return new WP_REST_Response(array(
                'success' => false,
                'error' => array(
                    'code' => 'INVALID_STATUS',
                    'message' => 'Invalid status. Allowed: ' . implode(', ', $allowed_statuses)
                )
            ), 400);
        }

        $table_courses = $wpdb->prefix . 'asg_courses';
        $updated_count = 0;
        $errors = array();

        foreach ($course_ids as $course_id) {
            // Verificar que el curso existe
            $exists = $wpdb->get_var($wpdb->prepare(
                "SELECT id FROM {$table_courses} WHERE id = %d",
                $course_id
            ));

            if (!$exists) {
                $errors[] = "Course ID {$course_id} not found";
                continue;
            }

            // Actualizar estado
            $result = $wpdb->update(
                $table_courses,
                array(
                    'status' => $new_status,
                    'updated_at' => current_time('mysql')
                ),
                array('id' => $course_id),
                array('%s', '%s'),
                array('%d')
            );

            if ($result !== false) {
                $updated_count++;

                // También actualizar el post status
                $post_id = $wpdb->get_var($wpdb->prepare(
                    "SELECT post_id FROM {$table_courses} WHERE id = %d",
                    $course_id
                ));

                if ($post_id) {
                    $post_status = ($new_status === 'published') ? 'publish' : 'draft';
                    wp_update_post(array(
                        'ID' => $post_id,
                        'post_status' => $post_status
                    ));
                }
            } else {
                $errors[] = "Failed to update course ID {$course_id}";
            }
        }

        return new WP_REST_Response(array(
            'success' => true,
            'data' => array(
                'updated_count' => $updated_count,
                'total_requested' => count($course_ids),
                'new_status' => $new_status,
                'errors' => $errors
            )
        ), 200);

    } catch (Exception $e) {
        error_log('ASG Error en asg_bulk_update_status: ' . $e->getMessage());
        return new WP_REST_Response(array(
            'success' => false,
            'error' => array(
                'code' => 'BULK_UPDATE_ERROR',
                'message' => 'Error updating course statuses: ' . $e->getMessage()
            )
        ), 500);
    }
}

/**
 * Eliminar múltiples cursos (bulk delete)
 */
function asg_bulk_delete_courses($request) {
    global $wpdb;

    try {
        $data = $request->get_json_params();

        if (empty($data['course_ids']) || !is_array($data['course_ids'])) {
            return new WP_REST_Response(array(
                'success' => false,
                'error' => array(
                    'code' => 'MISSING_COURSE_IDS',
                    'message' => 'Course IDs array is required'
                )
            ), 400);
        }

        $course_ids = array_map('intval', $data['course_ids']);
        $table_courses = $wpdb->prefix . 'asg_courses';
        $deleted_count = 0;
        $errors = array();

        foreach ($course_ids as $course_id) {
            // Verificar que el curso existe
            $course = $wpdb->get_row($wpdb->prepare(
                "SELECT id, post_id FROM {$table_courses} WHERE id = %d",
                $course_id
            ));

            if (!$course) {
                $errors[] = "Course ID {$course_id} not found";
                continue;
            }

            // Soft delete - cambiar a archived
            $result = $wpdb->update(
                $table_courses,
                array(
                    'status' => 'archived',
                    'updated_at' => current_time('mysql')
                ),
                array('id' => $course_id),
                array('%s', '%s'),
                array('%d')
            );

            if ($result !== false) {
                $deleted_count++;

                // También cambiar el post a draft
                if ($course->post_id) {
                    wp_update_post(array(
                        'ID' => $course->post_id,
                        'post_status' => 'draft'
                    ));
                }
            } else {
                $errors[] = "Failed to delete course ID {$course_id}";
            }
        }

        return new WP_REST_Response(array(
            'success' => true,
            'data' => array(
                'deleted_count' => $deleted_count,
                'total_requested' => count($course_ids),
                'errors' => $errors
            )
        ), 200);

    } catch (Exception $e) {
        error_log('ASG Error en asg_bulk_delete_courses: ' . $e->getMessage());
        return new WP_REST_Response(array(
            'success' => false,
            'error' => array(
                'code' => 'BULK_DELETE_ERROR',
                'message' => 'Error deleting courses: ' . $e->getMessage()
            )
        ), 500);
    }
}

/**
 * ========================================
 * FASE 3: FUNCIONES PARA EDIT COURSE
 * ========================================
 */

/**
 * Obtener solo los módulos de un curso
 */
function asg_get_course_modules_only($request) {
    $course_id = intval($request['id']);

    if (!$course_id) {
        return new WP_REST_Response(array(
            'success' => false,
            'error' => array(
                'code' => 'INVALID_COURSE_ID',
                'message' => 'Valid course ID is required'
            )
        ), 400);
    }

    try {
        $modules = asg_get_course_modules($course_id);

        return new WP_REST_Response(array(
            'success' => true,
            'data' => array(
                'course_id' => $course_id,
                'modules' => $modules,
                'total_modules' => count($modules)
            )
        ), 200);

    } catch (Exception $e) {
        error_log('ASG Error en asg_get_course_modules_only: ' . $e->getMessage());
        return new WP_REST_Response(array(
            'success' => false,
            'error' => array(
                'code' => 'MODULES_FETCH_ERROR',
                'message' => 'Error fetching course modules: ' . $e->getMessage()
            )
        ), 500);
    }
}

/**
 * Actualizar solo los módulos de un curso
 */
function asg_update_course_modules_only($request) {
    $course_id = intval($request['id']);
    $data = $request->get_json_params();

    if (!$course_id) {
        return new WP_REST_Response(array(
            'success' => false,
            'error' => array(
                'code' => 'INVALID_COURSE_ID',
                'message' => 'Valid course ID is required'
            )
        ), 400);
    }

    if (!isset($data['modules']) || !is_array($data['modules'])) {
        return new WP_REST_Response(array(
            'success' => false,
            'error' => array(
                'code' => 'INVALID_MODULES_DATA',
                'message' => 'Modules array is required'
            )
        ), 400);
    }

    try {
        // Verificar que el curso existe
        global $wpdb;
        $table_courses = $wpdb->prefix . 'asg_courses';

        $course_exists = $wpdb->get_var($wpdb->prepare(
            "SELECT id FROM {$table_courses} WHERE id = %d",
            $course_id
        ));

        if (!$course_exists) {
            return new WP_REST_Response(array(
                'success' => false,
                'error' => array(
                    'code' => 'COURSE_NOT_FOUND',
                    'message' => 'Course not found'
                )
            ), 404);
        }

        // Actualizar módulos
        asg_update_course_modules($course_id, $data['modules']);

        // Actualizar timestamp del curso
        $wpdb->update(
            $table_courses,
            array('updated_at' => current_time('mysql')),
            array('id' => $course_id),
            array('%s'),
            array('%d')
        );

        // Obtener módulos actualizados
        $updated_modules = asg_get_course_modules($course_id);

        return new WP_REST_Response(array(
            'success' => true,
            'data' => array(
                'course_id' => $course_id,
                'modules' => $updated_modules,
                'total_modules' => count($updated_modules),
                'updated_at' => current_time('mysql')
            )
        ), 200);

    } catch (Exception $e) {
        error_log('ASG Error en asg_update_course_modules_only: ' . $e->getMessage());
        return new WP_REST_Response(array(
            'success' => false,
            'error' => array(
                'code' => 'MODULES_UPDATE_ERROR',
                'message' => 'Error updating course modules: ' . $e->getMessage()
            )
        ), 500);
    }
}

/**
 * Agregar un módulo específico a un curso
 */
function asg_add_course_module($request) {
    $course_id = intval($request['id']);
    $data = $request->get_json_params();

    if (!$course_id) {
        return new WP_REST_Response(array(
            'success' => false,
            'error' => array(
                'code' => 'INVALID_COURSE_ID',
                'message' => 'Valid course ID is required'
            )
        ), 400);
    }

    if (empty($data['title'])) {
        return new WP_REST_Response(array(
            'success' => false,
            'error' => array(
                'code' => 'MISSING_MODULE_TITLE',
                'message' => 'Module title is required'
            )
        ), 400);
    }

    try {
        global $wpdb;
        $table_courses = $wpdb->prefix . 'asg_courses';
        $table_modules = $wpdb->prefix . 'modules';

        // Verificar que el curso existe
        $course_exists = $wpdb->get_var($wpdb->prepare(
            "SELECT id FROM {$table_courses} WHERE id = %d",
            $course_id
        ));

        if (!$course_exists) {
            return new WP_REST_Response(array(
                'success' => false,
                'error' => array(
                    'code' => 'COURSE_NOT_FOUND',
                    'message' => 'Course not found'
                )
            ), 404);
        }

        // Obtener el siguiente order_index
        $next_order = $wpdb->get_var($wpdb->prepare(
            "SELECT COALESCE(MAX(order_index), -1) + 1 FROM {$table_modules} WHERE course_id = %d",
            $course_id
        ));

        // Insertar nuevo módulo
        $result = $wpdb->insert(
            $table_modules,
            array(
                'course_id' => $course_id,
                'title' => sanitize_text_field($data['title']),
                'description' => sanitize_textarea_field($data['description'] ?? ''),
                'image_url' => esc_url_raw($data['image_url'] ?? ''),
                'order_index' => intval($next_order),
                'created_at' => current_time('mysql')
            ),
            array('%d', '%s', '%s', '%s', '%d', '%s')
        );

        if ($result === false) {
            throw new Exception('Failed to insert module');
        }

        $module_id = $wpdb->insert_id;

        // Actualizar timestamp del curso
        $wpdb->update(
            $table_courses,
            array('updated_at' => current_time('mysql')),
            array('id' => $course_id),
            array('%s'),
            array('%d')
        );

        // Obtener el módulo creado
        $new_module = $wpdb->get_row($wpdb->prepare(
            "SELECT * FROM {$table_modules} WHERE id = %d",
            $module_id
        ), ARRAY_A);

        return new WP_REST_Response(array(
            'success' => true,
            'data' => array(
                'module' => $new_module,
                'course_id' => $course_id,
                'message' => 'Module added successfully'
            )
        ), 201);

    } catch (Exception $e) {
        error_log('ASG Error en asg_add_course_module: ' . $e->getMessage());
        return new WP_REST_Response(array(
            'success' => false,
            'error' => array(
                'code' => 'MODULE_ADD_ERROR',
                'message' => 'Error adding module: ' . $e->getMessage()
            )
        ), 500);
    }
}

/**
 * Eliminar un módulo específico
 */
function asg_delete_course_module($request) {
    $course_id = intval($request['course_id']);
    $module_id = intval($request['module_id']);

    if (!$course_id || !$module_id) {
        return new WP_REST_Response(array(
            'success' => false,
            'error' => array(
                'code' => 'INVALID_IDS',
                'message' => 'Valid course ID and module ID are required'
            )
        ), 400);
    }

    try {
        global $wpdb;
        $table_courses = $wpdb->prefix . 'asg_courses';
        $table_modules = $wpdb->prefix . 'modules';

        // Verificar que el módulo existe y pertenece al curso
        $module = $wpdb->get_row($wpdb->prepare(
            "SELECT * FROM {$table_modules} WHERE id = %d AND course_id = %d",
            $module_id,
            $course_id
        ));

        if (!$module) {
            return new WP_REST_Response(array(
                'success' => false,
                'error' => array(
                    'code' => 'MODULE_NOT_FOUND',
                    'message' => 'Module not found or does not belong to this course'
                )
            ), 404);
        }

        // Eliminar módulo
        $result = $wpdb->delete(
            $table_modules,
            array('id' => $module_id),
            array('%d')
        );

        if ($result === false) {
            throw new Exception('Failed to delete module');
        }

        // Reordenar módulos restantes
        $remaining_modules = $wpdb->get_results($wpdb->prepare(
            "SELECT id FROM {$table_modules} WHERE course_id = %d ORDER BY order_index ASC",
            $course_id
        ));

        foreach ($remaining_modules as $index => $mod) {
            $wpdb->update(
                $table_modules,
                array('order_index' => $index),
                array('id' => $mod->id),
                array('%d'),
                array('%d')
            );
        }

        // Actualizar timestamp del curso
        $wpdb->update(
            $table_courses,
            array('updated_at' => current_time('mysql')),
            array('id' => $course_id),
            array('%s'),
            array('%d')
        );

        return new WP_REST_Response(array(
            'success' => true,
            'data' => array(
                'deleted_module_id' => $module_id,
                'course_id' => $course_id,
                'message' => 'Module deleted successfully'
            )
        ), 200);

    } catch (Exception $e) {
        error_log('ASG Error en asg_delete_course_module: ' . $e->getMessage());
        return new WP_REST_Response(array(
            'success' => false,
            'error' => array(
                'code' => 'MODULE_DELETE_ERROR',
                'message' => 'Error deleting module: ' . $e->getMessage()
            )
        ), 500);
    }
}

/**
 * Reordenar módulos de un curso
 */
function asg_reorder_course_modules($request) {
    $course_id = intval($request['id']);
    $data = $request->get_json_params();

    if (!$course_id) {
        return new WP_REST_Response(array(
            'success' => false,
            'error' => array(
                'code' => 'INVALID_COURSE_ID',
                'message' => 'Valid course ID is required'
            )
        ), 400);
    }

    if (!isset($data['module_order']) || !is_array($data['module_order'])) {
        return new WP_REST_Response(array(
            'success' => false,
            'error' => array(
                'code' => 'INVALID_ORDER_DATA',
                'message' => 'Module order array is required'
            )
        ), 400);
    }

    try {
        global $wpdb;
        $table_courses = $wpdb->prefix . 'asg_courses';
        $table_modules = $wpdb->prefix . 'modules';

        // Verificar que el curso existe
        $course_exists = $wpdb->get_var($wpdb->prepare(
            "SELECT id FROM {$table_courses} WHERE id = %d",
            $course_id
        ));

        if (!$course_exists) {
            return new WP_REST_Response(array(
                'success' => false,
                'error' => array(
                    'code' => 'COURSE_NOT_FOUND',
                    'message' => 'Course not found'
                )
            ), 404);
        }

        $module_order = array_map('intval', $data['module_order']);
        $updated_count = 0;

        // Actualizar orden de cada módulo
        foreach ($module_order as $index => $module_id) {
            $result = $wpdb->update(
                $table_modules,
                array('order_index' => $index),
                array('id' => $module_id, 'course_id' => $course_id),
                array('%d'),
                array('%d', '%d')
            );

            if ($result !== false) {
                $updated_count++;
            }
        }

        // Actualizar timestamp del curso
        $wpdb->update(
            $table_courses,
            array('updated_at' => current_time('mysql')),
            array('id' => $course_id),
            array('%s'),
            array('%d')
        );

        // Obtener módulos reordenados
        $reordered_modules = asg_get_course_modules($course_id);

        return new WP_REST_Response(array(
            'success' => true,
            'data' => array(
                'course_id' => $course_id,
                'modules' => $reordered_modules,
                'updated_count' => $updated_count,
                'total_modules' => count($reordered_modules)
            )
        ), 200);

    } catch (Exception $e) {
        error_log('ASG Error en asg_reorder_course_modules: ' . $e->getMessage());
        return new WP_REST_Response(array(
            'success' => false,
            'error' => array(
                'code' => 'REORDER_ERROR',
                'message' => 'Error reordering modules: ' . $e->getMessage()
            )
        ), 500);
    }
}

/**
 * Actualizar objetivos de aprendizaje de un curso
 */
function asg_update_course_objectives($request) {
    $course_id = intval($request['id']);
    $data = $request->get_json_params();

    if (!$course_id) {
        return new WP_REST_Response(array(
            'success' => false,
            'error' => array(
                'code' => 'INVALID_COURSE_ID',
                'message' => 'Valid course ID is required'
            )
        ), 400);
    }

    if (!isset($data['objectives']) || !is_array($data['objectives'])) {
        return new WP_REST_Response(array(
            'success' => false,
            'error' => array(
                'code' => 'INVALID_OBJECTIVES_DATA',
                'message' => 'Objectives array is required'
            )
        ), 400);
    }

    try {
        global $wpdb;
        $table_courses = $wpdb->prefix . 'asg_courses';
        $table_objectives = $wpdb->prefix . 'asg_course_objectives';

        // Verificar que el curso existe
        $course_exists = $wpdb->get_var($wpdb->prepare(
            "SELECT id FROM {$table_courses} WHERE id = %d",
            $course_id
        ));

        if (!$course_exists) {
            return new WP_REST_Response(array(
                'success' => false,
                'error' => array(
                    'code' => 'COURSE_NOT_FOUND',
                    'message' => 'Course not found'
                )
            ), 404);
        }

        // Eliminar objetivos existentes
        $wpdb->delete($table_objectives, array('course_id' => $course_id), array('%d'));

        // Insertar nuevos objetivos
        foreach ($data['objectives'] as $index => $objective) {
            if (!empty($objective['objective'])) {
                $wpdb->insert(
                    $table_objectives,
                    array(
                        'course_id' => $course_id,
                        'objective' => sanitize_text_field($objective['objective']),
                        'order_index' => $index
                    ),
                    array('%d', '%s', '%d')
                );
            }
        }

        // Actualizar timestamp del curso
        $wpdb->update(
            $table_courses,
            array('updated_at' => current_time('mysql')),
            array('id' => $course_id),
            array('%s'),
            array('%d')
        );

        // Obtener objetivos actualizados
        $updated_objectives = asg_get_course_objectives($course_id);

        return new WP_REST_Response(array(
            'success' => true,
            'data' => array(
                'course_id' => $course_id,
                'objectives' => $updated_objectives,
                'total_objectives' => count($updated_objectives)
            )
        ), 200);

    } catch (Exception $e) {
        error_log('ASG Error en asg_update_course_objectives: ' . $e->getMessage());
        return new WP_REST_Response(array(
            'success' => false,
            'error' => array(
                'code' => 'OBJECTIVES_UPDATE_ERROR',
                'message' => 'Error updating objectives: ' . $e->getMessage()
            )
        ), 500);
    }
}

/**
 * Actualizar beneficios de un curso
 */
function asg_update_course_benefits($request) {
    $course_id = intval($request['id']);
    $data = $request->get_json_params();

    if (!$course_id) {
        return new WP_REST_Response(array(
            'success' => false,
            'error' => array(
                'code' => 'INVALID_COURSE_ID',
                'message' => 'Valid course ID is required'
            )
        ), 400);
    }

    if (!isset($data['benefits']) || !is_array($data['benefits'])) {
        return new WP_REST_Response(array(
            'success' => false,
            'error' => array(
                'code' => 'INVALID_BENEFITS_DATA',
                'message' => 'Benefits array is required'
            )
        ), 400);
    }

    try {
        global $wpdb;
        $table_courses = $wpdb->prefix . 'asg_courses';
        $table_benefits = $wpdb->prefix . 'asg_course_benefits';

        // Verificar que el curso existe
        $course_exists = $wpdb->get_var($wpdb->prepare(
            "SELECT id FROM {$table_courses} WHERE id = %d",
            $course_id
        ));

        if (!$course_exists) {
            return new WP_REST_Response(array(
                'success' => false,
                'error' => array(
                    'code' => 'COURSE_NOT_FOUND',
                    'message' => 'Course not found'
                )
            ), 404);
        }

        // Eliminar beneficios existentes
        $wpdb->delete($table_benefits, array('course_id' => $course_id), array('%d'));

        // Insertar nuevos beneficios
        foreach ($data['benefits'] as $index => $benefit) {
            if (!empty($benefit['benefit'])) {
                $wpdb->insert(
                    $table_benefits,
                    array(
                        'course_id' => $course_id,
                        'benefit' => sanitize_text_field($benefit['benefit']),
                        'order_index' => $index
                    ),
                    array('%d', '%s', '%d')
                );
            }
        }

        // Actualizar timestamp del curso
        $wpdb->update(
            $table_courses,
            array('updated_at' => current_time('mysql')),
            array('id' => $course_id),
            array('%s'),
            array('%d')
        );

        // Obtener beneficios actualizados
        $updated_benefits = asg_get_course_benefits($course_id);

        return new WP_REST_Response(array(
            'success' => true,
            'data' => array(
                'course_id' => $course_id,
                'benefits' => $updated_benefits,
                'total_benefits' => count($updated_benefits)
            )
        ), 200);

    } catch (Exception $e) {
        error_log('ASG Error en asg_update_course_benefits: ' . $e->getMessage());
        return new WP_REST_Response(array(
            'success' => false,
            'error' => array(
                'code' => 'BENEFITS_UPDATE_ERROR',
                'message' => 'Error updating benefits: ' . $e->getMessage()
            )
        ), 500);
    }
}

/**
 * Duplicar un curso
 */
function asg_duplicate_course($request) {
    $course_id = intval($request['id']);

    if (!$course_id) {
        return new WP_REST_Response(array(
            'success' => false,
            'error' => array(
                'code' => 'INVALID_COURSE_ID',
                'message' => 'Valid course ID is required'
            )
        ), 400);
    }

    try {
        global $wpdb;
        $table_courses = $wpdb->prefix . 'asg_courses';

        // Obtener curso original
        $original_course = $wpdb->get_row($wpdb->prepare(
            "SELECT * FROM {$table_courses} WHERE id = %d",
            $course_id
        ), ARRAY_A);

        if (!$original_course) {
            return new WP_REST_Response(array(
                'success' => false,
                'error' => array(
                    'code' => 'COURSE_NOT_FOUND',
                    'message' => 'Original course not found'
                )
            ), 404);
        }

        // Obtener post original
        $original_post = get_post($original_course['post_id']);
        if (!$original_post) {
            throw new Exception('Original post not found');
        }

        // Crear nuevo post
        $new_post_data = array(
            'post_title' => $original_post->post_title . ' (Copy)',
            'post_content' => $original_post->post_content,
            'post_status' => 'draft',
            'post_type' => 'course',
            'post_author' => get_current_user_id() ?: 1
        );

        $new_post_id = wp_insert_post($new_post_data);
        if (is_wp_error($new_post_id)) {
            throw new Exception('Failed to create new post: ' . $new_post_id->get_error_message());
        }

        // Crear nuevo curso
        $new_course_data = $original_course;
        unset($new_course_data['id']);
        $new_course_data['post_id'] = $new_post_id;
        $new_course_data['status'] = 'draft';
        $new_course_data['created_at'] = current_time('mysql');
        $new_course_data['updated_at'] = current_time('mysql');

        $result = $wpdb->insert($table_courses, $new_course_data);
        if ($result === false) {
            wp_delete_post($new_post_id, true);
            throw new Exception('Failed to create new course record');
        }

        $new_course_id = $wpdb->insert_id;

        // Duplicar módulos
        $original_modules = asg_get_course_modules($course_id);
        if (!empty($original_modules)) {
            asg_insert_course_modules($new_course_id, $original_modules);
        }

        // Duplicar objetivos
        $original_objectives = asg_get_course_objectives($course_id);
        if (!empty($original_objectives)) {
            asg_insert_course_objectives($new_course_id, $original_objectives);
        }

        // Duplicar beneficios
        $original_benefits = asg_get_course_benefits($course_id);
        if (!empty($original_benefits)) {
            asg_insert_course_benefits($new_course_id, $original_benefits);
        }

        // Obtener curso duplicado completo
        $duplicated_course = asg_get_course_by_id($new_course_id);

        return new WP_REST_Response(array(
            'success' => true,
            'data' => array(
                'original_course_id' => $course_id,
                'new_course' => $duplicated_course,
                'message' => 'Course duplicated successfully'
            )
        ), 201);

    } catch (Exception $e) {
        error_log('ASG Error en asg_duplicate_course: ' . $e->getMessage());
        return new WP_REST_Response(array(
            'success' => false,
            'error' => array(
                'code' => 'DUPLICATE_ERROR',
                'message' => 'Error duplicating course: ' . $e->getMessage()
            )
        ), 500);
    }
}

/**
 * Cambiar estado específico de un curso
 */
function asg_update_course_status($request) {
    $course_id = intval($request['id']);
    $data = $request->get_json_params();

    if (!$course_id) {
        return new WP_REST_Response(array(
            'success' => false,
            'error' => array(
                'code' => 'INVALID_COURSE_ID',
                'message' => 'Valid course ID is required'
            )
        ), 400);
    }

    if (empty($data['status'])) {
        return new WP_REST_Response(array(
            'success' => false,
            'error' => array(
                'code' => 'MISSING_STATUS',
                'message' => 'Status is required'
            )
        ), 400);
    }

    $new_status = sanitize_text_field($data['status']);
    $allowed_statuses = array('draft', 'published', 'archived');

    if (!in_array($new_status, $allowed_statuses)) {
        return new WP_REST_Response(array(
            'success' => false,
            'error' => array(
                'code' => 'INVALID_STATUS',
                'message' => 'Invalid status. Allowed: ' . implode(', ', $allowed_statuses)
            )
        ), 400);
    }

    try {
        global $wpdb;
        $table_courses = $wpdb->prefix . 'asg_courses';

        // Verificar que el curso existe
        $course = $wpdb->get_row($wpdb->prepare(
            "SELECT id, post_id, status FROM {$table_courses} WHERE id = %d",
            $course_id
        ));

        if (!$course) {
            return new WP_REST_Response(array(
                'success' => false,
                'error' => array(
                    'code' => 'COURSE_NOT_FOUND',
                    'message' => 'Course not found'
                )
            ), 404);
        }

        $old_status = $course->status;

        // Actualizar estado del curso
        $result = $wpdb->update(
            $table_courses,
            array(
                'status' => $new_status,
                'updated_at' => current_time('mysql')
            ),
            array('id' => $course_id),
            array('%s', '%s'),
            array('%d')
        );

        if ($result === false) {
            throw new Exception('Failed to update course status');
        }

        // Actualizar estado del post
        $post_status = ($new_status === 'published') ? 'publish' : 'draft';
        wp_update_post(array(
            'ID' => $course->post_id,
            'post_status' => $post_status
        ));

        return new WP_REST_Response(array(
            'success' => true,
            'data' => array(
                'course_id' => $course_id,
                'old_status' => $old_status,
                'new_status' => $new_status,
                'updated_at' => current_time('mysql'),
                'message' => "Course status changed from {$old_status} to {$new_status}"
            )
        ), 200);

    } catch (Exception $e) {
        error_log('ASG Error en asg_update_course_status: ' . $e->getMessage());
        return new WP_REST_Response(array(
            'success' => false,
            'error' => array(
                'code' => 'STATUS_UPDATE_ERROR',
                'message' => 'Error updating course status: ' . $e->getMessage()
            )
        ), 500);
    }
}

/**
 * Obtener historial de cambios del curso (simulado)
 */
function asg_get_course_history($request) {
    $course_id = intval($request['id']);

    if (!$course_id) {
        return new WP_REST_Response(array(
            'success' => false,
            'error' => array(
                'code' => 'INVALID_COURSE_ID',
                'message' => 'Valid course ID is required'
            )
        ), 400);
    }

    try {
        global $wpdb;
        $table_courses = $wpdb->prefix . 'asg_courses';

        // Verificar que el curso existe
        $course = $wpdb->get_row($wpdb->prepare(
            "SELECT * FROM {$table_courses} WHERE id = %d",
            $course_id
        ));

        if (!$course) {
            return new WP_REST_Response(array(
                'success' => false,
                'error' => array(
                    'code' => 'COURSE_NOT_FOUND',
                    'message' => 'Course not found'
                )
            ), 404);
        }

        // Simular historial de cambios (en una implementación real, esto vendría de una tabla de auditoría)
        $history = array(
            array(
                'id' => 1,
                'action' => 'created',
                'description' => 'Course created',
                'user_id' => $course->post_id ? get_post_field('post_author', $course->post_id) : 1,
                'user_name' => 'Admin User',
                'timestamp' => $course->created_at,
                'details' => array(
                    'initial_status' => 'draft',
                    'initial_title' => get_the_title($course->post_id)
                )
            ),
            array(
                'id' => 2,
                'action' => 'updated',
                'description' => 'Course information updated',
                'user_id' => 1,
                'user_name' => 'Admin User',
                'timestamp' => $course->updated_at,
                'details' => array(
                    'current_status' => $course->status,
                    'current_price' => $course->price,
                    'current_category' => $course->category
                )
            )
        );

        // Obtener información adicional del curso
        $modules_count = $wpdb->get_var($wpdb->prepare(
            "SELECT COUNT(*) FROM {$wpdb->prefix}asg_course_modules WHERE course_id = %d",
            $course_id
        ));

        $objectives_count = $wpdb->get_var($wpdb->prepare(
            "SELECT COUNT(*) FROM {$wpdb->prefix}asg_course_objectives WHERE course_id = %d",
            $course_id
        ));

        return new WP_REST_Response(array(
            'success' => true,
            'data' => array(
                'course_id' => $course_id,
                'history' => $history,
                'summary' => array(
                    'total_changes' => count($history),
                    'created_at' => $course->created_at,
                    'last_updated' => $course->updated_at,
                    'current_status' => $course->status,
                    'modules_count' => intval($modules_count),
                    'objectives_count' => intval($objectives_count)
                )
            )
        ), 200);

    } catch (Exception $e) {
        error_log('ASG Error en asg_get_course_history: ' . $e->getMessage());
        return new WP_REST_Response(array(
            'success' => false,
            'error' => array(
                'code' => 'HISTORY_ERROR',
                'message' => 'Error getting course history: ' . $e->getMessage()
            )
        ), 500);
    }
}

/**
 * ========================================
 * FUNCIONES AUXILIARES ADICIONALES
 * ========================================
 */

/**
 * Insertar objetivos de aprendizaje
 */
function asg_insert_course_objectives($course_id, $objectives) {
    global $wpdb;

    $table_objectives = $wpdb->prefix . 'asg_course_objectives';

    foreach ($objectives as $index => $objective) {
        if (!empty($objective['objective'])) {
            $wpdb->insert(
                $table_objectives,
                array(
                    'course_id' => $course_id,
                    'objective' => sanitize_text_field($objective['objective']),
                    'order_index' => $index
                ),
                array('%d', '%s', '%d')
            );
        }
    }
}

/**
 * Insertar beneficios del curso
 */
function asg_insert_course_benefits($course_id, $benefits) {
    global $wpdb;

    $table_benefits = $wpdb->prefix . 'asg_course_benefits';

    foreach ($benefits as $index => $benefit) {
        if (!empty($benefit['benefit'])) {
            $wpdb->insert(
                $table_benefits,
                array(
                    'course_id' => $course_id,
                    'benefit' => sanitize_text_field($benefit['benefit']),
                    'order_index' => $index
                ),
                array('%d', '%s', '%d')
            );
        }
    }
}

/**
 * Insertar objetivos de aprendizaje
 * 
 * @param int $course_id
 * @param array $objectives
 */
function asg_insert_learn_objectives($course_id, $objectives) {
    global $wpdb;
    
    $table_objectives = $wpdb->prefix . 'asg_course_learn_objectives';
    
    foreach ($objectives as $index => $objective) {
        if (!empty($objective)) {
            $wpdb->insert(
                $table_objectives,
                array(
                    'course_id' => $course_id,
                    'objective' => sanitize_textarea_field($objective),
                    'order_index' => $index
                ),
                array('%d', '%s', '%d')
            );
        }
    }
    
    error_log('ASG: Insertados ' . count($objectives) . ' objetivos para curso ' . $course_id);
}



/**
 * Obtener módulos del curso
 * 
 * @param int $course_id
 * @return array
 */
function asg_get_course_modules($course_id) {
    global $wpdb;
    
    $table_modules = $wpdb->prefix . 'asg_course_modules';
    
    $modules = $wpdb->get_results($wpdb->prepare(
        "SELECT * FROM {$table_modules} WHERE course_id = %d ORDER BY order_index ASC",
        $course_id
    ), ARRAY_A);
    
    return $modules ?: array();
}

/**
 * Obtener objetivos del curso
 * 
 * @param int $course_id
 * @return array
 */
function asg_get_course_objectives($course_id) {
    global $wpdb;
    
    $table_objectives = $wpdb->prefix . 'asg_course_learn_objectives';
    
    $objectives = $wpdb->get_results($wpdb->prepare(
        "SELECT * FROM {$table_objectives} WHERE course_id = %d ORDER BY order_index ASC",
        $course_id
    ), ARRAY_A);
    
    return $objectives ?: array();
}

/**
 * Obtener beneficios del curso
 * 
 * @param int $course_id
 * @return array
 */
function asg_get_course_benefits($course_id) {
    global $wpdb;
    
    $table_benefits = $wpdb->prefix . 'asg_course_benefits';
    
    $benefits = $wpdb->get_results($wpdb->prepare(
        "SELECT * FROM {$table_benefits} WHERE course_id = %d ORDER BY order_index ASC",
        $course_id
    ), ARRAY_A);
    
    return $benefits ?: array();
}

/**
 * Actualizar módulos del curso
 * 
 * @param int $course_id
 * @param array $modules
 */
function asg_update_course_modules($course_id, $modules) {
    global $wpdb;
    
    $table_modules = $wpdb->prefix . 'asg_course_modules';
    
    // Eliminar módulos existentes
    $wpdb->delete($table_modules, array('course_id' => $course_id), array('%d'));
    
    // Insertar nuevos módulos
    asg_insert_course_modules($course_id, $modules);
}



/**
 * ========================================
 * LOGGING Y DEBUG
 * ========================================
 */

// Habilitar logging para desarrollo
if (!function_exists('asg_log')) {
    function asg_log($message, $level = 'info') {
        if (WP_DEBUG && WP_DEBUG_LOG) {
            error_log("ASG [{$level}]: " . $message);
        }
    }
}

// Log de inicialización
asg_log('Endpoints ASG cargados correctamente - Versión 1.0.2', 'info');

/**
 * ========================================
 * ENDPOINT DE INFORMACIÓN DEL SISTEMA
 * ========================================
 */

/**
 * Obtener información del sistema ASG
 */
function asg_get_system_info($request) {
    global $wpdb;

    try {
        error_log('ASG: Obteniendo información del sistema');

        // Información básica del sistema
        $system_info = array(
            'name' => 'AbilitySeminarsGroup Course Management System',
            'version' => '2.0.0',
            'description' => 'Sistema de gestión de cursos para AbilitySeminarsGroup',
            'author' => 'ASG Development Team',
            'api_version' => 'v1',
            'wordpress_version' => get_bloginfo('version'),
            'php_version' => PHP_VERSION,
            'mysql_version' => $wpdb->db_version(),
            'site_url' => get_site_url(),
            'api_base_url' => get_rest_url(null, 'asg/v1'),
            'timestamp' => current_time('mysql'),
            'timezone' => get_option('timezone_string') ?: 'UTC'
        );

        // Estadísticas de la base de datos
        $table_courses = $wpdb->prefix . 'courses';
        $table_modules = $wpdb->prefix . 'modules';
        $table_lessons = $wpdb->prefix . 'lessons';

        $database_stats = array(
            'total_courses' => (int) $wpdb->get_var("SELECT COUNT(*) FROM {$table_courses}"),
            'published_courses' => (int) $wpdb->get_var("SELECT COUNT(*) FROM {$table_courses} WHERE status_course = 'published'"),
            'draft_courses' => (int) $wpdb->get_var("SELECT COUNT(*) FROM {$table_courses} WHERE status_course = 'draft'"),
            'total_modules' => (int) $wpdb->get_var("SELECT COUNT(*) FROM {$table_modules}"),
            'total_lessons' => (int) $wpdb->get_var("SELECT COUNT(*) FROM {$table_lessons}")
        );

        // Verificar estado de las tablas
        $tables_status = array();
        $required_tables = array('courses', 'modules', 'lessons', 'course_meta', 'benefit_list', 'learn_list');

        foreach ($required_tables as $table) {
            $full_table_name = $wpdb->prefix . $table;
            $exists = $wpdb->get_var("SHOW TABLES LIKE '{$full_table_name}'");
            $tables_status[$table] = array(
                'exists' => !empty($exists),
                'full_name' => $full_table_name
            );
        }

        // Endpoints disponibles
        $available_endpoints = array(
            'courses' => array(
                'GET /courses' => 'Listar todos los cursos',
                'POST /courses' => 'Crear nuevo curso',
                'GET /courses/{id}' => 'Obtener curso específico',
                'PUT /courses/{id}' => 'Actualizar curso',
                'DELETE /courses/{id}' => 'Eliminar curso'
            ),
            'dashboard' => array(
                'GET /dashboard/stats' => 'Estadísticas del dashboard'
            ),
            'system' => array(
                'GET /info' => 'Información del sistema'
            )
        );

        return new WP_REST_Response(array(
            'success' => true,
            'data' => array(
                'system' => $system_info,
                'database' => $database_stats,
                'tables' => $tables_status,
                'endpoints' => $available_endpoints
            ),
            'message' => 'Información del sistema obtenida correctamente'
        ), 200);

    } catch (Exception $e) {
        error_log('ASG Error en get_system_info: ' . $e->getMessage());

        return new WP_REST_Response(array(
            'success' => false,
            'data' => null,
            'message' => 'Error al obtener información del sistema: ' . $e->getMessage()
        ), 500);
    }
}

