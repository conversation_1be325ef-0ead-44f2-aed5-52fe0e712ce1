/**
 * ========================================
 * CLIENTE JAVASCRIPT - API CURSOS ASG
 * ========================================
 *
 * Descripción: Cliente JavaScript simplificado para conectar
 * con los endpoints del sistema de cursos ASG.
 *
 * Estructura: Clase simple con métodos para cada endpoint
 * Compatible con: Vanilla JS, sin dependencias
 *
 * Autor: ASG Team
 * Fecha: 2025-01-03
 * Versión: 1.0.0
 */

class ASG_CourseAPI {

    constructor() {
        // URL base de la API - AbilitySeminarsGroup
        this.baseURL = 'https://abilityseminarsgroup.com/wp-json/asg/v1';

        // Configuración por defecto para fetch
        this.defaultConfig = {
            headers: {
                'Content-Type': 'application/json',
                'X-WP-Nonce': wpApiSettings?.nonce || ''
            }
        };

        console.log('🚀 ASG Course API inicializado - Base URL:', this.baseURL);
    }
    
    /**
     * ===== ENDPOINTS PRINCIPALES =====
     */

    /**
     * Crear nuevo curso
     *
     * @param {Object} courseData Datos del curso
     * @returns {Promise} Respuesta de la API
     */
    async createCourse(courseData) {
        console.log('📝 ASG: Creando curso...', courseData);

        return await this.makeRequest('POST', '/courses', courseData);
    }
    
    /**
     * Guardar curso como borrador
     *
     * @param {Object} courseData Datos del curso
     * @returns {Promise} Respuesta de la API
     */
    async saveDraft(courseData) {
        console.log('💾 ASG: Guardando borrador...', courseData);

        return await this.makeRequest('POST', '/courses/draft', courseData);
    }
    
    /**
     * Subir imagen
     *
     * @param {File} file Archivo de imagen
     * @param {string} type Tipo de imagen (course, module)
     * @returns {Promise} Respuesta de la API
     */
    async uploadImage(file, type = 'course') {
        console.log('📸 ASG: Subiendo imagen...', file.name, 'tipo:', type);

        const formData = new FormData();
        formData.append('image', file);
        formData.append('type', type);

        return await this.makeRequest('POST', '/courses/upload-image', formData, true);
    }
    
    /**
     * Obtener categorías disponibles
     *
     * @returns {Promise} Lista de categorías
     */
    async getCategories() {
        console.log('📋 ASG: Obteniendo categorías...');

        return await this.makeRequest('GET', '/courses/categories');
    }
    
    /**
     * Validar datos del curso
     *
     * @param {Object} courseData Datos a validar
     * @returns {Promise} Resultado de la validación
     */
    async validateCourse(courseData) {
        console.log('✅ ASG: Validando curso...', courseData);

        return await this.makeRequest('POST', '/courses/validate', courseData);
    }

    /**
     * Obtener todos los cursos
     *
     * @param {Object} params Parámetros de filtrado y paginación
     * @returns {Promise} Lista de cursos
     */
    async getAllCourses(params = {}) {
        console.log('📚 ASG: Obteniendo todos los cursos...', params);

        const queryString = new URLSearchParams(params).toString();
        const endpoint = queryString ? `/courses?${queryString}` : '/courses';

        return await this.makeRequest('GET', endpoint);
    }
    
    /**
     * ===== FUNCIONES AUXILIARES =====
     */

    /**
     * Realizar petición HTTP genérica
     *
     * @param {string} method Método HTTP
     * @param {string} endpoint Endpoint de la API
     * @param {Object|FormData} data Datos a enviar
     * @param {boolean} isFormData Si los datos son FormData
     * @returns {Promise} Respuesta de la API
     */
    async makeRequest(method, endpoint, data = null, isFormData = false) {
        try {
            const config = {
                method: method,
                headers: isFormData ?
                    { 'X-WP-Nonce': wpApiSettings?.nonce || '' } :
                    this.defaultConfig.headers
            };

            if (data && method !== 'GET') {
                config.body = isFormData ? data : JSON.stringify(data);
            }

            const response = await fetch(`${this.baseURL}${endpoint}`, config);
            const result = await response.json();

            if (!response.ok) {
                this.handleError(result);
                throw new Error(result.error?.message || 'Error en la API');
            }

            if (result.success) {
                this.showSuccessMessage(result.message);
            }

            return result;

        } catch (error) {
            console.error('❌ ASG API Error:', error);
            throw error;
        }
    }
    
    /**
     * Manejar errores de la API
     *
     * @param {Object} errorData Datos del error
     */
    handleError(errorData) {
        if (errorData.error && errorData.error.details) {
            this.showValidationErrors(errorData.error.details);
        } else {
            this.showErrorMessage(errorData.error?.message || 'Error desconocido');
        }
    }

    /**
     * Mostrar mensaje de éxito
     *
     * @param {string} message Mensaje a mostrar
     */
    showSuccessMessage(message) {
        if (message) {
            console.log('✅ ASG:', message);

            // Implementar según tu sistema de notificaciones
            if (typeof showToast === 'function') {
                showToast(message, 'success');
            }
        }
    }

    /**
     * Mostrar mensaje de error
     *
     * @param {string} message Mensaje de error
     */
    showErrorMessage(message) {
        console.error('❌ ASG:', message);

        // Implementar según tu sistema de notificaciones
        if (typeof showToast === 'function') {
            showToast(message, 'error');
        } else {
            alert('Error: ' + message);
        }
    }
    
    /**
     * Mostrar errores de validación
     *
     * @param {Object} errors Errores de validación
     */
    showValidationErrors(errors) {
        console.error('❌ ASG: Errores de validación:', errors);

        // Limpiar errores anteriores
        this.clearValidationErrors();

        // Mostrar nuevos errores
        Object.keys(errors).forEach(field => {
            const errorElement = document.querySelector(`#${field}-error`);
            const inputElement = document.querySelector(`[name="${field}"]`);

            if (errorElement) {
                errorElement.textContent = errors[field];
                errorElement.style.display = 'block';
                errorElement.classList.add('text-danger');
            }

            if (inputElement) {
                inputElement.classList.add('is-invalid');
            }
        });
    }

    /**
     * Limpiar errores de validación
     */
    clearValidationErrors() {
        document.querySelectorAll('.field-error').forEach(el => {
            el.textContent = '';
            el.style.display = 'none';
            el.classList.remove('text-danger');
        });

        document.querySelectorAll('.is-invalid').forEach(el => {
            el.classList.remove('is-invalid');
        });
    }
}

/**
 * ========================================
 * INICIALIZACIÓN Y EJEMPLOS DE USO
 * ========================================
 */

// Crear instancia global
window.courseAPI = new ASG_CourseAPI();

// Ejemplo de uso básico:
/*
// 1. Crear curso
const courseData = {
    title: 'Mi Nuevo Curso',
    description: 'Descripción completa del curso...',
    price: 99.99,
    category: 'business',
    level: 'beginner',
    duration: 10.5,
    modules: [
        {
            title: 'Módulo 1: Introducción',
            description: 'Descripción del módulo',
            duration: 60
        }
    ],
    learn_objectives: [
        'Aprender conceptos básicos',
        'Dominar técnicas avanzadas'
    ],
    benefits: [
        'Certificación incluida',
        'Acceso de por vida'
    ]
};

// Crear curso publicado
courseAPI.createCourse(courseData)
    .then(result => console.log('Curso creado:', result))
    .catch(error => console.error('Error:', error));

// Guardar como borrador
courseAPI.saveDraft(courseData)
    .then(result => console.log('Borrador guardado:', result))
    .catch(error => console.error('Error:', error));

// 2. Subir imagen
const fileInput = document.querySelector('#course-image');
if (fileInput && fileInput.files[0]) {
    courseAPI.uploadImage(fileInput.files[0], 'course')
        .then(result => console.log('Imagen subida:', result))
        .catch(error => console.error('Error:', error));
}

// 3. Obtener categorías
courseAPI.getCategories()
    .then(result => console.log('Categorías:', result.data))
    .catch(error => console.error('Error:', error));

// 4. Listar cursos
courseAPI.getAllCourses({ page: 1, per_page: 10, status: 'published' })
    .then(result => console.log('Cursos:', result.data))
    .catch(error => console.error('Error:', error));

// 5. Validar datos
courseAPI.validateCourse({ title: 'Test', price: 'invalid' })
    .then(result => console.log('Validación:', result))
    .catch(error => console.error('Errores de validación:', error));
*/
