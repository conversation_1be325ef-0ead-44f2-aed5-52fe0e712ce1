/**
 * ========================================
 * NEW COURSE v2.0 - CREACIÓN MODERNA
 * ========================================
 * 
 * Descripción: Formulario multi-paso para crear cursos completos
 * Incluye: Validaciones, preview en tiempo real, auto-guardado
 * 
 * Autor: ASG Team
 * Fecha: 2025-01-04
 * Versión: 2.0.0 - FORMULARIO MODERNO
 */

/**
 * ========================================
 * CLASE PRINCIPAL DE CREACIÓN DE CURSOS
 * ========================================
 */
class ASGNewCourse {
    constructor() {
        this.apiUrl = ASG.config.apiBaseUrl;
        this.currentStep = 1;
        this.totalSteps = 4;
        this.courseData = {
            basicInfo: {},
            content: { modules: [], objectives: [], benefits: [] },
            configuration: {},
            review: {}
        };
        this.validationRules = {};
        this.autoSaveInterval = null;
        
        // Elementos DOM
        this.elements = {};
        
        this.init();
    }
    
    init() {
        console.log('➕ Inicializando New Course v2.0...');
        
        this.cacheElements();
        this.setupValidationRules();
        this.setupEventListeners();
        this.setupAutoSave();
        this.updatePreview();
        
        console.log('✅ New Course inicializado correctamente');
    }
    
    cacheElements() {
        this.elements = {
            // Form and steps
            form: document.getElementById('courseForm'),
            steps: document.querySelectorAll('.form-step'),
            stepIndicators: document.querySelectorAll('.step'),
            
            // Navigation
            prevBtn: document.getElementById('prevStepBtn'),
            nextBtn: document.getElementById('nextStepBtn'),
            publishBtn: document.getElementById('publishBtn'),
            saveDraftBtn: document.getElementById('saveDraftBtn'),
            
            // Form fields
            courseName: document.getElementById('courseName'),
            courseDescription: document.getElementById('courseDescription'),
            courseCategory: document.getElementById('courseCategory'),
            courseLanguage: document.getElementById('courseLanguage'),
            courseImageInput: document.getElementById('courseImageInput'),
            
            // Preview elements
            previewTitle: document.getElementById('previewTitle'),
            previewDescription: document.getElementById('previewDescription'),
            previewCategory: document.getElementById('previewCategory'),
            previewPrice: document.getElementById('previewPrice'),
            previewModules: document.getElementById('previewModules'),
            previewLessons: document.getElementById('previewLessons'),
            previewLanguage: document.getElementById('previewLanguage'),
            previewImage: document.querySelector('.preview-image'),
            
            // Counters
            descriptionCount: document.getElementById('descriptionCount')
        };
    }
    
    setupValidationRules() {
        this.validationRules = {
            step1: {
                courseName: {
                    required: true,
                    minLength: 3,
                    maxLength: 255,
                    message: 'El título debe tener entre 3 y 255 caracteres'
                },
                courseDescription: {
                    required: true,
                    minLength: 10,
                    maxLength: 1000,
                    message: 'La descripción debe tener entre 10 y 1000 caracteres'
                },
                courseCategory: {
                    required: true,
                    message: 'Debes seleccionar una categoría'
                }
            }
        };
    }
    
    setupEventListeners() {
        // Navigation buttons
        if (this.elements.prevBtn) {
            this.elements.prevBtn.addEventListener('click', () => this.previousStep());
        }
        
        if (this.elements.nextBtn) {
            this.elements.nextBtn.addEventListener('click', () => this.nextStep());
        }
        
        if (this.elements.publishBtn) {
            this.elements.publishBtn.addEventListener('click', () => this.publishCourse());
        }
        
        if (this.elements.saveDraftBtn) {
            this.elements.saveDraftBtn.addEventListener('click', () => this.saveDraft());
        }
        
        // Form field listeners for real-time preview
        if (this.elements.courseName) {
            this.elements.courseName.addEventListener('input', () => {
                this.updatePreview();
                this.validateField('courseName');
            });
        }
        
        if (this.elements.courseDescription) {
            this.elements.courseDescription.addEventListener('input', () => {
                this.updateDescriptionCounter();
                this.updatePreview();
                this.validateField('courseDescription');
            });
        }
        
        if (this.elements.courseCategory) {
            this.elements.courseCategory.addEventListener('change', () => {
                this.updatePreview();
                this.validateField('courseCategory');
            });
        }
        
        if (this.elements.courseLanguage) {
            this.elements.courseLanguage.addEventListener('change', () => {
                this.updatePreview();
            });
        }
        
        // Form submission
        if (this.elements.form) {
            this.elements.form.addEventListener('submit', (e) => {
                e.preventDefault();
                this.handleFormSubmit();
            });
        }
        
        // Prevent accidental navigation away
        window.addEventListener('beforeunload', (e) => {
            if (this.hasUnsavedChanges()) {
                e.preventDefault();
                e.returnValue = '';
            }
        });
    }
    
    setupAutoSave() {
        // Auto-save every 30 seconds
        this.autoSaveInterval = setInterval(() => {
            if (this.hasUnsavedChanges()) {
                this.autoSave();
            }
        }, 30000);
    }
    
    updateDescriptionCounter() {
        if (this.elements.courseDescription && this.elements.descriptionCount) {
            const length = this.elements.courseDescription.value.length;
            this.elements.descriptionCount.textContent = length;
            
            // Change color based on length
            if (length > 900) {
                this.elements.descriptionCount.style.color = 'var(--asg-error)';
            } else if (length > 800) {
                this.elements.descriptionCount.style.color = 'var(--asg-warning)';
            } else {
                this.elements.descriptionCount.style.color = 'var(--asg-gray-500)';
            }
        }
    }
    
    updatePreview() {
        // Update title
        if (this.elements.previewTitle && this.elements.courseName) {
            const title = this.elements.courseName.value.trim();
            this.elements.previewTitle.textContent = title || 'Título del curso';
            this.elements.previewTitle.style.color = title ? 'var(--asg-gray-900)' : 'var(--asg-gray-500)';
        }
        
        // Update description
        if (this.elements.previewDescription && this.elements.courseDescription) {
            const description = this.elements.courseDescription.value.trim();
            this.elements.previewDescription.textContent = description || 'Descripción del curso...';
            this.elements.previewDescription.style.color = description ? 'var(--asg-gray-600)' : 'var(--asg-gray-500)';
        }
        
        // Update category
        if (this.elements.previewCategory && this.elements.courseCategory) {
            const category = this.elements.courseCategory.value;
            const categoryLabels = {
                finanzas: 'Finanzas',
                marketing: 'Marketing',
                'desarrollo-personal': 'Desarrollo Personal',
                tecnologia: 'Tecnología',
                negocios: 'Negocios',
                salud: 'Salud y Bienestar',
                educacion: 'Educación',
                arte: 'Arte y Creatividad'
            };
            
            this.elements.previewCategory.textContent = categoryLabels[category] || 'Categoría';
            this.elements.previewCategory.className = category ? 'asg-badge asg-badge-primary' : 'asg-badge asg-badge-gray';
        }
        
        // Update language
        if (this.elements.previewLanguage && this.elements.courseLanguage) {
            const language = this.elements.courseLanguage.value;
            const languageLabels = {
                es: 'Español',
                en: 'Inglés',
                fr: 'Francés',
                pt: 'Portugués'
            };
            
            this.elements.previewLanguage.textContent = languageLabels[language] || 'Español';
        }
    }
    
    validateField(fieldName) {
        const field = this.elements[fieldName];
        if (!field) return true;
        
        const rules = this.validationRules[`step${this.currentStep}`]?.[fieldName];
        if (!rules) return true;
        
        const value = field.value.trim();
        let isValid = true;
        let errorMessage = '';
        
        // Required validation
        if (rules.required && !value) {
            isValid = false;
            errorMessage = 'Este campo es obligatorio';
        }
        
        // Length validations
        if (isValid && rules.minLength && value.length < rules.minLength) {
            isValid = false;
            errorMessage = `Mínimo ${rules.minLength} caracteres`;
        }
        
        if (isValid && rules.maxLength && value.length > rules.maxLength) {
            isValid = false;
            errorMessage = `Máximo ${rules.maxLength} caracteres`;
        }
        
        // Update field appearance
        this.updateFieldValidation(field, isValid, errorMessage);
        
        return isValid;
    }
    
    updateFieldValidation(field, isValid, errorMessage) {
        // Remove existing validation classes
        field.classList.remove('field-valid', 'field-invalid');
        
        // Remove existing error message
        const existingError = field.parentNode.querySelector('.field-error');
        if (existingError) {
            existingError.remove();
        }
        
        if (field.value.trim()) {
            if (isValid) {
                field.classList.add('field-valid');
                field.style.borderColor = 'var(--asg-success)';
            } else {
                field.classList.add('field-invalid');
                field.style.borderColor = 'var(--asg-error)';
                
                // Add error message
                const errorDiv = document.createElement('div');
                errorDiv.className = 'field-error';
                errorDiv.style.cssText = `
                    margin-top: var(--asg-space-1);
                    font-size: var(--asg-text-xs);
                    color: var(--asg-error);
                `;
                errorDiv.textContent = errorMessage;
                field.parentNode.appendChild(errorDiv);
            }
        } else {
            field.style.borderColor = '';
        }
    }
    
    validateCurrentStep() {
        const stepRules = this.validationRules[`step${this.currentStep}`];
        if (!stepRules) return true;
        
        let isValid = true;
        
        Object.keys(stepRules).forEach(fieldName => {
            if (!this.validateField(fieldName)) {
                isValid = false;
            }
        });
        
        return isValid;
    }
    
    nextStep() {
        if (!this.validateCurrentStep()) {
            ASG.notifications().error('Por favor, completa todos los campos obligatorios');
            return;
        }
        
        if (this.currentStep < this.totalSteps) {
            this.saveCurrentStepData();
            this.currentStep++;
            this.updateStepDisplay();
            this.updateNavigation();
            
            // Scroll to top
            window.scrollTo({ top: 0, behavior: 'smooth' });
        }
    }
    
    previousStep() {
        if (this.currentStep > 1) {
            this.saveCurrentStepData();
            this.currentStep--;
            this.updateStepDisplay();
            this.updateNavigation();
            
            // Scroll to top
            window.scrollTo({ top: 0, behavior: 'smooth' });
        }
    }
    
    updateStepDisplay() {
        // Hide all steps
        this.elements.steps.forEach(step => {
            step.style.display = 'none';
        });
        
        // Show current step
        const currentStepElement = document.querySelector(`[data-step="${this.currentStep}"]`);
        if (currentStepElement) {
            currentStepElement.style.display = 'block';
        }
        
        // Update step indicators
        this.elements.stepIndicators.forEach((indicator, index) => {
            const stepNumber = index + 1;
            const stepNumberEl = indicator.querySelector('.step-number');
            const stepText = indicator.querySelector('span');
            
            indicator.classList.remove('active', 'completed');
            
            if (stepNumber === this.currentStep) {
                indicator.classList.add('active');
            } else if (stepNumber < this.currentStep) {
                indicator.classList.add('completed');
                stepNumberEl.innerHTML = '<i class="bi bi-check"></i>';
            } else {
                stepNumberEl.textContent = stepNumber;
            }
        });
    }
    
    updateNavigation() {
        // Previous button
        if (this.elements.prevBtn) {
            this.elements.prevBtn.style.display = this.currentStep > 1 ? 'flex' : 'none';
        }
        
        // Next button
        if (this.elements.nextBtn) {
            this.elements.nextBtn.style.display = this.currentStep < this.totalSteps ? 'flex' : 'none';
        }
        
        // Publish button
        if (this.elements.publishBtn) {
            this.elements.publishBtn.style.display = this.currentStep === this.totalSteps ? 'flex' : 'none';
        }
    }
    
    saveCurrentStepData() {
        switch (this.currentStep) {
            case 1:
                this.courseData.basicInfo = {
                    name: this.elements.courseName?.value || '',
                    description: this.elements.courseDescription?.value || '',
                    category: this.elements.courseCategory?.value || '',
                    language: this.elements.courseLanguage?.value || 'es',
                    image: this.courseData.basicInfo.image || null
                };
                break;
            // Add other steps as they are implemented
        }
    }
    
    autoSave() {
        this.saveCurrentStepData();
        
        // Save to localStorage as backup
        localStorage.setItem('asg_course_draft', JSON.stringify({
            data: this.courseData,
            step: this.currentStep,
            timestamp: new Date().toISOString()
        }));
        
        console.log('📝 Auto-guardado realizado');
    }
    
    saveDraft() {
        this.saveCurrentStepData();
        
        ASG.loading().show('save-draft');
        
        // Simulate API call
        setTimeout(() => {
            ASG.loading().hide('save-draft');
            ASG.notifications().success('Borrador guardado correctamente');
            
            // Save to localStorage
            this.autoSave();
        }, 1000);
    }
    
    publishCourse() {
        if (!this.validateCurrentStep()) {
            ASG.notifications().error('Por favor, completa todos los campos obligatorios');
            return;
        }
        
        this.saveCurrentStepData();
        
        if (confirm('¿Estás seguro de que quieres publicar este curso?')) {
            ASG.loading().show('publish-course');
            
            // Simulate API call
            setTimeout(() => {
                ASG.loading().hide('publish-course');
                ASG.notifications().success('¡Curso publicado exitosamente!');
                
                // Clear draft
                localStorage.removeItem('asg_course_draft');
                
                // Redirect to courses list
                setTimeout(() => {
                    window.location.href = 'https://abilityseminarsgroup.com/all-courses/';
                }, 2000);
            }, 2000);
        }
    }
    
    handleFormSubmit() {
        this.publishCourse();
    }
    
    hasUnsavedChanges() {
        // Simple check - in a real app, this would be more sophisticated
        return this.elements.courseName?.value.trim() || 
               this.elements.courseDescription?.value.trim();
    }
    
    loadDraft() {
        const draft = localStorage.getItem('asg_course_draft');
        if (draft) {
            try {
                const draftData = JSON.parse(draft);
                this.courseData = draftData.data;
                this.currentStep = draftData.step || 1;
                
                // Restore form fields
                if (this.courseData.basicInfo) {
                    if (this.elements.courseName) this.elements.courseName.value = this.courseData.basicInfo.name || '';
                    if (this.elements.courseDescription) this.elements.courseDescription.value = this.courseData.basicInfo.description || '';
                    if (this.elements.courseCategory) this.elements.courseCategory.value = this.courseData.basicInfo.category || '';
                    if (this.elements.courseLanguage) this.elements.courseLanguage.value = this.courseData.basicInfo.language || 'es';
                }
                
                this.updateStepDisplay();
                this.updateNavigation();
                this.updatePreview();
                this.updateDescriptionCounter();
                
                ASG.notifications().info('Borrador cargado desde la última sesión');
            } catch (error) {
                console.error('Error loading draft:', error);
                localStorage.removeItem('asg_course_draft');
            }
        }
    }
}

/**
 * ========================================
 * FUNCIONES GLOBALES PARA HTML
 * ========================================
 */
function handleImageUpload(input) {
    const file = input.files[0];
    if (!file) return;
    
    // Validate file size (5MB max)
    if (file.size > 5 * 1024 * 1024) {
        ASG.notifications().error('La imagen no puede superar los 5MB');
        input.value = '';
        return;
    }
    
    // Validate file type
    if (!file.type.startsWith('image/')) {
        ASG.notifications().error('Solo se permiten archivos de imagen');
        input.value = '';
        return;
    }
    
    const reader = new FileReader();
    reader.onload = function(e) {
        const imagePreview = document.getElementById('imagePreview');
        const uploadPlaceholder = document.getElementById('uploadPlaceholder');
        const previewImg = document.getElementById('previewImg');
        const previewImage = document.querySelector('.preview-image');
        
        if (imagePreview && uploadPlaceholder && previewImg) {
            previewImg.src = e.target.result;
            imagePreview.style.display = 'block';
            uploadPlaceholder.style.display = 'none';
            
            // Update sidebar preview
            if (previewImage) {
                previewImage.style.backgroundImage = `url(${e.target.result})`;
                previewImage.style.backgroundSize = 'cover';
                previewImage.style.backgroundPosition = 'center';
                previewImage.innerHTML = '';
            }
            
            // Store in course data
            if (window.newCourse) {
                window.newCourse.courseData.basicInfo.image = e.target.result;
            }
        }
    };
    
    reader.readAsDataURL(file);
}

function removeImage() {
    const imagePreview = document.getElementById('imagePreview');
    const uploadPlaceholder = document.getElementById('uploadPlaceholder');
    const courseImageInput = document.getElementById('courseImageInput');
    const previewImage = document.querySelector('.preview-image');
    
    if (imagePreview && uploadPlaceholder && courseImageInput) {
        imagePreview.style.display = 'none';
        uploadPlaceholder.style.display = 'block';
        courseImageInput.value = '';
        
        // Reset sidebar preview
        if (previewImage) {
            previewImage.style.backgroundImage = '';
            previewImage.innerHTML = '<i class="bi bi-image" style="font-size: 2rem;"></i>';
        }
        
        // Remove from course data
        if (window.newCourse) {
            window.newCourse.courseData.basicInfo.image = null;
        }
    }
}

/**
 * ========================================
 * INICIALIZACIÓN
 * ========================================
 */
let newCourse = null;

document.addEventListener('DOMContentLoaded', function() {
    // Esperar a que los componentes base estén listos
    setTimeout(() => {
        newCourse = new ASGNewCourse();
        
        // Load draft if exists
        newCourse.loadDraft();
        
        // Hacer disponible globalmente
        window.newCourse = newCourse;
    }, 100);
});
