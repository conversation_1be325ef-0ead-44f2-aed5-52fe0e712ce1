<?php
/**
 * ========================================
 * ASG BASE CONFIGURATION & API - SNIPPET 1
 * ========================================
 * 
 * Descripción: Configuración base del sistema ASG y API REST
 * Uso: Copiar y pegar en WP Code Snippets
 * Tipo: PHP Snippet
 * Ubicación: Ejecutar en todas partes
 * 
 * Autor: ASG Team
 * Fecha: 2025-01-04
 * Versión: 2.0.0 - CODE SNIPPETS
 */

// Prevenir ejecución directa
if (!defined('ABSPATH')) {
    exit;
}

/**
 * ========================================
 * CONFIGURACIÓN GLOBAL ASG
 * ========================================
 */
if (!defined('ASG_VERSION')) {
    define('ASG_VERSION', '2.0.0');
    define('ASG_API_NAMESPACE', 'asg/v2');
    define('ASG_DB_VERSION', '1.0');
}

/**
 * ========================================
 * REGISTRO DE API REST
 * ========================================
 */
add_action('rest_api_init', function () {
    
    // ===== ENDPOINTS PÚBLICOS =====
    
    // Obtener curso completo para frontend público
    register_rest_route(ASG_API_NAMESPACE, '/public/courses/(?P<code>[a-zA-Z0-9_-]+)', array(
        'methods' => 'GET',
        'callback' => 'asg_get_public_course',
        'permission_callback' => '__return_true',
        'args' => array(
            'code' => array(
                'required' => true,
                'validate_callback' => function($param) {
                    return preg_match('/^course_\d+$/', $param);
                }
            )
        )
    ));
    
    // Lista de cursos públicos
    register_rest_route(ASG_API_NAMESPACE, '/public/courses', array(
        'methods' => 'GET',
        'callback' => 'asg_get_public_courses',
        'permission_callback' => '__return_true'
    ));
    
    // ===== ENDPOINTS ADMINISTRATIVOS =====
    
    // CRUD completo de cursos
    register_rest_route(ASG_API_NAMESPACE, '/admin/courses', array(
        'methods' => 'GET',
        'callback' => 'asg_admin_get_courses',
        'permission_callback' => 'asg_check_admin_permissions'
    ));
    
    register_rest_route(ASG_API_NAMESPACE, '/admin/courses', array(
        'methods' => 'POST',
        'callback' => 'asg_admin_create_course',
        'permission_callback' => 'asg_check_admin_permissions'
    ));
    
    register_rest_route(ASG_API_NAMESPACE, '/admin/courses/(?P<code>[a-zA-Z0-9_-]+)', array(
        'methods' => array('GET', 'PUT', 'DELETE'),
        'callback' => 'asg_admin_handle_course',
        'permission_callback' => 'asg_check_admin_permissions'
    ));
    
    // Estadísticas del dashboard
    register_rest_route(ASG_API_NAMESPACE, '/admin/dashboard/stats', array(
        'methods' => 'GET',
        'callback' => 'asg_admin_get_dashboard_stats',
        'permission_callback' => 'asg_check_admin_permissions'
    ));
});

/**
 * ========================================
 * FUNCIONES DE API
 * ========================================
 */

/**
 * Verificar permisos de administrador
 */
function asg_check_admin_permissions() {
    return current_user_can('manage_options') || current_user_can('edit_posts');
}

/**
 * Obtener curso público completo
 */
function asg_get_public_course($request) {
    global $wpdb;
    
    $course_code = sanitize_text_field($request['code']);
    
    try {
        // Obtener curso principal
        $course = $wpdb->get_row($wpdb->prepare("
            SELECT * FROM {$wpdb->prefix}courses 
            WHERE code_course = %s 
            AND status_course = 'published' 
            AND is_deleted = 0
        ", $course_code));
        
        if (!$course) {
            return new WP_Error('course_not_found', 'Curso no encontrado', array('status' => 404));
        }
        
        // Obtener módulos con lecciones
        $modules = $wpdb->get_results($wpdb->prepare("
            SELECT m.*, COUNT(l.id_lesson) as lesson_count
            FROM {$wpdb->prefix}modules m
            LEFT JOIN {$wpdb->prefix}lessons l ON m.code_module = l.code_module AND l.is_deleted = 0
            WHERE m.code_course = %s AND m.is_deleted = 0
            GROUP BY m.id_modules
            ORDER BY m.order_module ASC
        ", $course_code));
        
        // Obtener lecciones para cada módulo
        foreach ($modules as &$module) {
            $module->lessons = $wpdb->get_results($wpdb->prepare("
                SELECT * FROM {$wpdb->prefix}lessons 
                WHERE code_module = %s AND is_deleted = 0
                ORDER BY order_lesson ASC
            ", $module->code_module));
        }
        
        // Obtener objetivos y beneficios
        $objectives = $wpdb->get_results($wpdb->prepare("
            SELECT * FROM {$wpdb->prefix}learn_list 
            WHERE code_course = %s AND is_deleted = 0
            ORDER BY order_learn ASC
        ", $course_code));
        
        $benefits = $wpdb->get_results($wpdb->prepare("
            SELECT * FROM {$wpdb->prefix}benefit_list 
            WHERE code_course = %s AND is_deleted = 0
            ORDER BY order_benefit ASC
        ", $course_code));
        
        return new WP_REST_Response(array(
            'success' => true,
            'data' => array(
                'course' => $course,
                'modules' => $modules,
                'objectives' => $objectives,
                'benefits' => $benefits,
                'stats' => array(
                    'total_modules' => count($modules),
                    'total_lessons' => array_sum(array_column($modules, 'lesson_count'))
                )
            ),
            'timestamp' => current_time('mysql')
        ), 200);
        
    } catch (Exception $e) {
        return new WP_Error('server_error', 'Error interno del servidor', array('status' => 500));
    }
}

/**
 * Obtener lista de cursos públicos
 */
function asg_get_public_courses($request) {
    global $wpdb;
    
    $page = intval($request->get_param('page')) ?: 1;
    $per_page = intval($request->get_param('per_page')) ?: 12;
    $category = sanitize_text_field($request->get_param('category'));
    
    $offset = ($page - 1) * $per_page;
    $where_conditions = array("status_course = 'published'", "is_deleted = 0");
    $where_params = array();
    
    if ($category) {
        $where_conditions[] = "category_course = %s";
        $where_params[] = $category;
    }
    
    $where_clause = implode(' AND ', $where_conditions);
    
    try {
        // Contar total
        $total_query = "SELECT COUNT(*) FROM {$wpdb->prefix}courses WHERE {$where_clause}";
        $total = $wpdb->get_var($wpdb->prepare($total_query, $where_params));
        
        // Obtener cursos
        $courses_query = "
            SELECT c.*, COUNT(m.id_modules) as module_count
            FROM {$wpdb->prefix}courses c
            LEFT JOIN {$wpdb->prefix}modules m ON c.code_course = m.code_course AND m.is_deleted = 0
            WHERE {$where_clause}
            GROUP BY c.id_course
            ORDER BY c.featured_course DESC, c.created_at DESC
            LIMIT %d OFFSET %d
        ";
        
        $query_params = array_merge($where_params, array($per_page, $offset));
        $courses = $wpdb->get_results($wpdb->prepare($courses_query, $query_params));
        
        return new WP_REST_Response(array(
            'success' => true,
            'data' => $courses,
            'pagination' => array(
                'page' => $page,
                'per_page' => $per_page,
                'total' => intval($total),
                'total_pages' => ceil($total / $per_page)
            )
        ), 200);
        
    } catch (Exception $e) {
        return new WP_Error('server_error', 'Error interno del servidor', array('status' => 500));
    }
}

/**
 * Obtener estadísticas del dashboard
 */
function asg_admin_get_dashboard_stats($request) {
    global $wpdb;
    
    try {
        // Estadísticas básicas
        $total_courses = $wpdb->get_var("SELECT COUNT(*) FROM {$wpdb->prefix}courses WHERE is_deleted = 0");
        $published_courses = $wpdb->get_var("SELECT COUNT(*) FROM {$wpdb->prefix}courses WHERE status_course = 'published' AND is_deleted = 0");
        $draft_courses = $wpdb->get_var("SELECT COUNT(*) FROM {$wpdb->prefix}courses WHERE status_course = 'draft' AND is_deleted = 0");
        
        // Ingresos potenciales
        $total_revenue = $wpdb->get_var("SELECT SUM(price_course) FROM {$wpdb->prefix}courses WHERE status_course = 'published' AND is_deleted = 0");
        $avg_price = $wpdb->get_var("SELECT AVG(price_course) FROM {$wpdb->prefix}courses WHERE status_course = 'published' AND is_deleted = 0");
        
        return new WP_REST_Response(array(
            'success' => true,
            'data' => array(
                'totalCourses' => intval($total_courses),
                'publishedCourses' => intval($published_courses),
                'draftCourses' => intval($draft_courses),
                'totalRevenue' => floatval($total_revenue),
                'avgPrice' => floatval($avg_price),
                'coursesChange' => 15 // Simulado - calcular vs mes anterior
            )
        ), 200);
        
    } catch (Exception $e) {
        return new WP_Error('server_error', 'Error interno del servidor', array('status' => 500));
    }
}

/**
 * Obtener cursos para administración
 */
function asg_admin_get_courses($request) {
    global $wpdb;
    
    $page = intval($request->get_param('page')) ?: 1;
    $per_page = intval($request->get_param('per_page')) ?: 20;
    $status = sanitize_text_field($request->get_param('status'));
    
    $offset = ($page - 1) * $per_page;
    $where_conditions = array("is_deleted = 0");
    $where_params = array();
    
    if ($status) {
        $where_conditions[] = "status_course = %s";
        $where_params[] = $status;
    }
    
    $where_clause = implode(' AND ', $where_conditions);
    
    try {
        $total_query = "SELECT COUNT(*) FROM {$wpdb->prefix}courses WHERE {$where_clause}";
        $total = $wpdb->get_var($wpdb->prepare($total_query, $where_params));
        
        $courses_query = "
            SELECT c.*, 
                   COUNT(DISTINCT m.id_modules) as module_count,
                   COUNT(DISTINCT l.id_lesson) as lesson_count
            FROM {$wpdb->prefix}courses c
            LEFT JOIN {$wpdb->prefix}modules m ON c.code_course = m.code_course AND m.is_deleted = 0
            LEFT JOIN {$wpdb->prefix}lessons l ON m.code_module = l.code_module AND l.is_deleted = 0
            WHERE {$where_clause}
            GROUP BY c.id_course
            ORDER BY c.updated_at DESC
            LIMIT %d OFFSET %d
        ";
        
        $query_params = array_merge($where_params, array($per_page, $offset));
        $courses = $wpdb->get_results($wpdb->prepare($courses_query, $query_params));
        
        return new WP_REST_Response(array(
            'success' => true,
            'data' => $courses,
            'pagination' => array(
                'page' => $page,
                'per_page' => $per_page,
                'total' => intval($total),
                'total_pages' => ceil($total / $per_page)
            )
        ), 200);
        
    } catch (Exception $e) {
        return new WP_Error('server_error', 'Error interno del servidor', array('status' => 500));
    }
}

/**
 * Manejar operaciones CRUD de cursos individuales
 */
function asg_admin_handle_course($request) {
    $method = $request->get_method();
    $course_code = sanitize_text_field($request['code']);
    
    switch ($method) {
        case 'GET':
            return asg_admin_get_single_course($course_code);
        case 'PUT':
            return asg_admin_update_course($course_code, $request);
        case 'DELETE':
            return asg_admin_delete_course($course_code);
        default:
            return new WP_Error('method_not_allowed', 'Método no permitido', array('status' => 405));
    }
}

/**
 * Obtener curso individual para administración
 */
function asg_admin_get_single_course($course_code) {
    global $wpdb;
    
    try {
        $course = $wpdb->get_row($wpdb->prepare("
            SELECT * FROM {$wpdb->prefix}courses 
            WHERE code_course = %s AND is_deleted = 0
        ", $course_code));
        
        if (!$course) {
            return new WP_Error('course_not_found', 'Curso no encontrado', array('status' => 404));
        }
        
        // Obtener módulos, objetivos y beneficios
        $modules = $wpdb->get_results($wpdb->prepare("
            SELECT * FROM {$wpdb->prefix}modules 
            WHERE code_course = %s AND is_deleted = 0
            ORDER BY order_module ASC
        ", $course_code));
        
        $objectives = $wpdb->get_results($wpdb->prepare("
            SELECT * FROM {$wpdb->prefix}learn_list 
            WHERE code_course = %s AND is_deleted = 0
            ORDER BY order_learn ASC
        ", $course_code));
        
        $benefits = $wpdb->get_results($wpdb->prepare("
            SELECT * FROM {$wpdb->prefix}benefit_list 
            WHERE code_course = %s AND is_deleted = 0
            ORDER BY order_benefit ASC
        ", $course_code));
        
        return new WP_REST_Response(array(
            'success' => true,
            'data' => array(
                'course' => $course,
                'modules' => $modules,
                'objectives' => $objectives,
                'benefits' => $benefits
            )
        ), 200);
        
    } catch (Exception $e) {
        return new WP_Error('server_error', 'Error interno del servidor', array('status' => 500));
    }
}

/**
 * ========================================
 * UTILIDADES GLOBALES
 * ========================================
 */

/**
 * Formatear moneda
 */
function asg_format_currency($amount) {
    return number_format($amount, 2, ',', '.') . ' €';
}

/**
 * Formatear fecha
 */
function asg_format_date($date) {
    return date_i18n('j F Y', strtotime($date));
}

/**
 * Generar código único de curso
 */
function asg_generate_course_code() {
    global $wpdb;
    
    $last_course = $wpdb->get_var("
        SELECT code_course FROM {$wpdb->prefix}courses 
        WHERE code_course REGEXP '^course_[0-9]+$' 
        ORDER BY CAST(SUBSTRING(code_course, 8) AS UNSIGNED) DESC 
        LIMIT 1
    ");
    
    if ($last_course) {
        $last_number = intval(str_replace('course_', '', $last_course));
        $new_number = $last_number + 1;
    } else {
        $new_number = 1;
    }
    
    return 'course_' . $new_number;
}

/**
 * ========================================
 * CONFIGURACIÓN JAVASCRIPT GLOBAL
 * ========================================
 */
add_action('wp_head', function() {
    ?>
    <script>
    window.ASG_CONFIG = {
        apiUrl: '<?php echo esc_url(rest_url(ASG_API_NAMESPACE)); ?>',
        nonce: '<?php echo wp_create_nonce('wp_rest'); ?>',
        logoUrl: '<?php echo esc_url(get_site_url()); ?>/wp-content/uploads/2023/09/Asset-6-4-768x355.png',
        version: '<?php echo ASG_VERSION; ?>',
        breakpoints: {
            mobile: 640,
            tablet: 768,
            desktop: 1024
        }
    };
    </script>
    <?php
});

/**
 * ========================================
 * ESTILOS CSS GLOBALES
 * ========================================
 */
add_action('wp_head', function() {
    ?>
    <style>
    :root {
        --asg-primary: #2563eb;
        --asg-primary-dark: #1d4ed8;
        --asg-success: #10b981;
        --asg-warning: #f59e0b;
        --asg-error: #ef4444;
        --asg-gray-50: #f8fafc;
        --asg-gray-100: #f1f5f9;
        --asg-gray-200: #e2e8f0;
        --asg-gray-600: #475569;
        --asg-gray-900: #0f172a;
        --asg-space-2: 0.5rem;
        --asg-space-4: 1rem;
        --asg-space-6: 1.5rem;
        --asg-radius: 0.375rem;
        --asg-shadow: 0 1px 3px 0 rgb(0 0 0 / 0.1);
        --asg-transition: 200ms ease-in-out;
    }
    
    .asg-btn {
        display: inline-flex;
        align-items: center;
        gap: var(--asg-space-2);
        padding: var(--asg-space-2) var(--asg-space-4);
        border: 1px solid transparent;
        border-radius: var(--asg-radius);
        font-weight: 500;
        text-decoration: none;
        cursor: pointer;
        transition: all var(--asg-transition);
    }
    
    .asg-btn-primary {
        background: var(--asg-primary);
        color: white;
        border-color: var(--asg-primary);
    }
    
    .asg-btn-primary:hover {
        background: var(--asg-primary-dark);
        color: white;
    }
    
    .asg-card {
        background: white;
        border: 1px solid var(--asg-gray-200);
        border-radius: var(--asg-radius);
        box-shadow: var(--asg-shadow);
    }
    
    .asg-badge {
        display: inline-flex;
        padding: 0.25rem 0.75rem;
        font-size: 0.75rem;
        font-weight: 500;
        border-radius: 9999px;
        text-transform: uppercase;
    }
    
    .asg-badge-success {
        background: #d1fae5;
        color: #065f46;
    }
    
    .asg-badge-warning {
        background: #fef3c7;
        color: #92400e;
    }
    
    .asg-badge-gray {
        background: var(--asg-gray-100);
        color: var(--asg-gray-600);
    }
    </style>
    <?php
});
?>
