<?php
/**
 * ASG Edit Course Page - WordPress Code Snippet
 * 
 * Descripción: Crea la página para editar cursos existentes
 * Versión: 2.0.0
 * Autor: AbilitySeminarsGroup
 */

// Evitar acceso directo
if (!defined('ABSPATH')) {
    exit;
}

/**
 * Crear página de editar curso
 */
function asg_create_edit_course_page() {
    // Verificar permisos
    if (!current_user_can('manage_options')) {
        wp_die(__('No tienes permisos para acceder a esta página.'));
    }
    
    // Obtener URL base
    $site_url = get_site_url();
    ?>
    <!DOCTYPE html>
    <html lang="es">
    <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>Editar Curso - ASG</title>
        <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.3/dist/css/bootstrap.min.css" rel="stylesheet">
        <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.11.3/font/bootstrap-icons.css">
        <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
        
        <style>
            /* CSS optimizado para snippet */
            * { margin: 0; padding: 0; box-sizing: border-box; }
            body { font-family: 'Inter', sans-serif; background: #f8f9fa; }
            .navbar { background: linear-gradient(135deg, #1e3a5f 0%, #2563eb 100%); height: 70px; position: fixed; top: 0; left: 0; right: 0; z-index: 1000; }
            .navbar-brand img { width: 130px; height: auto; }
            .navbar-nav .nav-link { color: white !important; font-weight: 500; padding: 0.5rem 1rem; border-radius: 6px; }
            .main-content { margin-top: 70px; padding: 2rem; min-height: calc(100vh - 70px); }
            .page-header { background: white; border-radius: 12px; padding: 2rem; margin-bottom: 2rem; box-shadow: 0 2px 10px rgba(0,0,0,0.05); }
            .page-title { font-size: 2rem; font-weight: 700; color: #1e3a5f; margin-bottom: 0.5rem; }
            .course-info { display: flex; align-items: center; gap: 1rem; margin-top: 1rem; padding: 1rem; background: #f8f9fa; border-radius: 8px; }
            .course-image-small { width: 60px; height: 45px; border-radius: 6px; background-size: cover; background-position: center; }
            .nav-tabs { border-bottom: 2px solid #e9ecef; margin-bottom: 2rem; }
            .nav-tabs .nav-link { border: none; color: #6b7280; font-weight: 500; padding: 1rem 1.5rem; border-radius: 0; }
            .nav-tabs .nav-link.active { color: #2563eb; background: none; border-bottom: 2px solid #2563eb; }
            .tab-content { background: white; border-radius: 12px; padding: 2rem; box-shadow: 0 2px 10px rgba(0,0,0,0.05); }
            .form-group { margin-bottom: 1.5rem; }
            .form-group label { font-weight: 500; color: #374151; margin-bottom: 0.5rem; display: block; }
            .form-control { width: 100%; padding: 0.75rem; border: 1px solid #d1d5db; border-radius: 6px; font-size: 0.9rem; transition: all 0.3s ease; }
            .form-control:focus { outline: none; border-color: #2563eb; box-shadow: 0 0 0 3px rgba(37, 99, 235, 0.1); }
            .form-text { font-size: 0.8rem; color: #6b7280; margin-top: 0.25rem; }
            .form-grid { display: grid; grid-template-columns: 1fr 1fr; gap: 1.5rem; }
            .form-grid-3 { display: grid; grid-template-columns: 1fr 1fr 1fr; gap: 1rem; }
            .image-upload { border: 2px dashed #d1d5db; border-radius: 8px; padding: 2rem; text-align: center; cursor: pointer; background: #fafbfc; }
            .image-preview { max-width: 100%; max-height: 200px; border-radius: 8px; margin-top: 1rem; }
            .category-pills { display: flex; flex-wrap: wrap; gap: 0.5rem; }
            .category-pill { padding: 0.5rem 1rem; border: 1px solid #d1d5db; border-radius: 20px; cursor: pointer; font-size: 0.85rem; background: white; }
            .category-pill.selected { background: #2563eb; color: white; border-color: #2563eb; }
            .status-badge { padding: 0.25rem 0.75rem; border-radius: 20px; font-size: 0.75rem; font-weight: 500; text-transform: uppercase; }
            .status-published { background-color: #d1fae5; color: #065f46; }
            .status-draft { background-color: #fef3c7; color: #92400e; }
            .btn-group { display: flex; gap: 1rem; justify-content: flex-end; margin-top: 2rem; padding-top: 2rem; border-top: 1px solid #f1f3f4; }
            .btn { padding: 0.75rem 1.5rem; border-radius: 6px; font-weight: 500; cursor: pointer; border: none; display: inline-flex; align-items: center; gap: 0.5rem; }
            .btn-primary { background: #2563eb; color: white; }
            .btn-success { background: #10b981; color: white; }
            .btn-warning { background: #f59e0b; color: white; }
            .btn-danger { background: #ef4444; color: white; }
            .btn-outline { background: white; color: #6b7280; border: 1px solid #d1d5db; }
            .btn:disabled { opacity: 0.5; cursor: not-allowed; }
            .loading-skeleton { background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%); background-size: 200% 100%; animation: loading 1.5s infinite; border-radius: 4px; }
            @keyframes loading { 0% { background-position: 200% 0; } 100% { background-position: -200% 0; } }
            @keyframes slideIn { from { transform: translateX(100%); opacity: 0; } to { transform: translateX(0); opacity: 1; } }
            @keyframes slideOut { from { transform: translateX(0); opacity: 1; } to { transform: translateX(100%); opacity: 0; } }
            .spin { animation: spin 1s linear infinite; }
            @keyframes spin { from { transform: rotate(0deg); } to { transform: rotate(360deg); } }
            @media (max-width: 768px) { .main-content { padding: 1rem; } .form-grid, .form-grid-3 { grid-template-columns: 1fr; } .btn-group { flex-direction: column; } }
        </style>
    </head>
    <body>
        <!-- Navbar -->
        <nav class="navbar navbar-expand-lg">
            <div class="container-fluid">
                <a class="navbar-brand" href="<?php echo $site_url; ?>/admin-dashboard/">
                    <img src="https://abilityseminarsgroup.com/wp-content/uploads/2023/09/Asset-6-4-768x355.png" alt="ASG Logo">
                </a>
                
                <div class="collapse navbar-collapse">
                    <ul class="navbar-nav ms-auto">
                        <li class="nav-item">
                            <a class="nav-link" href="<?php echo $site_url; ?>/admin-dashboard/">
                                <i class="bi bi-speedometer2"></i> Dashboard
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="<?php echo $site_url; ?>/all-courses/">
                                <i class="bi bi-collection"></i> Todos los Cursos
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="<?php echo $site_url; ?>/new-course/">
                                <i class="bi bi-plus-circle"></i> Nuevo Curso
                            </a>
                        </li>
                    </ul>
                </div>
            </div>
        </nav>

        <!-- Main Content -->
        <div class="main-content">
            <!-- Page Header -->
            <div class="page-header">
                <h1 class="page-title">✏️ Editar Curso</h1>
                <p style="color: #6c757d; font-size: 1.1rem;">Modifica la información y configuración del curso</p>
                
                <!-- Course Info -->
                <div class="course-info" id="courseInfo">
                    <div class="loading-skeleton" style="width: 60px; height: 45px; border-radius: 6px;"></div>
                    <div>
                        <div class="loading-skeleton" style="height: 1.1rem; width: 200px; margin-bottom: 0.5rem;"></div>
                        <div class="loading-skeleton" style="height: 0.85rem; width: 150px;"></div>
                    </div>
                </div>
            </div>

            <!-- Navigation Tabs -->
            <ul class="nav nav-tabs" id="editTabs" role="tablist">
                <li class="nav-item" role="presentation">
                    <button class="nav-link active" id="basic-tab" data-bs-toggle="tab" data-bs-target="#basic" type="button" role="tab">
                        <i class="bi bi-info-circle"></i> Información Básica
                    </button>
                </li>
                <li class="nav-item" role="presentation">
                    <button class="nav-link" id="content-tab" data-bs-toggle="tab" data-bs-target="#content" type="button" role="tab">
                        <i class="bi bi-image"></i> Contenido
                    </button>
                </li>
                <li class="nav-item" role="presentation">
                    <button class="nav-link" id="settings-tab" data-bs-toggle="tab" data-bs-target="#settings" type="button" role="tab">
                        <i class="bi bi-gear"></i> Configuración
                    </button>
                </li>
            </ul>

            <!-- Tab Content -->
            <div class="tab-content" id="editTabContent">
                <!-- Información Básica -->
                <div class="tab-pane fade show active" id="basic" role="tabpanel">
                    <form id="basicForm">
                        <div class="form-group">
                            <label for="courseTitle">Título del Curso</label>
                            <input type="text" id="courseTitle" class="form-control" placeholder="Cargando...">
                            <div class="form-text">Un título claro y atractivo que describa el contenido del curso</div>
                        </div>

                        <div class="form-group">
                            <label for="courseDescription">Descripción</label>
                            <textarea id="courseDescription" class="form-control" rows="4" placeholder="Cargando..."></textarea>
                            <div class="form-text">Una descripción detallada que motive a los estudiantes a inscribirse</div>
                        </div>

                        <div class="form-grid">
                            <div class="form-group">
                                <label>Categoría</label>
                                <div class="category-pills" id="categoryPills">
                                    <!-- Se cargan dinámicamente -->
                                </div>
                                <input type="hidden" id="courseCategory">
                            </div>

                            <div class="form-group">
                                <label for="coursePrice">Precio (€)</label>
                                <input type="number" id="coursePrice" class="form-control" placeholder="0.00" step="0.01" min="0">
                                <div class="form-text">Precio en euros (usar punto para decimales)</div>
                            </div>
                        </div>
                    </form>
                </div>

                <!-- Contenido -->
                <div class="tab-pane fade" id="content" role="tabpanel">
                    <form id="contentForm">
                        <div class="form-group">
                            <label>Imagen Principal</label>
                            <div class="image-upload" id="imageUpload">
                                <div style="font-size: 2rem; color: #9ca3af; margin-bottom: 0.5rem;">
                                    <i class="bi bi-cloud-upload"></i>
                                </div>
                                <div><strong>Haz clic para cambiar</strong> o arrastra una nueva imagen aquí</div>
                                <div style="font-size: 0.8rem; color: #6b7280; margin-top: 0.25rem;">Formatos: JPG, PNG, WebP (máx. 2MB)</div>
                                <img id="imagePreview" class="image-preview" style="display: none;">
                            </div>
                            <input type="file" id="imageInput" accept="image/*" style="display: none;">
                        </div>

                        <div class="form-grid-3">
                            <div class="form-group">
                                <label for="courseModules">Número de Módulos</label>
                                <input type="number" id="courseModules" class="form-control" min="1" max="50">
                                <div class="form-text">Cantidad de módulos del curso</div>
                            </div>

                            <div class="form-group">
                                <label for="courseLessons">Número de Lecciones</label>
                                <input type="number" id="courseLessons" class="form-control" min="1" max="200">
                                <div class="form-text">Total de lecciones</div>
                            </div>

                            <div class="form-group">
                                <label for="courseDuration">Duración (horas)</label>
                                <input type="number" id="courseDuration" class="form-control" min="1" max="500">
                                <div class="form-text">Duración estimada en horas</div>
                            </div>
                        </div>
                    </form>
                </div>

                <!-- Configuración -->
                <div class="tab-pane fade" id="settings" role="tabpanel">
                    <form id="settingsForm">
                        <div class="form-grid">
                            <div class="form-group">
                                <label for="courseStatus">Estado del Curso</label>
                                <select id="courseStatus" class="form-control">
                                    <option value="draft">✏️ Borrador</option>
                                    <option value="published">✅ Publicado</option>
                                    <option value="archived">📦 Archivado</option>
                                </select>
                                <div class="form-text">Los borradores no son visibles para los estudiantes</div>
                            </div>

                            <div class="form-group">
                                <label for="courseCode">Código del Curso</label>
                                <input type="text" id="courseCode" class="form-control" readonly>
                                <div class="form-text">Identificador único del curso (no editable)</div>
                            </div>
                        </div>

                        <div class="form-group">
                            <label for="courseNotes">Notas Internas</label>
                            <textarea id="courseNotes" class="form-control" rows="3" placeholder="Notas para uso interno del equipo..."></textarea>
                            <div class="form-text">Información adicional para el equipo (no visible para estudiantes)</div>
                        </div>

                        <div class="form-group">
                            <label>Estadísticas del Curso</label>
                            <div class="form-grid-3">
                                <div style="text-align: center; padding: 1rem; background: #f8f9fa; border-radius: 8px;">
                                    <div style="font-size: 1.5rem; font-weight: 600; color: #2563eb;" id="courseStudents">0</div>
                                    <div style="font-size: 0.8rem; color: #6b7280;">Estudiantes</div>
                                </div>
                                <div style="text-align: center; padding: 1rem; background: #f8f9fa; border-radius: 8px;">
                                    <div style="font-size: 1.5rem; font-weight: 600; color: #10b981;" id="courseRating">0.0</div>
                                    <div style="font-size: 0.8rem; color: #6b7280;">Valoración</div>
                                </div>
                                <div style="text-align: center; padding: 1rem; background: #f8f9fa; border-radius: 8px;">
                                    <div style="font-size: 1.5rem; font-weight: 600; color: #f59e0b;" id="courseRevenue">€0</div>
                                    <div style="font-size: 0.8rem; color: #6b7280;">Ingresos</div>
                                </div>
                            </div>
                        </div>
                    </form>
                </div>
            </div>

            <!-- Action Buttons -->
            <div class="btn-group">
                <button type="button" class="btn btn-outline" onclick="cancelEdit()">
                    <i class="bi bi-x-circle"></i> Cancelar
                </button>
                <button type="button" class="btn btn-warning" onclick="saveDraft()">
                    <i class="bi bi-file-earmark"></i> Guardar Borrador
                </button>
                <button type="button" class="btn btn-success" onclick="publishCourse()">
                    <i class="bi bi-check-circle"></i> Publicar
                </button>
                <button type="button" class="btn btn-primary" onclick="saveChanges()">
                    <i class="bi bi-save"></i> Guardar Cambios
                </button>
            </div>
        </div>

        <!-- Scripts -->
        <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.3/dist/js/bootstrap.bundle.min.js"></script>

        <!-- JavaScript con API Real -->
        <script>
            // Configuración con API Real
            const ASG_CONFIG = {
                version: '2.0.0',
                apiUrl: '<?php echo $site_url; ?>/wp-json/asg/v1',
                siteUrl: '<?php echo $site_url; ?>',
                categories: {
                    finanzas: { label: 'Finanzas', icon: '💰' },
                    marketing: { label: 'Marketing', icon: '📱' },
                    'desarrollo-personal': { label: 'Desarrollo Personal', icon: '🧠' },
                    tecnologia: { label: 'Tecnología', icon: '💻' },
                    negocios: { label: 'Negocios', icon: '💼' },
                    salud: { label: 'Salud', icon: '🏥' }
                },
                states: {
                    draft: { label: 'Borrador', badge: 'status-draft' },
                    published: { label: 'Publicado', badge: 'status-published' },
                    archived: { label: 'Archivado', badge: 'status-archived' }
                }
            };

            // Datos mock para cursos
            const MOCK_COURSES = {
                'course_1': {
                    id: 1, code: 'course_1', title: 'Como hacerte millonario?',
                    description: 'Aprende las estrategias más efectivas para generar riqueza y alcanzar la libertad financiera.',
                    image: 'https://images.unsplash.com/photo-1579621970563-ebec7560ff3e?w=400&h=300&fit=crop',
                    price: 299.99, status: 'published', category: 'finanzas', modules: 8, lessons: 24, duration: 12,
                    students: 156, rating: 4.8, revenue: 46799.44, created: '2024-12-15', updated: '2024-12-20',
                    notes: 'Curso muy popular, considerar crear versión avanzada'
                },
                'course_2': {
                    id: 2, code: 'course_2', title: 'Marketing Digital Avanzado',
                    description: 'Domina las técnicas más avanzadas del marketing digital y haz crecer tu negocio online.',
                    image: 'https://images.unsplash.com/photo-1460925895917-afdab827c52f?w=400&h=300&fit=crop',
                    price: 199.99, status: 'published', category: 'marketing', modules: 6, lessons: 18, duration: 8,
                    students: 89, rating: 4.6, revenue: 17799.11, created: '2024-12-10', updated: '2024-12-18',
                    notes: 'Actualizar contenido de redes sociales'
                },
                'course_3': {
                    id: 3, code: 'course_3', title: 'Desarrollo Personal Integral',
                    description: 'Transforma tu vida con técnicas probadas de crecimiento personal y liderazgo.',
                    image: 'https://images.unsplash.com/photo-1516321318423-f06f85e504b3?w=400&h=300&fit=crop',
                    price: 149.99, status: 'draft', category: 'desarrollo-personal', modules: 4, lessons: 12, duration: 6,
                    students: 0, rating: 0, revenue: 0, created: '2024-12-05', updated: '2024-12-22',
                    notes: 'Pendiente de revisión final antes de publicar'
                }
            };

            // Variables globales
            let currentCourse = null;
            let hasUnsavedChanges = false;

            // API Client
            class ASGApiClient {
                constructor() {
                    this.baseUrl = ASG_CONFIG.apiUrl;
                }

                async request(endpoint, options = {}) {
                    const url = `${this.baseUrl}${endpoint}`;
                    const defaultOptions = {
                        method: 'GET',
                        headers: {
                            'Content-Type': 'application/json',
                            'X-WP-Nonce': '<?php echo wp_create_nonce('wp_rest'); ?>'
                        }
                    };

                    try {
                        const response = await fetch(url, { ...defaultOptions, ...options });
                        if (!response.ok) throw new Error(`HTTP ${response.status}`);
                        return await response.json();
                    } catch (error) {
                        console.warn('API Error:', error);
                        throw error;
                    }
                }

                async getCourse(courseCode) {
                    try {
                        const response = await this.request(`/courses/${courseCode}`);
                        return response.data || response;
                    } catch (error) {
                        console.warn('Get course error, using fallback');
                        return MOCK_COURSES[courseCode] || null;
                    }
                }

                async updateCourse(courseCode, courseData) {
                    try {
                        const response = await this.request(`/courses/${courseCode}`, {
                            method: 'PUT',
                            body: JSON.stringify(courseData)
                        });
                        return response;
                    } catch (error) {
                        console.warn('Update course error, simulating success');
                        return { success: true, data: courseData };
                    }
                }
            }

            const ASG_API = new ASGApiClient();

            // Utilidades
            const ASG_UTILS = {
                formatCurrency: (amount) => {
                    return new Intl.NumberFormat('es-ES', {
                        style: 'currency',
                        currency: 'EUR'
                    }).format(amount || 0);
                },
                formatDate: (date) => {
                    return new Intl.DateTimeFormat('es-ES', {
                        year: 'numeric',
                        month: 'long',
                        day: 'numeric',
                        hour: '2-digit',
                        minute: '2-digit'
                    }).format(new Date(date));
                },
                getCategoryInfo: (code) => ASG_CONFIG.categories[code] || { label: code, icon: '📚' },
                getStatusInfo: (code) => ASG_CONFIG.states[code] || { label: code, badge: 'status-draft' },
                getUrlParameter: (name) => {
                    const urlParams = new URLSearchParams(window.location.search);
                    return urlParams.get(name);
                }
            };

            // Notificaciones
            function showNotification(message, type = 'info') {
                const colors = { success: '#10b981', error: '#ef4444', warning: '#f59e0b', info: '#06b6d4' };
                const icons = { success: '✅', error: '❌', warning: '⚠️', info: 'ℹ️' };

                const toast = document.createElement('div');
                toast.style.cssText = `
                    position: fixed; top: 90px; right: 20px; background: ${colors[type]};
                    color: white; padding: 12px 16px; border-radius: 8px;
                    box-shadow: 0 4px 12px rgba(0,0,0,0.15); z-index: 9999;
                    animation: slideIn 0.3s ease-out; cursor: pointer; max-width: 400px;
                `;
                toast.innerHTML = `
                    <div style="display: flex; align-items: center; gap: 8px;">
                        <span>${icons[type]}</span><span>${message}</span><span style="opacity: 0.7; margin-left: auto;">×</span>
                    </div>
                `;

                toast.addEventListener('click', () => {
                    toast.style.animation = 'slideOut 0.3s ease-in';
                    setTimeout(() => document.body.removeChild(toast), 300);
                });

                document.body.appendChild(toast);
                setTimeout(() => {
                    if (toast.parentNode) {
                        toast.style.animation = 'slideOut 0.3s ease-in';
                        setTimeout(() => toast.parentNode && document.body.removeChild(toast), 300);
                    }
                }, 5000);
            }

            // Cargar curso
            async function loadCourse(courseCode) {
                try {
                    showNotification('📚 Cargando datos del curso...', 'info');

                    const course = await ASG_API.getCourse(courseCode);
                    if (!course) {
                        throw new Error('Curso no encontrado');
                    }

                    currentCourse = { ...course };
                    renderCourseInfo();
                    populateForm();

                    showNotification('✅ Curso cargado correctamente', 'success');

                } catch (error) {
                    showNotification('❌ Error al cargar el curso', 'error');
                    setTimeout(() => {
                        window.location.href = '<?php echo $site_url; ?>/all-courses/';
                    }, 2000);
                }
            }

            function renderCourseInfo() {
                if (!currentCourse) return;

                const category = ASG_UTILS.getCategoryInfo(currentCourse.category);
                const status = ASG_UTILS.getStatusInfo(currentCourse.status);

                document.getElementById('courseInfo').innerHTML = `
                    <div class="course-image-small" style="background-image: url('${currentCourse.image}')"></div>
                    <div>
                        <h4 style="margin: 0; color: #1e3a5f; font-size: 1.1rem;">${currentCourse.title}</h4>
                        <div style="font-size: 0.85rem; color: #6b7280; margin-top: 0.25rem;">
                            ${category.icon} ${category.label} •
                            <span class="status-badge ${status.badge}">${status.label}</span> •
                            ${ASG_UTILS.formatCurrency(currentCourse.price)} •
                            ${currentCourse.students} estudiantes
                        </div>
                    </div>
                `;
            }

            function populateForm() {
                if (!currentCourse) return;

                // Información básica
                document.getElementById('courseTitle').value = currentCourse.title;
                document.getElementById('courseDescription').value = currentCourse.description;
                document.getElementById('coursePrice').value = currentCourse.price;
                document.getElementById('courseCategory').value = currentCourse.category;

                // Contenido
                document.getElementById('courseModules').value = currentCourse.modules;
                document.getElementById('courseLessons').value = currentCourse.lessons;
                document.getElementById('courseDuration').value = currentCourse.duration;

                // Configuración
                document.getElementById('courseStatus').value = currentCourse.status;
                document.getElementById('courseCode').value = currentCourse.code;
                document.getElementById('courseNotes').value = currentCourse.notes || '';

                // Estadísticas
                document.getElementById('courseStudents').textContent = currentCourse.students;
                document.getElementById('courseRating').textContent = currentCourse.rating.toFixed(1);
                document.getElementById('courseRevenue').textContent = ASG_UTILS.formatCurrency(currentCourse.revenue);

                // Imagen
                if (currentCourse.image) {
                    const preview = document.getElementById('imagePreview');
                    preview.src = currentCourse.image;
                    preview.style.display = 'block';
                }

                // Categorías
                renderCategories();
                selectCategory(currentCourse.category);
            }

            function renderCategories() {
                const container = document.getElementById('categoryPills');
                container.innerHTML = Object.entries(ASG_CONFIG.categories).map(([code, info]) => `
                    <div class="category-pill" data-category="${code}" onclick="selectCategory('${code}')">
                        ${info.icon} ${info.label}
                    </div>
                `).join('');
            }

            function selectCategory(categoryCode) {
                document.querySelectorAll('.category-pill').forEach(pill => pill.classList.remove('selected'));
                const selectedPill = document.querySelector(`[data-category="${categoryCode}"]`);
                if (selectedPill) {
                    selectedPill.classList.add('selected');
                    currentCourse.category = categoryCode;
                    document.getElementById('courseCategory').value = categoryCode;
                    markAsChanged();
                }
            }

            function markAsChanged() {
                hasUnsavedChanges = true;
                const title = document.querySelector('.page-title');
                if (!title.textContent.includes('*')) {
                    title.textContent = '✏️ Editar Curso *';
                }
            }

            function updateCourseData() {
                if (!currentCourse) return;

                currentCourse.title = document.getElementById('courseTitle').value;
                currentCourse.description = document.getElementById('courseDescription').value;
                currentCourse.price = parseFloat(document.getElementById('coursePrice').value) || 0;
                currentCourse.modules = parseInt(document.getElementById('courseModules').value) || 1;
                currentCourse.lessons = parseInt(document.getElementById('courseLessons').value) || 1;
                currentCourse.duration = parseInt(document.getElementById('courseDuration').value) || 1;
                currentCourse.status = document.getElementById('courseStatus').value;
                currentCourse.notes = document.getElementById('courseNotes').value;
                currentCourse.updated = new Date().toISOString();
            }

            // Funciones de guardado
            async function saveChanges() {
                if (!currentCourse) return;

                try {
                    const btn = event.target;
                    const originalText = btn.innerHTML;

                    btn.innerHTML = '<i class="bi bi-arrow-clockwise spin"></i> Guardando...';
                    btn.disabled = true;

                    updateCourseData();

                    showNotification('💾 Guardando cambios...', 'info');

                    await ASG_API.updateCourse(currentCourse.code, currentCourse);

                    hasUnsavedChanges = false;
                    document.querySelector('.page-title').textContent = '✏️ Editar Curso';
                    renderCourseInfo();

                    showNotification('✅ Cambios guardados correctamente', 'success');

                } catch (error) {
                    showNotification('❌ Error al guardar los cambios', 'error');
                } finally {
                    const btn = document.querySelector('.btn-primary');
                    btn.innerHTML = '<i class="bi bi-save"></i> Guardar Cambios';
                    btn.disabled = false;
                }
            }

            async function saveDraft() {
                if (!currentCourse) return;

                try {
                    updateCourseData();
                    currentCourse.status = 'draft';
                    document.getElementById('courseStatus').value = 'draft';

                    showNotification('💾 Guardando como borrador...', 'info');

                    await ASG_API.updateCourse(currentCourse.code, currentCourse);

                    hasUnsavedChanges = false;
                    document.querySelector('.page-title').textContent = '✏️ Editar Curso';
                    renderCourseInfo();

                    showNotification('✅ Borrador guardado correctamente', 'success');

                } catch (error) {
                    showNotification('❌ Error al guardar el borrador', 'error');
                }
            }

            async function publishCourse() {
                if (!currentCourse) return;

                if (!confirm('¿Estás seguro de que quieres publicar este curso? Será visible para todos los estudiantes.')) {
                    return;
                }

                try {
                    updateCourseData();
                    currentCourse.status = 'published';
                    document.getElementById('courseStatus').value = 'published';

                    showNotification('🚀 Publicando curso...', 'info');

                    await ASG_API.updateCourse(currentCourse.code, currentCourse);

                    hasUnsavedChanges = false;
                    document.querySelector('.page-title').textContent = '✏️ Editar Curso';
                    renderCourseInfo();

                    showNotification('✅ Curso publicado exitosamente', 'success');

                } catch (error) {
                    showNotification('❌ Error al publicar el curso', 'error');
                }
            }

            function cancelEdit() {
                if (hasUnsavedChanges) {
                    if (!confirm('Tienes cambios sin guardar. ¿Estás seguro de que quieres salir?')) {
                        return;
                    }
                }
                window.location.href = '<?php echo $site_url; ?>/all-courses/';
            }

            // Configurar seguimiento de cambios
            function setupChangeTracking() {
                const inputs = document.querySelectorAll('input, textarea, select');
                inputs.forEach(input => {
                    if (input.id !== 'courseCode') {
                        input.addEventListener('input', markAsChanged);
                        input.addEventListener('change', markAsChanged);
                    }
                });
            }

            // Inicialización
            document.addEventListener('DOMContentLoaded', function() {
                console.log('🚀 Edit Course ASG WordPress v2.0.0 loaded');

                const courseCode = ASG_UTILS.getUrlParameter('course') || 'course_1';

                setupChangeTracking();

                setTimeout(() => {
                    loadCourse(courseCode);
                }, 500);

                setTimeout(() => {
                    showNotification('✏️ Editor de curso cargado', 'success');
                }, 1000);

                // Advertir sobre cambios no guardados
                window.addEventListener('beforeunload', (e) => {
                    if (hasUnsavedChanges) {
                        e.preventDefault();
                        e.returnValue = 'Tienes cambios sin guardar. ¿Estás seguro de que quieres salir?';
                    }
                });
            });
        </script>
    </body>
    </html>
    <?php
}

/**
 * Registrar la página de editar curso
 */
function asg_register_edit_course_page() {
    $page_slug = 'edit-course';
    $page = get_page_by_path($page_slug);

    if (!$page) {
        $page_data = array(
            'post_title'    => 'Editar Curso ASG',
            'post_content'  => '[asg_edit_course]',
            'post_status'   => 'publish',
            'post_type'     => 'page',
            'post_name'     => $page_slug
        );

        wp_insert_post($page_data);
    }
}

/**
 * Shortcode para mostrar el editor de curso
 */
function asg_edit_course_shortcode($atts) {
    ob_start();
    asg_create_edit_course_page();
    return ob_get_clean();
}

// Registrar hooks
add_action('init', 'asg_register_edit_course_page');
add_shortcode('asg_edit_course', 'asg_edit_course_shortcode');

// Registrar ruta personalizada
add_action('init', function() {
    add_rewrite_rule('^edit-course/?$', 'index.php?asg_page=edit_course', 'top');
});

add_filter('query_vars', function($vars) {
    $vars[] = 'asg_page';
    return $vars;
});

add_action('template_redirect', function() {
    $asg_page = get_query_var('asg_page');
    if ($asg_page === 'edit_course') {
        asg_create_edit_course_page();
        exit;
    }
});

?>
