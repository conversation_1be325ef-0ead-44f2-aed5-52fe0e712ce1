<!--
         * ========================================
         * CREACIÓN DE CURSOS - ABILITYSEMINARSGROUP
         * ========================================
         *
         * Descripción: Sistema completo para la creación de nuevos cursos
         * con formulario multi-paso, validaciones y gestión de contenido.
         *
         * Funcionalidades:
         * - Formulario multi-paso para creación de cursos
         * - Validaciones en tiempo real
         * - Gestión de contenido y módulos
         * - Preview del curso en tiempo real
         * - Guardado automático y manual
         *
         * Autor: <PERSON>.M
         * Fecha: 2025
         * Versión: 1.0 - FIXED
         
-->
<!DOCTYPE html>
<html lang="es">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>New Course - AbilitySeminarsGroup</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.11.3/font/bootstrap-icons.css">
    <link href="https://fonts.googleapis.com/css2?family=Inter:ital,opsz,wght@0,14..32,100..900;1,14..32,100..900&display=swap" rel="stylesheet">
    
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background-color: #1e3a5f;
            overflow-x: hidden;
            min-height: 100vh;
        }

        /* Navbar Styles */
        .navbar {
            background-color: #1e3a5f;
            height: 70px;
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            z-index: 1030;
            padding: 0 1rem;
            border-bottom: 1px solid #2c5282;
        }

        .navbar .btn {
            color: white;
            border: none;
            background: none;
            padding: 0.5rem;
            border-radius: 4px;
            transition: background-color 0.2s ease;
        }

        .navbar .btn:hover {
            background-color: rgba(255, 255, 255, 0.1);
        }

        .navbar .btn:focus {
            box-shadow: none;
            outline: none;
        }

        /* .navbar-brand img filter removed for real logo */

        .avatar {
            width: 35px;
            height: 35px;
            background-color: #4299e1;
            font-weight: bold;
            font-size: 1rem;
        }

        /* Sidebar Styles */
        .sidebar {
            position: fixed;
            top: 0;
            left: 0;
            width: 220px;
            height: 100vh;
            background-color: #1e3a5f;
            color: white;
            transform: translateX(-100%);
            transition: all 0.3s ease;
            z-index: 1040;
            padding-top: 70px;
            border-right: 1px solid #2c5282;
        }

        .sidebar.show {
            transform: translateX(0);
        }

        .sidebar.collapsed {
            width: 55px;
        }

        .sidebar.collapsed .menu-text {
            display: none;
        }

        .sidebar.collapsed .dropdown {
            display: none;
        }

        .sidebar.collapsed:not(.desktop-hidden):hover {
            width: 220px;
        }

        .sidebar.collapsed:not(.desktop-hidden):hover .menu-text {
            display: inline;
        }

        .sidebar.collapsed:not(.desktop-hidden):hover .dropdown {
            display: inline;
        }

        /* Disable all hover effects when sidebar is hidden by hamburger button */
        .sidebar.desktop-hidden:hover {
            transform: translateX(-100%) !important;
            width: 220px !important;
        }

        .sidebar-nav {
            padding: 0.5rem 0;
            height: calc(100vh - 70px);
            overflow: visible;
        }

        .sidebar-parent > a {
            padding: 0.6rem 1rem;
            display: flex;
            align-items: center;
            color: #b8d4f0;
            text-decoration: none;
            transition: all 0.3s ease;
            border-left: 3px solid transparent;
            font-size: 0.9rem;
        }

        .sidebar-parent > a:hover,
        .sidebar-parent.active > a {
            background-color: #2c5282;
            color: white;
            border-left-color: #4299e1;
        }

        .sidebar-children {
            list-style: none;
            padding: 0;
            margin: 0;
            background-color: #2c5282;
            max-height: 0;
            overflow: hidden;
            transition: max-height 0.3s ease;
        }

        .sidebar-parent.expanded .sidebar-children {
            max-height: 200px;
        }

        .sidebar-children li a {
            padding: 0.4rem 1rem 0.4rem 2.5rem;
            display: flex;
            align-items: center;
            color: #b8d4f0;
            text-decoration: none;
            transition: all 0.3s ease;
            font-size: 0.85rem;
        }

        .sidebar-children li a:hover,
        .sidebar-children li a.active {
            background-color: #3182ce;
            color: white;
        }

        /* Body Content */
        .body-content {
            margin-left: 0;
            margin-top: 70px;
            transition: margin-left 0.3s ease;
            min-height: calc(100vh - 70px);
            background-color: #f8f9fa;
        }

        .body-content.collapsed {
            margin-left: 55px;
        }

        .page-header {
            background-color: #1e3a5f;
            color: white;
            padding: 1rem 2rem;
            margin-bottom: 0;
            position: sticky;
            top: 70px;
            z-index: 1020;
        }

        /* Main Content */
        .main-content {
            display: flex;
            justify-content: center;
            align-items: center;
            min-height: calc(100vh - 140px);
            padding: 2rem;
        }

        /* Modal Card */
        .modal-card {
            background: white;
            border-radius: 12px;
            box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
            width: 100%;
            max-width: 500px;
            padding: 2rem;
            position: relative;
        }

        .modal-header {
            text-align: center;
            margin-bottom: 2rem;
        }

        .modal-header i {
            font-size: 3rem;
            color: #1e3a5f;
            margin-bottom: 1rem;
        }

        .modal-header h3 {
            color: #1e3a5f;
            font-weight: 600;
            margin: 0;
        }

        /* Form Styles */
        .form-group {
            margin-bottom: 1.5rem;
        }

        .form-group label {
            display: block;
            margin-bottom: 0.5rem;
            color: #333;
            font-weight: 500;
        }

        .form-group input,
        .form-group textarea,
        .form-group select {
            width: 100%;
            padding: 0.75rem;
            border: 1px solid #ddd;
            border-radius: 6px;
            font-size: 1rem;
            transition: border-color 0.3s ease;
            background-color: white;
        }

        .form-group input:focus,
        .form-group textarea:focus,
        .form-group select:focus {
            outline: none;
            border-color: #4299e1;
            box-shadow: 0 0 0 3px rgba(66, 153, 225, 0.1);
        }

        .form-group select {
            cursor: pointer;
        }

        /* Image Upload */
        .image-upload-area {
            border: 2px dashed #ddd;
            border-radius: 8px;
            padding: 3rem 2rem;
            text-align: center;
            cursor: pointer;
            transition: all 0.3s ease;
            background-color: #f8f9fa;
        }

        .image-upload-area:hover {
            border-color: #4299e1;
            background-color: #f0f8ff;
        }

        .image-upload-area.dragover {
            border-color: #4299e1;
            background-color: #e6f3ff;
        }

        .image-upload-area i {
            font-size: 3rem;
            color: #999;
            margin-bottom: 1rem;
        }

        .file-status {
            margin-top: 1rem;
            font-size: 0.9rem;
        }

        .file-status.success {
            color: #28a745;
        }

        .file-status.error {
            color: #dc3545;
        }

        .image-preview {
            margin-top: 1rem;
            text-align: center;
        }

        .image-preview img {
            max-width: 200px;
            max-height: 150px;
            border-radius: 8px;
            border: 2px solid #ddd;
        }

        /* Price Selection */
        .price-options {
            display: flex;
            gap: 1rem;
            margin-bottom: 1.5rem;
        }

        .price-option {
            flex: 1;
            padding: 1rem;
            border: 2px solid #ddd;
            border-radius: 8px;
            text-align: center;
            cursor: pointer;
            transition: all 0.3s ease;
            background: white;
        }

        .price-option:hover {
            border-color: #4299e1;
        }

        .price-option.selected {
            border-color: #4299e1;
            background-color: #f0f8ff;
        }

        .price-option i {
            font-size: 1.5rem;
            margin-bottom: 0.5rem;
            color: #4299e1;
        }

        /* Buttons */
        .btn-custom {
            padding: 0.75rem 2rem;
            border: none;
            border-radius: 6px;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.3s ease;
            text-decoration: none;
            display: inline-block;
            text-align: center;
        }

        .btn-primary {
            background-color: #1e3a5f;
            color: white;
        }

        .btn-primary:hover {
            background-color: #2c5282;
        }

        .btn-primary:disabled {
            background-color: #6c757d;
            cursor: not-allowed;
        }

        .btn-secondary {
            background-color: #6c757d;
            color: white;
        }

        .btn-secondary:hover {
            background-color: #5a6268;
        }

        .modal-footer {
            display: flex;
            justify-content: space-between;
            margin-top: 2rem;
        }

        /* Alerts */
        .alert {
            padding: 1rem;
            border-radius: 6px;
            margin-bottom: 1rem;
            display: none;
        }

        .alert.show {
            display: block;
        }

        .alert-danger {
            background-color: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }

        .alert-success {
            background-color: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }

        .alert-warning {
            background-color: #fff3cd;
            color: #856404;
            border: 1px solid #ffeaa7;
        }

        /* Steps */
        .form-step {
            display: none;
        }

        .form-step.active {
            display: block;
        }

        /* Progress indicator */
        .progress-indicator {
            display: flex;
            justify-content: center;
            margin-bottom: 2rem;
        }

        .progress-step {
            width: 30px;
            height: 30px;
            border-radius: 50%;
            background-color: #ddd;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 0.5rem;
            font-weight: bold;
            color: #666;
        }

        .progress-step.active {
            background-color: #1e3a5f;
            color: white;
        }

        .progress-step.completed {
            background-color: #28a745;
            color: white;
        }

        /* Overlay */
        .sidebar-overlay {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0, 0, 0, 0.5);
            z-index: 1035;
            display: none;
        }

        .sidebar-overlay.show {
            display: block;
        }

        /* Responsive */
        @media (min-width: 992px) {
            .sidebar:not(.desktop-hidden) {
                transform: translateX(0);
                position: fixed;
            }

            .sidebar.desktop-hidden {
                transform: translateX(-100%);
            }

            .body-content {
                margin-left: 220px;
                transition: margin-left 0.3s ease;
            }

            .body-content.sidebar-hidden {
                margin-left: 0;
            }

            .sidebar-overlay {
                display: none !important;
            }
        }

        @media (max-width: 991px) {
            .main-content {
                padding: 1rem;
            }
            
            .modal-card {
                padding: 1.5rem;
            }
        }

        @media (min-width: 992px) and (max-width: 1200px) {
            .sidebar.auto-collapsed {
                width: 55px;
            }
            
            .sidebar.auto-collapsed .menu-text {
                display: none;
            }
            
            .sidebar.auto-collapsed .dropdown {
                display: none;
            }
            
            .body-content.auto-collapsed {
                margin-left: 55px;
            }
        }
    </style>
</head>
<body>
    <!-- Navbar -->
    <nav class="navbar">
        <div class="container-fluid d-flex align-items-center">
            <div class="d-flex align-items-center">
                <button class="btn fs-5 me-3" id="toggleSidebar">☰</button>
                <a class="navbar-brand p-0 pb-1" href="https://abilityseminarsgroup.com/admin-dashboard/">
                    <img src="https://abilityseminarsgroup.com/wp-content/uploads/2023/09/Asset-6-4-768x355.png" alt="Logo" style="width:130px;height:auto">
                </a>
            </div>

            <div class="ms-auto d-none d-md-flex align-items-center">
                <div class="dropdown me-3">
                    <i id="confi" class="bi bi-gear fs-5" data-bs-toggle="dropdown" role="button" aria-expanded="false" style="cursor: pointer;"></i>
                    <ul class="dropdown-menu dropdown-menu-end" aria-labelledby="confi">
                        <li><a class="dropdown-item" href="#"><i class="bi bi-person-circle fs-5 me-2"></i>Mi perfil</a></li>
                        <li><a class="dropdown-item" href="#"><i class="bi bi-shield-lock fs-5 me-2"></i>Contraseña</a></li>
                    </ul>
                </div>

                <figure class="m-0 me-4 ms-2 d-flex align-items-center">
                    <div class="avatar rounded-circle d-flex justify-content-center align-items-center text-light">
                        A
                    </div>
                    <p class="ms-2 mb-0 text-white">anthony sosa</p>
                    <i class="bi bi-chevron-down ms-2 text-white"></i>
                </figure>
            </div>
        </div>
    </nav>

    <!-- Sidebar Overlay -->
    <div class="sidebar-overlay" id="sidebarOverlay"></div>

    <!-- Sidebar -->
    <div id="sidebar" class="sidebar text-white">
        <div class="w-100 d-flex justify-content-end align-items-center" style="height: 50px; padding-top: 0.5rem;">
            <button id="toggleSidebar2" class="btn btn-sm text-white fs-6">☰</button>
        </div>
        
        <ul class="sidebar-nav list-unstyled">
            <li class="sidebar-parent">
                <a href="https://abilityseminarsgroup.com/admin-dashboard/" class="d-block text-white text-decoration-none">
                    <i class="bi bi-house fs-6 me-2"></i><span class="menu-text">Dashboard</span>
                </a>
            </li>
            
            <li class="sidebar-parent">
                <a href="#" class="d-block text-white text-decoration-none">
                    <i class="bi bi-credit-card-2-back fs-6 me-2"></i><span class="menu-text">Sales<i class="ms-2 bi bi-chevron-down dropdown"></i></span>
                </a>
                <ul class="sidebar-children">
                    <li>
                        <a href="#" class="">
                            <i class="bi-cart-check fs-6 me-2"></i><span class="menu-text">Offers</span>
                        </a>
                    </li>
                    <li>
                        <a href="#" class="">
                            <i class="bi bi-credit-card-fill fs-6 me-2"></i><span class="menu-text">Payments</span>
                        </a>
                    </li>
                </ul>
            </li>
            
            <li class="sidebar-parent">
                <a href="#" class="d-block text-white text-decoration-none">
                    <i class="bi bi-envelope fs-6 me-2"></i><span class="menu-text">Marketing<i class="ms-2 bi bi-chevron-down dropdown"></i></span>
                </a>
                <ul class="sidebar-children">
                    <li>
                        <a href="#" class="">
                            <i class="bi bi-envelope-fill fs-6 me-2"></i><span class="menu-text">Email Campaigns</span>
                        </a>
                    </li>
                </ul>
            </li>
            
            <li class="sidebar-parent active expanded">
                <a href="#" class="d-block text-white text-decoration-none">
                    <i class="bi bi-book fs-6 me-2"></i><span class="menu-text">Courses<i class="ms-2 bi bi-chevron-down dropdown"></i></span>
                </a>
                <ul class="sidebar-children">
                    <li>
                        <a href="https://abilityseminarsgroup.com/new-course/" class="active">
                            <i class="bi bi-plus-circle fs-6 me-2"></i><span class="menu-text">New Courses</span>
                        </a>
                    </li>
                    <li>
                        <a href="https://abilityseminarsgroup.com/all-courses/" class="">
                            <i class="bi bi-box-fill fs-6 me-2"></i><span class="menu-text">All Courses</span>
                        </a>
                    </li>
                </ul>
            </li>
            
            <li class="sidebar-parent">
                <a href="#" class="d-block text-white text-decoration-none">
                    <i class="bi bi-people-fill fs-6 me-2"></i><span class="menu-text">Students<i class="ms-2 bi bi-chevron-down dropdown"></i></span>
                </a>
                <ul class="sidebar-children">
                    <li>
                        <a href="#" class="">
                            <i class="bi bi-people fs-6 me-2"></i><span class="menu-text">All Students</span>
                        </a>
                    </li>
                    <li>
                        <a href="#" class="">
                            <i class="bi bi-clipboard-data fs-6 me-2"></i><span class="menu-text">Student Progress</span>
                        </a>
                    </li>
                    <li>
                        <a href="#" class="">
                            <i class="bi bi-chat-text-fill fs-6 me-2"></i><span class="menu-text">Student Message</span>
                        </a>
                    </li>
                </ul>
            </li>
            
            <li class="sidebar-parent">
                <a href="#" class="d-block text-white text-decoration-none">
                    <i class="bi bi-bar-chart-fill fs-6 me-2"></i><span class="menu-text">Analytics</span>
                </a>
            </li>
            
            <li class="sidebar-parent">
                <a href="#" class="d-block text-white text-decoration-none">
                    <i class="bi bi-gear fs-6 me-2"></i><span class="menu-text">Setting<i class="ms-2 bi bi-chevron-down dropdown"></i></span>
                </a>
                <ul class="sidebar-children">
                    <li>
                        <a href="#" class="">
                            <i class="bi bi-person-fill-gear fs-6 me-2"></i><span class="menu-text">Manage Users</span>
                        </a>
                    </li>
                    <li>
                        <a href="#" class="">
                            <i class="bi-file-earmark-text fs-6 me-2"></i><span class="menu-text">Manage Courses</span>
                        </a>
                    </li>
                </ul>
            </li>
            
            <li class="sidebar-parent">
                <a href="#" class="d-block text-white text-decoration-none">
                    <i class="bi bi-box-arrow-left fs-6 me-2"></i><span class="menu-text">Cerrar sesión</span>
                </a>
            </li>
        </ul>
    </div>

    <!-- Main Content -->
    <section class="body-content">
        <header class="page-header d-flex p-2 align-items-center">
            <h5 class="mb-0 px-3 py-2 text-white">New course</h5>
        </header>
        
        <section class="main-content">
            <div class="modal-card">
                <div class="modal-header">
                    <i class="bi bi-plus-circle"></i>
                    <h3>Create New Course</h3>
                </div>

                <!-- Progress Indicator -->
                <div class="progress-indicator">
                    <div class="progress-step active" id="progressStep1">1</div>
                    <div class="progress-step" id="progressStep2">2</div>
                    <div class="progress-step" id="progressStep3">3</div>
                    <div class="progress-step" id="progressStep4">4</div>
                </div>

                <!-- Alerts -->
                <div id="alertDanger" class="alert alert-danger">
                    <i class="bi bi-exclamation-triangle me-2"></i>
                    <span id="alertDangerText">Please fill out all required fields</span>
                </div>
                <div id="alertSuccess" class="alert alert-success">
                    <i class="bi bi-check-circle me-2"></i>
                    Your course has been created successfully!
                </div>
                <div id="alertWarning" class="alert alert-warning">
                    <i class="bi bi-exclamation-triangle me-2"></i>
                    <span id="alertWarningText">Warning message</span>
                </div>

                <form id="courseForm">
                    <!-- Step 1: Basic Info -->
                    <div id="step1" class="form-step active">
                        <div class="form-group">
                            <label for="title">Course Title<span style="color: red;">*</span></label>
                            <input type="text" id="title" name="title" placeholder="Enter course title" maxlength="100" required>
                            <small class="text-muted">Maximum 100 characters</small>
                        </div>
                        <div class="form-group">
                            <label for="description">Brief Description<span style="color: red;">*</span></label>
                            <textarea id="description" name="description" placeholder="Enter a brief description of your course" maxlength="500" rows="3" required></textarea>
                            <small class="text-muted">Maximum 500 characters</small>
                        </div>
                        <div class="form-group">
                            <label for="category">Category<span style="color: red;">*</span></label>
                            <select id="category" name="category" required>
                                <option value="">Loading categories...</option>
                            </select>
                        </div>
                    </div>

                    <!-- Step 2: Cover Image -->
                    <div id="step2" class="form-step">
                        <div class="form-group">
                            <label style="text-align: center; font-size: 1.2rem; margin-bottom: 1rem; display: block;">Course Cover Image</label>
                            <div class="image-upload-area" id="imageUploadArea">
                                <i class="bi bi-image"></i>
                                <p>Click to upload or drag and drop</p>
                                <p class="text-muted small">PNG, JPG, WebP up to 5MB</p>
                                <div id="fileStatus" class="file-status error">No file selected</div>
                            </div>
                            <input type="file" id="fileInput" accept="image/*" style="display: none;">
                            <div id="imagePreview" class="image-preview" style="display: none;">
                                <img id="previewImg" alt="Course preview">
                            </div>
                        </div>
                    </div>

                    <!-- Step 3: Price -->
                    <div id="step3" class="form-step">
                        <div class="price-options">
                            <div class="price-option selected" id="freeOption">
                                <i class="bi bi-gift"></i>
                                <div><strong>Free</strong></div>
                                <small>No cost to students</small>
                            </div>
                            <div class="price-option" id="paidOption">
                                <i class="bi bi-currency-dollar"></i>
                                <div><strong>Paid</strong></div>
                                <small>Set a price</small>
                            </div>
                        </div>
                        <div class="form-group">
                            <label for="price">Price<span style="color: red;">*</span></label>
                            <input type="text" id="price" name="price" value="$0.00" disabled>
                            <input type="hidden" id="finalPrice" name="finalPrice" value="0">
                            <small class="text-muted">Enter price in USD</small>
                        </div>
                    </div>

                    <!-- Step 4: Success -->
                    <div id="step4" class="form-step">
                        <div class="text-center mb-4">
                            <i class="bi bi-check-circle-fill text-success" style="font-size: 4rem;"></i>
                            <h4 class="text-success mt-3">Course Created Successfully!</h4>
                        </div>
                        <div class="form-group">
                            <label for="titleDisplay">Course Title</label>
                            <input type="text" id="titleDisplay" readonly class="form-control-plaintext">
                        </div>
                        <div class="form-group">
                            <label for="descriptionDisplay">Description</label>
                            <textarea id="descriptionDisplay" readonly class="form-control-plaintext" rows="3"></textarea>
                        </div>
                        <div class="form-group">
                            <label for="priceDisplay">Price</label>
                            <input type="text" id="priceDisplay" readonly class="form-control-plaintext">
                        </div>
                    </div>
                </form>

                <div class="modal-footer">
                    <button type="button" id="backBtn" class="btn-custom btn-secondary" style="display: none;">
                        <i class="bi bi-arrow-left me-2"></i>Back
                    </button>
                    <button type="button" id="nextBtn" class="btn-custom btn-primary">
                        Next<i class="bi bi-arrow-right ms-2"></i>
                    </button>
                    <button type="button" id="saveBtn" class="btn-custom btn-primary" style="display: none;">
                        <i class="bi bi-save me-2"></i>Create Course
                    </button>
                    <button type="button" id="finalBtn" class="btn-custom btn-primary" style="display: none;">
                        <i class="bi bi-list me-2"></i>View All Courses
                    </button>
                </div>
            </div>
        </section>
    </section>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.3/dist/js/bootstrap.bundle.min.js"></script>

   
    
    <script>
  
        // ========================================
        // VARIABLES GLOBALES
        // ========================================

        /**
         * Configuración del sistema de creación de cursos
         */
        const CourseCreator = {
            currentStep: 1,
            totalSteps: 4,
            courseData: {
                title: '',
                description: '',
                category: '',
                price: 0,
                imageFile: null,
                imageUrl: ''
            },
            elements: {},
            isSubmitting: false
        };

        // ========================================
        // INICIALIZACIÓN DEL SISTEMA
        // ========================================

        document.addEventListener('DOMContentLoaded', function() {
            console.log('🚀 Iniciando Sistema de Creación de Cursos...');
            
            // Inicializar elementos del DOM
            initializeElements();
            
            // Inicializar componentes
            initializeSidebar();
            initializeForm();
            initializeImageUpload();
            initializePriceOptions();
            initializeNavigation();
            
            // Cargar categorías
            loadCategories();
            
            console.log('✅ Sistema inicializado correctamente');
        });

        /**
         * Inicializar elementos del DOM
         */
        function initializeElements() {
            CourseCreator.elements = {
                // Form elements
                title: document.getElementById('title'),
                description: document.getElementById('description'),
                category: document.getElementById('category'),
                price: document.getElementById('price'),
                finalPrice: document.getElementById('finalPrice'),
                fileInput: document.getElementById('fileInput'),
                
                // Display elements
                titleDisplay: document.getElementById('titleDisplay'),
                descriptionDisplay: document.getElementById('descriptionDisplay'),
                priceDisplay: document.getElementById('priceDisplay'),
                
                // Navigation buttons
                backBtn: document.getElementById('backBtn'),
                nextBtn: document.getElementById('nextBtn'),
                saveBtn: document.getElementById('saveBtn'),
                finalBtn: document.getElementById('finalBtn'),
                
                // Price options
                freeOption: document.getElementById('freeOption'),
                paidOption: document.getElementById('paidOption'),
                
                // Upload elements
                imageUploadArea: document.getElementById('imageUploadArea'),
                fileStatus: document.getElementById('fileStatus'),
                imagePreview: document.getElementById('imagePreview'),
                previewImg: document.getElementById('previewImg'),
                
                // Alerts
                alertDanger: document.getElementById('alertDanger'),
                alertSuccess: document.getElementById('alertSuccess'),
                alertWarning: document.getElementById('alertWarning'),
                alertDangerText: document.getElementById('alertDangerText'),
                alertWarningText: document.getElementById('alertWarningText'),
                
                // Sidebar
                sidebar: document.getElementById('sidebar'),
                sidebarOverlay: document.getElementById('sidebarOverlay'),
                bodyContent: document.querySelector('.body-content'),
                toggleSidebar: document.getElementById('toggleSidebar'),
                toggleSidebar2: document.getElementById('toggleSidebar2')
            };
            
            console.log('✅ Elementos del DOM inicializados');
        }

        /**
         * Inicializar sidebar
         */
        function initializeSidebar() {
            const { sidebar, sidebarOverlay, bodyContent, toggleSidebar, toggleSidebar2 } = CourseCreator.elements;
            
            // Toggle sidebar
            toggleSidebar.addEventListener('click', () => toggleSidebarVisibility());
            toggleSidebar2.addEventListener('click', () => toggleSidebarVisibility());
            
            // Close sidebar on overlay click (mobile)
            sidebarOverlay.addEventListener('click', () => closeSidebar());
            
            // Handle submenu toggles
            document.querySelectorAll('.sidebar-parent').forEach(parent => {
                const link = parent.querySelector('a');
                const hasChildren = parent.querySelector('.sidebar-children');
                
                if (hasChildren) {
                    link.addEventListener('click', (e) => {
                        e.preventDefault();
                        parent.classList.toggle('expanded');
                    });
                }
            });
            
            // Handle responsive behavior
            window.addEventListener('resize', handleSidebarResize);
            
            console.log('✅ Sidebar inicializado');
        }

        /**
         * Toggle sidebar visibility
         */
        function toggleSidebarVisibility() {
            const { sidebar, sidebarOverlay, bodyContent } = CourseCreator.elements;
            
            if (window.innerWidth >= 992) {
                // Desktop behavior
                sidebar.classList.toggle('desktop-hidden');
                bodyContent.classList.toggle('sidebar-hidden');
            } else {
                // Mobile behavior
                sidebar.classList.toggle('show');
                sidebarOverlay.classList.toggle('show');
            }
        }

        /**
         * Close sidebar (mobile)
         */
        function closeSidebar() {
            const { sidebar, sidebarOverlay } = CourseCreator.elements;
            sidebar.classList.remove('show');
            sidebarOverlay.classList.remove('show');
        }

        /**
         * Handle sidebar responsive behavior
         */
        function handleSidebarResize() {
            const { sidebar, sidebarOverlay, bodyContent } = CourseCreator.elements;
            
            if (window.innerWidth >= 992) {
                // Desktop: clean mobile classes
                sidebar.classList.remove('show');
                sidebarOverlay.classList.remove('show');
            } else {
                // Mobile: clean desktop classes
                sidebar.classList.remove('desktop-hidden');
                bodyContent.classList.remove('sidebar-hidden');
            }
        }

        /**
         * Inicializar formulario
         */
        function initializeForm() {
            const { title, description, category } = CourseCreator.elements;
            
            // Add real-time validation
            title.addEventListener('input', validateTitle);
            description.addEventListener('input', validateDescription);
            category.addEventListener('change', validateCategory);
            
            console.log('✅ Formulario inicializado');
        }

        /**
         * Validar título
         */
        function validateTitle() {
            const { title } = CourseCreator.elements;
            const value = title.value.trim();
            
            if (value.length < 3) {
                title.style.borderColor = '#dc3545';
                return false;
            } else {
                title.style.borderColor = '#28a745';
                return true;
            }
        }

        /**
         * Validar descripción
         */
        function validateDescription() {
            const { description } = CourseCreator.elements;
            const value = description.value.trim();
            
            if (value.length < 10) {
                description.style.borderColor = '#dc3545';
                return false;
            } else {
                description.style.borderColor = '#28a745';
                return true;
            }
        }

        /**
         * Validar categoría
         */
        function validateCategory() {
            const { category } = CourseCreator.elements;
            const value = category.value;
            
            if (!value) {
                category.style.borderColor = '#dc3545';
                return false;
            } else {
                category.style.borderColor = '#28a745';
                return true;
            }
        }

        /**
         * Inicializar subida de imágenes
         */
        function initializeImageUpload() {
            const { imageUploadArea, fileInput, fileStatus, imagePreview, previewImg } = CourseCreator.elements;
            
            // Click to upload
            imageUploadArea.addEventListener('click', () => fileInput.click());
            
            // File input change
            fileInput.addEventListener('change', handleFileSelect);
            
            // Drag and drop
            imageUploadArea.addEventListener('dragover', handleDragOver);
            imageUploadArea.addEventListener('dragleave', handleDragLeave);
            imageUploadArea.addEventListener('drop', handleFileDrop);
            
            console.log('✅ Subida de imágenes inicializada');
        }

        /**
         * Handle file selection
         */
        function handleFileSelect(event) {
            const file = event.target.files[0];
            if (file) {
                processImageFile(file);
            }
        }

        /**
         * Handle drag over
         */
        function handleDragOver(event) {
            event.preventDefault();
            CourseCreator.elements.imageUploadArea.classList.add('dragover');
        }

        /**
         * Handle drag leave
         */
        function handleDragLeave(event) {
            event.preventDefault();
            CourseCreator.elements.imageUploadArea.classList.remove('dragover');
        }

        /**
         * Handle file drop
         */
        function handleFileDrop(event) {
            event.preventDefault();
            CourseCreator.elements.imageUploadArea.classList.remove('dragover');
            
            const files = event.dataTransfer.files;
            if (files.length > 0) {
                processImageFile(files[0]);
            }
        }

        /**
         * Process image file
         */
        function processImageFile(file) {
            const { fileStatus, imagePreview, previewImg } = CourseCreator.elements;
            
            // Validate file
            if (!file.type.startsWith('image/')) {
                showAlert('danger', 'Please select a valid image file');
                return;
            }
            
            if (file.size > 5 * 1024 * 1024) {
                showAlert('danger', 'Image size must be less than 5MB');
                return;
            }
            
            // Store file
            CourseCreator.courseData.imageFile = file;
            
            // Update status
            fileStatus.textContent = `File selected: ${file.name}`;
            fileStatus.classList.remove('error');
            fileStatus.classList.add('success');
            
            // Show preview
            const reader = new FileReader();
            reader.onload = function(e) {
                previewImg.src = e.target.result;
                imagePreview.style.display = 'block';
            };
            reader.readAsDataURL(file);
            
            console.log('📸 Imagen procesada:', file.name);
        }

        /**
         * Inicializar opciones de precio
         */
        function initializePriceOptions() {
            const { freeOption, paidOption, price, finalPrice } = CourseCreator.elements;
            
            // Free option
            freeOption.addEventListener('click', () => {
                freeOption.classList.add('selected');
                paidOption.classList.remove('selected');
                price.value = '$0.00';
                price.disabled = true;
                finalPrice.value = '0';
                CourseCreator.courseData.price = 0;
                console.log('💰 Opción FREE seleccionada');
            });
            
            // Paid option
            paidOption.addEventListener('click', () => {
                paidOption.classList.add('selected');
                freeOption.classList.remove('selected');
                price.value = '';
                price.disabled = false;
                price.focus();
                console.log('💰 Opción PAID seleccionada');
            });
            
            // Price input formatting
            price.addEventListener('input', function() {
                let value = this.value.replace(/[^0-9.]/g, '');
                let number = parseFloat(value);
                
                if (!isNaN(number) && number >= 0) {
                    finalPrice.value = number;
                    this.value = '$' + number.toFixed(2);
                    CourseCreator.courseData.price = number;
                } else {
                    this.value = '$0.00';
                    finalPrice.value = '0';
                    CourseCreator.courseData.price = 0;
                }
            });
            
            console.log('✅ Opciones de precio inicializadas');
        }

        /**
         * Inicializar navegación
         */
        function initializeNavigation() {
            const { backBtn, nextBtn, saveBtn, finalBtn } = CourseCreator.elements;
            
            // Navigation buttons
            nextBtn.addEventListener('click', handleNext);
            backBtn.addEventListener('click', handleBack);
            saveBtn.addEventListener('click', handleSave);
            finalBtn.addEventListener('click', () => {
                window.location.href = 'https://abilityseminarsgroup.com/all-courses/';
            });
            
            // Initialize first step
            showStep(1);
            
            console.log('✅ Navegación inicializada');
        }

        /**
         * Handle next button
         */
        function handleNext() {
            if (validateCurrentStep()) {
                if (CourseCreator.currentStep < CourseCreator.totalSteps - 1) {
                    CourseCreator.currentStep++;
                    showStep(CourseCreator.currentStep);
                }
            }
        }

        /**
         * Handle back button
         */
        function handleBack() {
            if (CourseCreator.currentStep > 1) {
                CourseCreator.currentStep--;
                showStep(CourseCreator.currentStep);
            }
        }

        /**
         * Handle save button
         */
        async function handleSave() {
            if (CourseCreator.isSubmitting) {
                return;
            }
            
            if (validateCurrentStep()) {
                await saveCourse();
            }
        }

        /**
         * Validate current step
         */
        function validateCurrentStep() {
            const step = CourseCreator.currentStep;
            
            switch (step) {
                case 1:
                    return validateStep1();
                case 2:
                    return validateStep2();
                case 3:
                    return validateStep3();
                default:
                    return true;
            }
        }

        /**
         * Validate step 1
         */
        function validateStep1() {
            const { title, description, category } = CourseCreator.elements;
            
            if (!title.value.trim()) {
                showAlert('danger', 'Course title is required');
                title.focus();
                return false;
            }
            
            if (title.value.trim().length < 3) {
                showAlert('danger', 'Course title must be at least 3 characters');
                title.focus();
                return false;
            }
            
            if (!description.value.trim()) {
                showAlert('danger', 'Course description is required');
                description.focus();
                return false;
            }
            
            if (description.value.trim().length < 10) {
                showAlert('danger', 'Course description must be at least 10 characters');
                description.focus();
                return false;
            }
            
            if (!category.value) {
                showAlert('danger', 'Please select a category');
                category.focus();
                return false;
            }
            
            // Store data
            CourseCreator.courseData.title = title.value.trim();
            CourseCreator.courseData.description = description.value.trim();
            CourseCreator.courseData.category = category.value;
            
            return true;
        }

        /**
         * Validate step 2
         */
        function validateStep2() {
            // Image is optional, but if provided, should be valid
            return true;
        }

        /**
         * Validate step 3
         */
        function validateStep3() {
            const { finalPrice } = CourseCreator.elements;
            
            const price = parseFloat(finalPrice.value);
            if (isNaN(price) || price < 0) {
                showAlert('danger', 'Please set a valid price');
                return false;
            }
            
            CourseCreator.courseData.price = price;
            return true;
        }

        /**
         * Show specific step
         */
        function showStep(step) {
            // Hide all steps
            for (let i = 1; i <= CourseCreator.totalSteps; i++) {
                const stepElement = document.getElementById(`step${i}`);
                const progressStep = document.getElementById(`progressStep${i}`);
                
                if (stepElement) {
                    stepElement.classList.remove('active');
                }
                
                if (progressStep) {
                    progressStep.classList.remove('active', 'completed');
                    if (i < step) {
                        progressStep.classList.add('completed');
                    } else if (i === step) {
                        progressStep.classList.add('active');
                    }
                }
            }
            
            // Show current step
            const currentStepElement = document.getElementById(`step${step}`);
            if (currentStepElement) {
                currentStepElement.classList.add('active');
            }
            
            // Update buttons
            const { backBtn, nextBtn, saveBtn, finalBtn } = CourseCreator.elements;
            
            backBtn.style.display = step > 1 ? 'inline-block' : 'none';
            nextBtn.style.display = step < 3 ? 'inline-block' : 'none';
            saveBtn.style.display = step === 3 ? 'inline-block' : 'none';
            finalBtn.style.display = step === 4 ? 'inline-block' : 'none';
            
            console.log(`📄 Mostrando paso ${step}`);
        }

        /**
         * Save course
         */
        async function saveCourse() {
            try {
                CourseCreator.isSubmitting = true;
                
                // Show loading state
                const { saveBtn } = CourseCreator.elements;
                const originalText = saveBtn.innerHTML;
                saveBtn.innerHTML = '<i class="bi bi-hourglass-split me-2"></i>Creating Course...';
                saveBtn.disabled = true;
                
                console.log('💾 Guardando curso...');
                
                // 1. Upload image if exists
                let imageUrl = '';
                if (CourseCreator.courseData.imageFile) {
                    console.log('📸 Subiendo imagen...');
                    imageUrl = await uploadImage(CourseCreator.courseData.imageFile);
                }
                
                // 2. Prepare course data
                const courseData = {
                    title: CourseCreator.courseData.title,
                    description: CourseCreator.courseData.description,
                    category: CourseCreator.courseData.category,
                    price: CourseCreator.courseData.price,
                    duration: 0,
                    language: 'es',
                    featured_image_url: imageUrl,
                    modules: [],
                    learn_objectives: [],
                    benefits: []
                };
                
                console.log('📝 Datos del curso:', courseData);
                
                // 3. Create course
                const response = await fetch('https://abilityseminarsgroup.com/wp-json/asg/v1/courses', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify(courseData)
                });
                
                console.log('📡 Respuesta del servidor:', response.status, response.statusText);
                
                if (!response.ok) {
                    throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                }
                
                const result = await response.json();
                console.log('📋 Resultado:', result);
                
                if (result.success) {
                    console.log('✅ Curso creado exitosamente:', result.data);
                    
                    // Show success data
                    showSuccessData(courseData);
                    
                    // Go to success step
                    CourseCreator.currentStep = 4;
                    showStep(CourseCreator.currentStep);
                    
                    showAlert('success', 'Course created successfully!');
                    
                } else {
                    throw new Error(result.error?.message || 'Unknown error creating course');
                }
                
            } catch (error) {
                console.error('❌ Error guardando curso:', error);
                showAlert('danger', `Error creating course: ${error.message}`);
                
            } finally {
                CourseCreator.isSubmitting = false;
                
                // Restore button
                const { saveBtn } = CourseCreator.elements;
                saveBtn.innerHTML = '<i class="bi bi-save me-2"></i>Create Course';
                saveBtn.disabled = false;
            }
        }

        /**
         * Upload image
         */
        async function uploadImage(file) {
            try {
                const formData = new FormData();
                formData.append('image', file);
                formData.append('type', 'course');
                
                const response = await fetch('https://abilityseminarsgroup.com/wp-json/asg/v1/courses/upload-image', {
                    method: 'POST',
                    body: formData
                });
                
                if (!response.ok) {
                    throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                }
                
                const result = await response.json();
                
                if (result.success) {
                    console.log('✅ Imagen subida:', result.data.urls.original);
                    return result.data.urls.original;
                } else {
                    throw new Error(result.error?.message || 'Error uploading image');
                }
                
            } catch (error) {
                console.error('❌ Error subiendo imagen:', error);
                showAlert('warning', 'Image upload failed, but course will be created without image');
                return '';
            }
        }

        /**
         * Show success data
         */
        function showSuccessData(courseData) {
            const { titleDisplay, descriptionDisplay, priceDisplay } = CourseCreator.elements;
            
            titleDisplay.value = courseData.title;
            descriptionDisplay.value = courseData.description;
            priceDisplay.value = courseData.price === 0 ? 'Free' : `$${courseData.price.toFixed(2)}`;
        }

        /**
         * Load categories
         */
        async function loadCategories() {
            try {
                console.log('📋 Cargando categorías...');
                
                const response = await fetch('https://abilityseminarsgroup.com/wp-json/asg/v1/courses/categories');
                const result = await response.json();
                
                if (result.success) {
                    const { category } = CourseCreator.elements;
                    
                    // Clear existing options
                    category.innerHTML = '<option value="">Select a category...</option>';
                    
                    // Add categories
                    Object.entries(result.data).forEach(([value, label]) => {
                        const option = document.createElement('option');
                        option.value = value;
                        option.textContent = label;
                        category.appendChild(option);
                    });
                    
                    console.log('✅ Categorías cargadas:', Object.keys(result.data).length);
                    
                } else {
                    throw new Error(result.error?.message || 'Error loading categories');
                }
                
            } catch (error) {
                console.error('❌ Error cargando categorías:', error);
                
                // Fallback categories
                const { category } = CourseCreator.elements;
                category.innerHTML = `
                    <option value="">Select a category...</option>
                    <option value="business">Business</option>
                    <option value="technology">Technology</option>
                    <option value="marketing">Marketing</option>
                    <option value="leadership">Leadership</option>
                    <option value="sales">Sales</option>
                    <option value="finance">Finance</option>
                `;
            }
        }

        /**
         * Show alert
         */
        function showAlert(type, message, duration = 5000) {
            const alertElement = CourseCreator.elements[`alert${type.charAt(0).toUpperCase() + type.slice(1)}`];
            const textElement = CourseCreator.elements[`alert${type.charAt(0).toUpperCase() + type.slice(1)}Text`];
            
            if (alertElement) {
                if (textElement) {
                    textElement.textContent = message;
                }
                
                // Hide all alerts first
                Object.keys(CourseCreator.elements).forEach(key => {
                    if (key.startsWith('alert') && CourseCreator.elements[key].classList) {
                        CourseCreator.elements[key].classList.remove('show');
                    }
                });
                
                // Show current alert
                alertElement.classList.add('show');
                
                // Auto hide
                setTimeout(() => {
                    alertElement.classList.remove('show');
                }, duration);
            }
        }

        // ========================================
        // FIN DEL SISTEMA
        // ========================================

        console.log('🎉 Sistema de Creación de Cursos cargado completamente');
    </script>
</body>
</html>
