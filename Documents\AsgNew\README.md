# 🎓 AbilitySeminarsGroup - Sistema de Gestión de Cursos v2.0

## 📋 **DESCRIPCIÓN DEL PROYECTO**

Sistema completo de gestión de cursos para AbilitySeminarsGroup, completamente reestructurado con tecnologías modernas y arquitectura escalable.

### **🎯 CARACTERÍSTICAS PRINCIPALES**

- ✅ **Dashboard moderno** con métricas en tiempo real
- ✅ **Gestión completa de cursos** con filtros avanzados y búsqueda
- ✅ **Creación de cursos** con formulario multi-paso intuitivo
- ✅ **Editor avanzado** con gestión visual de módulos y lecciones
- ✅ **Sistema dinámico** basado en `course_id` para frontend público
- ✅ **API REST completa** con endpoints unificados
- ✅ **Design System** consistente y responsive
- ✅ **Componentes reutilizables** en JavaScript

## 🏗️ **ARQUITECTURA DEL SISTEMA**

### **Frontend (Panel Administrativo)**
```
📁 Documents/AsgNew/
├── 📄 dashboard-v2.html          # Dashboard principal
├── 📄 all-courses-v2.html        # Gestión de cursos
├── 📄 new-course-v2.html         # Creación de cursos
├── 📄 edit-course-v2.html        # Edición avanzada
├── 📁 assets/
│   ├── 📁 css/
│   │   └── 📄 asg-design-system.css    # Sistema de diseño
│   └── 📁 js/
│       ├── 📄 asg-components.js        # Componentes base
│       ├── 📄 dashboard-v2.js          # Lógica del dashboard
│       ├── 📄 courses-manager-v2.js    # Gestión de cursos
│       ├── 📄 new-course-v2.js         # Creación de cursos
│       └── 📄 edit-course-v2.js        # Editor avanzado
```

### **Backend (API REST)**
```
📁 api/
├── 📄 asg-api-v2.php              # API principal
├── 📄 asg-api-utilities.php       # Utilidades y helpers
└── 📁 database/
    ├── 📄 asg-database-schema.sql # Esquema de base de datos
    └── 📄 DATABASE_DESIGN_DECISIONS.md
```

## 🗄️ **BASE DE DATOS**

### **Estructura Optimizada**
- **`wpic_courses`** - Cursos principales con metadatos completos
- **`wpic_modules`** - Módulos organizados por curso
- **`wpic_lessons`** - Lecciones dentro de cada módulo
- **`wpic_learn_list`** - Objetivos de aprendizaje
- **`wpic_benefit_list`** - Beneficios del curso
- **`wpic_course_meta`** - Metadatos flexibles

### **Características Técnicas**
- ✅ **Códigos únicos** (`course_1`, `course_2`) para sistema dinámico
- ✅ **Relaciones jerárquicas** con integridad referencial
- ✅ **Eliminación lógica** con campo `is_deleted`
- ✅ **Auditoría completa** con timestamps y usuarios
- ✅ **Índices optimizados** para consultas frecuentes

## 🔌 **API REST**

### **Endpoints Públicos (Frontend Dinámico)**
```
GET /wp-json/asg/v2/public/courses/{code}     # Curso completo
GET /wp-json/asg/v2/public/courses           # Lista de cursos
```

### **Endpoints Administrativos**
```
GET    /wp-json/asg/v2/admin/courses         # Lista de cursos
POST   /wp-json/asg/v2/admin/courses         # Crear curso
GET    /wp-json/asg/v2/admin/courses/{code}  # Obtener curso
PUT    /wp-json/asg/v2/admin/courses/{code}  # Actualizar curso
DELETE /wp-json/asg/v2/admin/courses/{code}  # Eliminar curso
GET    /wp-json/asg/v2/admin/dashboard/stats # Estadísticas
```

## 🎨 **DESIGN SYSTEM**

### **Variables CSS Principales**
```css
:root {
  --asg-primary: #2563eb;
  --asg-success: #10b981;
  --asg-warning: #f59e0b;
  --asg-error: #ef4444;
  --asg-gray-50: #f8fafc;
  --asg-gray-900: #0f172a;
}
```

### **Componentes Disponibles**
- ✅ **Botones** (primary, secondary, success, danger)
- ✅ **Cards** con header, body y footer
- ✅ **Formularios** con validaciones
- ✅ **Badges** de estado
- ✅ **Sidebar** responsive
- ✅ **Notificaciones** toast
- ✅ **Loading** overlays

## 🚀 **INSTALACIÓN Y CONFIGURACIÓN**

### **1. Configuración de Base de Datos**
```sql
-- Ejecutar el esquema de base de datos
SOURCE database/asg-database-schema.sql;
```

### **2. Configuración de WordPress**
```php
// Agregar en functions.php o usar WP Code Snippets
require_once 'api/asg-api-v2.php';
require_once 'api/asg-api-utilities.php';
```

### **3. Configuración de URLs**
Actualizar las URLs en los archivos HTML según tu dominio:
```javascript
// En asg-components.js
const ASG_CONFIG = {
    apiBaseUrl: 'https://tu-dominio.com/wp-json/asg/v2',
    logoUrl: 'https://tu-dominio.com/logo.png'
};
```

## 📱 **RESPONSIVE DESIGN**

### **Breakpoints**
- **Mobile**: < 640px
- **Tablet**: 640px - 1024px  
- **Desktop**: > 1024px

### **Características Responsive**
- ✅ **Sidebar colapsable** en móviles
- ✅ **Grid adaptativo** para cards
- ✅ **Formularios optimizados** para touch
- ✅ **Navegación simplificada** en pantallas pequeñas

## 🔧 **FUNCIONALIDADES PRINCIPALES**

### **Dashboard**
- 📊 **Métricas en tiempo real** (cursos, ingresos, estadísticas)
- 🔄 **Auto-refresh** cada 5 minutos
- 📈 **Animaciones de números** con easing
- 🎯 **Acciones rápidas** para tareas comunes

### **Gestión de Cursos**
- 🔍 **Búsqueda en tiempo real** con debounce
- 🏷️ **Filtros múltiples** (estado, categoría)
- 👁️ **Vista grid/list** intercambiable
- 📄 **Paginación** inteligente
- ⭐ **Calificaciones** y estadísticas

### **Creación de Cursos**
- 📝 **Formulario multi-paso** con validaciones
- 👀 **Preview en tiempo real** del curso
- 💾 **Auto-guardado** cada 30 segundos
- 🖼️ **Subida de imágenes** con validación
- ✅ **Validaciones** en tiempo real

### **Editor Avanzado**
- 🏷️ **Sistema de tabs** para organización
- 🧩 **Gestión visual** de módulos y lecciones
- 🎯 **Objetivos y beneficios** editables
- ⚙️ **Configuración avanzada** (SEO, estado)
- 💾 **Auto-guardado** cada 2 minutos

## 🌐 **SISTEMA DINÁMICO**

### **Concepto Clave**
El sistema está diseñado para que el **frontend público** use una sola página `/course` que carga contenido dinámicamente basado en el `course_id`:

```
/course?id=course_1  → Carga "Como hacerte millonario?"
/course?id=course_2  → Carga "Marketing Digital Avanzado"
```

### **API para Frontend Público**
```javascript
// Obtener curso completo para mostrar al estudiante
fetch('/wp-json/asg/v2/public/courses/course_1')
  .then(response => response.json())
  .then(data => {
    // data.course - Información del curso
    // data.modules - Módulos con lecciones
    // data.objectives - Objetivos de aprendizaje
    // data.benefits - Beneficios del curso
  });
```

## 🔒 **SEGURIDAD**

### **Validaciones Implementadas**
- ✅ **Sanitización** de inputs con WordPress functions
- ✅ **Validación** de tipos de archivo y tamaños
- ✅ **Escape** de outputs para prevenir XSS
- ✅ **Prepared statements** para consultas SQL
- ✅ **Nonces** para formularios (pendiente implementar)

### **Permisos**
```php
// Verificación de permisos (a implementar)
function asg_check_admin_permissions() {
    return current_user_can('manage_options') || 
           current_user_can('edit_courses');
}
```

## 🧪 **TESTING**

### **Datos de Prueba**
El sistema incluye datos simulados para desarrollo:
- 6 cursos de ejemplo con diferentes estados
- Módulos y lecciones de muestra
- Objetivos y beneficios predefinidos
- Estadísticas realistas para el dashboard

### **Testing Manual**
1. **Dashboard**: Verificar carga de métricas y cursos recientes
2. **Gestión**: Probar filtros, búsqueda y paginación
3. **Creación**: Completar formulario multi-paso
4. **Edición**: Modificar curso existente y guardar cambios

## 📚 **DOCUMENTACIÓN TÉCNICA**

### **Decisiones de Arquitectura**
- **Separación de responsabilidades**: Frontend admin vs. Frontend público
- **API-first approach**: Toda la lógica en endpoints REST
- **Component-based**: JavaScript modular y reutilizable
- **Mobile-first**: Diseño responsive desde el inicio

### **Patrones Utilizados**
- **Module Pattern** para JavaScript
- **Observer Pattern** para eventos
- **Factory Pattern** para crear elementos DOM
- **Singleton Pattern** para componentes globales

## 🔄 **PRÓXIMOS PASOS**

### **Funcionalidades Pendientes**
- [ ] **Gestión de lecciones** dentro de módulos
- [ ] **Editor de contenido** rich text
- [ ] **Sistema de archivos** multimedia
- [ ] **Analíticas avanzadas** con gráficos
- [ ] **Notificaciones push** en tiempo real
- [ ] **Backup automático** de contenido

### **Optimizaciones**
- [ ] **Lazy loading** de imágenes
- [ ] **Service Workers** para cache
- [ ] **Compresión** de assets
- [ ] **CDN** para recursos estáticos

## 👥 **EQUIPO DE DESARROLLO**

- **ASG Team** - Desarrollo completo del sistema
- **Arquitectura**: Sistema modular y escalable
- **Frontend**: HTML5, CSS3, JavaScript ES6+
- **Backend**: PHP 7.4+, WordPress REST API
- **Base de datos**: MySQL 5.7+

## 📞 **SOPORTE**

Para soporte técnico o consultas sobre el sistema:
- 📧 **Email**: <EMAIL>
- 📱 **Documentación**: Ver archivos en `/database/`
- 🐛 **Issues**: Reportar en el sistema de gestión

---

**© 2025 AbilitySeminarsGroup - Sistema de Gestión de Cursos v2.0**
