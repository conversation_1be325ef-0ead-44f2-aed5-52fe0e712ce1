<!DOCTYPE html>
<html lang="es">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Edit Course - AbilitySeminarsGroup</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.11.3/font/bootstrap-icons.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Inter:ital,opsz,wght@0,14..32,100..900;1,14..32,100..900&display=swap" rel="stylesheet">
    
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, '<PERSON><PERSON>e UI', <PERSON>o, sans-serif;
            background-color: #f8f9fa;
            overflow-x: hidden;
            min-height: 100vh;
        }

        /* Navbar Styles */
        .navbar {
            background-color: #1e3a5f;
            height: 70px;
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            z-index: 1030;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
        }

        .navbar-brand {
            color: white !important;
            font-weight: 600;
            font-size: 1.25rem;
        }

        .navbar-nav .nav-link {
            color: rgba(255, 255, 255, 0.8) !important;
            font-weight: 500;
            transition: color 0.3s ease;
        }

        .navbar-nav .nav-link:hover {
            color: #f6d55c !important;
        }

        .navbar-toggler {
            border: none;
            color: white;
        }

        .navbar-toggler:focus {
            box-shadow: none;
        }

        /* Sidebar Styles */
        .sidebar {
            position: fixed;
            top: 70px;
            left: -280px;
            width: 280px;
            height: calc(100vh - 70px);
            background: white;
            box-shadow: 2px 0 10px rgba(0, 0, 0, 0.1);
            transition: left 0.3s ease;
            z-index: 1020;
            overflow-y: auto;
        }

        .sidebar.active {
            left: 0;
        }

        .sidebar-overlay {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.5);
            z-index: 1010;
            opacity: 0;
            visibility: hidden;
            transition: all 0.3s ease;
        }

        .sidebar-overlay.active {
            opacity: 1;
            visibility: visible;
        }

        .sidebar-header {
            padding: 1.5rem;
            border-bottom: 1px solid #e9ecef;
            background: #f8f9fa;
        }

        .sidebar-header h5 {
            margin: 0;
            color: #1e3a5f;
            font-weight: 600;
        }

        .sidebar-menu {
            padding: 1rem 0;
        }

        .sidebar-menu .menu-item {
            display: block;
            padding: 0.75rem 1.5rem;
            color: #495057;
            text-decoration: none;
            transition: all 0.3s ease;
            border: none;
            background: none;
            width: 100%;
            text-align: left;
        }

        .sidebar-menu .menu-item:hover {
            background: #f8f9fa;
            color: #1e3a5f;
            padding-left: 2rem;
        }

        .sidebar-menu .menu-item i {
            width: 20px;
            margin-right: 0.75rem;
        }

        /* Main Content */
        .main-content {
            margin-top: 70px;
            padding: 2rem;
            transition: margin-left 0.3s ease;
        }

        @media (min-width: 992px) {
            .sidebar {
                left: 0;
            }
            
            .main-content {
                margin-left: 280px;
            }
            
            .sidebar-overlay {
                display: none;
            }
        }

        /* Page Header */
        .page-header {
            background: white;
            border-radius: 12px;
            padding: 2rem;
            margin-bottom: 2rem;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
        }

        .page-title {
            color: #1e3a5f;
            font-size: 2rem;
            font-weight: 700;
            margin-bottom: 0.5rem;
        }

        .page-subtitle {
            color: #6c757d;
            font-size: 1.1rem;
            margin: 0;
        }

        /* Course Info Card */
        .course-info-card {
            background: white;
            border-radius: 12px;
            padding: 2rem;
            margin-bottom: 2rem;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
        }

        .course-image {
            width: 100%;
            max-width: 300px;
            height: 200px;
            object-fit: cover;
            border-radius: 8px;
            margin-bottom: 1rem;
        }

        .course-title {
            color: #1e3a5f;
            font-size: 1.5rem;
            font-weight: 600;
            margin-bottom: 0.5rem;
        }

        .course-description {
            color: #6c757d;
            margin-bottom: 1rem;
        }

        .course-meta {
            display: flex;
            gap: 1rem;
            flex-wrap: wrap;
        }

        .meta-item {
            display: flex;
            align-items: center;
            gap: 0.5rem;
            color: #495057;
            font-size: 0.9rem;
        }

        .meta-item i {
            color: #f6d55c;
        }

        /* Form Styles */
        .form-section {
            background: white;
            border-radius: 12px;
            padding: 2rem;
            margin-bottom: 2rem;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
        }

        .section-title {
            color: #1e3a5f;
            font-size: 1.25rem;
            font-weight: 600;
            margin-bottom: 1.5rem;
            padding-bottom: 0.5rem;
            border-bottom: 2px solid #f6d55c;
        }

        .form-group {
            margin-bottom: 1.5rem;
        }

        .form-group label {
            display: block;
            margin-bottom: 0.5rem;
            color: #495057;
            font-weight: 500;
        }

        .form-group input,
        .form-group textarea,
        .form-group select {
            width: 100%;
            padding: 0.75rem;
            border: 1px solid #ddd;
            border-radius: 6px;
            font-size: 1rem;
            transition: border-color 0.3s ease;
            background-color: white;
        }

        .form-group input:focus,
        .form-group textarea:focus,
        .form-group select:focus {
            outline: none;
            border-color: #4299e1;
            box-shadow: 0 0 0 3px rgba(66, 153, 225, 0.1);
        }

        .form-group select {
            cursor: pointer;
        }

        /* Button Styles */
        .btn-primary {
            background-color: #1e3a5f;
            border-color: #1e3a5f;
            color: white;
            font-weight: 500;
            padding: 0.75rem 1.5rem;
            border-radius: 6px;
            transition: all 0.3s ease;
        }

        .btn-primary:hover {
            background-color: #2c5282;
            border-color: #2c5282;
            transform: translateY(-1px);
        }

        .btn-secondary {
            background-color: #6c757d;
            border-color: #6c757d;
            color: white;
            font-weight: 500;
            padding: 0.75rem 1.5rem;
            border-radius: 6px;
            transition: all 0.3s ease;
        }

        .btn-secondary:hover {
            background-color: #5a6268;
            border-color: #5a6268;
        }

        .btn-success {
            background-color: #28a745;
            border-color: #28a745;
            color: white;
            font-weight: 500;
            padding: 0.75rem 1.5rem;
            border-radius: 6px;
            transition: all 0.3s ease;
        }

        .btn-success:hover {
            background-color: #218838;
            border-color: #218838;
        }

        .btn-danger {
            background-color: #dc3545;
            border-color: #dc3545;
            color: white;
            font-weight: 500;
            padding: 0.75rem 1.5rem;
            border-radius: 6px;
            transition: all 0.3s ease;
        }

        .btn-danger:hover {
            background-color: #c82333;
            border-color: #c82333;
        }

        /* Loading States */
        .loading-state {
            text-align: center;
            padding: 3rem;
            color: #6c757d;
        }

        .loading-state i {
            font-size: 3rem;
            margin-bottom: 1rem;
            animation: spin 1s linear infinite;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        .error-state {
            text-align: center;
            padding: 3rem;
            color: #dc3545;
        }

        .error-state i {
            font-size: 3rem;
            margin-bottom: 1rem;
        }

        /* Modules Section */
        .modules-container {
            display: grid;
            gap: 1rem;
        }

        .module-item {
            background: #f8f9fa;
            border: 1px solid #e9ecef;
            border-radius: 8px;
            padding: 1rem;
            display: flex;
            align-items: center;
            gap: 1rem;
        }

        .module-image {
            width: 80px;
            height: 60px;
            border-radius: 6px;
            object-fit: cover;
            background: #e9ecef;
        }

        .module-info {
            flex: 1;
        }

        .module-title {
            font-weight: 600;
            color: #1e3a5f;
            margin-bottom: 0.25rem;
        }

        .module-description {
            color: #6c757d;
            font-size: 0.9rem;
        }

        .module-actions {
            display: flex;
            gap: 0.5rem;
        }

        .module-actions button {
            padding: 0.25rem 0.5rem;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .edit-btn {
            background-color: #007bff;
            color: white;
        }

        .edit-btn:hover {
            background-color: #0056b3;
        }

        .delete-btn {
            background-color: #dc3545;
            color: white;
        }

        .delete-btn:hover {
            background-color: #c82333;
        }

        .move-btn {
            background-color: #6c757d;
            color: white;
        }

        .move-btn:hover {
            background-color: #5a6268;
        }

        .move-btn:disabled {
            opacity: 0.5;
            cursor: not-allowed;
        }

        /* Responsive Design */
        @media (max-width: 768px) {
            .main-content {
                padding: 1rem;
            }

            .page-header {
                padding: 1.5rem;
            }

            .course-info-card {
                padding: 1.5rem;
            }

            .form-section {
                padding: 1.5rem;
            }

            .course-meta {
                flex-direction: column;
                gap: 0.5rem;
            }

            .module-item {
                flex-direction: column;
                text-align: center;
            }

            .module-actions {
                justify-content: center;
            }
        }
    </style>
</head>
<body>
    <!-- Navbar -->
    <nav class="navbar navbar-expand-lg">
        <div class="container-fluid">
            <button class="btn d-lg-none me-3" id="sidebarToggle">
                <i class="bi bi-list text-white"></i>
            </button>
            
            <a class="navbar-brand d-flex align-items-center" href="https://abilityseminarsgroup.com/admin-dashboard/">
                <img src="https://abilityseminarsgroup.com/wp-content/uploads/2023/09/Asset-6-4-768x355.png" 
                     alt="AbilitySeminarsGroup" style="width:130px;height:auto;">
            </a>
            
            <div class="navbar-nav ms-auto">
                <a class="nav-link" href="https://abilityseminarsgroup.com/admin-dashboard/">
                    <i class="bi bi-house-door me-1"></i>Dashboard
                </a>
                <a class="nav-link" href="https://abilityseminarsgroup.com/all-courses/">
                    <i class="bi bi-arrow-left me-1"></i>Back to Courses
                </a>
            </div>
        </div>
    </nav>

    <!-- Sidebar -->
    <div class="sidebar" id="sidebar">
        <div class="sidebar-header">
            <h5><i class="bi bi-gear me-2"></i>Course Management</h5>
        </div>
        <div class="sidebar-menu">
            <a href="https://abilityseminarsgroup.com/admin-dashboard/" class="menu-item">
                <i class="bi bi-speedometer2"></i>Dashboard
            </a>
            <a href="https://abilityseminarsgroup.com/all-courses/" class="menu-item">
                <i class="bi bi-collection"></i>All Courses
            </a>
            <a href="https://abilityseminarsgroup.com/new-course/" class="menu-item">
                <i class="bi bi-plus-circle"></i>New Course
            </a>
            <a href="#" class="menu-item" style="background: #f8f9fa; color: #1e3a5f; font-weight: 600;">
                <i class="bi bi-pencil-square"></i>Edit Course
            </a>
        </div>
    </div>

    <!-- Sidebar Overlay -->
    <div class="sidebar-overlay" id="sidebarOverlay"></div>

    <!-- Main Content -->
    <div class="main-content">
        <!-- Page Header -->
        <div class="page-header">
            <h1 class="page-title">
                <i class="bi bi-pencil-square me-2"></i>Edit Course
            </h1>
            <p class="page-subtitle">Modify course information, modules, and settings</p>
        </div>

        <!-- Loading State -->
        <div id="loadingState" class="loading-state">
            <i class="bi bi-arrow-clockwise"></i>
            <h3>Loading Course Data...</h3>
            <p>Please wait while we fetch the course information.</p>
        </div>

        <!-- Error State -->
        <div id="errorState" class="error-state" style="display: none;">
            <i class="bi bi-exclamation-triangle"></i>
            <h3>Error Loading Course</h3>
            <p id="errorMessage">Unable to load course data. Please try again.</p>
            <button class="btn btn-primary" onclick="location.reload()">
                <i class="bi bi-arrow-clockwise me-1"></i>Retry
            </button>
        </div>

        <!-- Course Content (Hidden initially) -->
        <div id="courseContent" style="display: none;">
            <!-- Course Info Card -->
            <div class="course-info-card">
                <div class="row">
                    <div class="col-md-4">
                        <img id="courseImage" src="" alt="Course Image" class="course-image">
                    </div>
                    <div class="col-md-8">
                        <h2 id="courseTitle" class="course-title">Course Title</h2>
                        <p id="courseDescription" class="course-description">Course description will appear here...</p>
                        <div class="course-meta">
                            <div class="meta-item">
                                <i class="bi bi-calendar"></i>
                                <span id="courseDate">Date</span>
                            </div>
                            <div class="meta-item">
                                <i class="bi bi-tag"></i>
                                <span id="courseCategory">Category</span>
                            </div>
                            <div class="meta-item">
                                <i class="bi bi-currency-dollar"></i>
                                <span id="coursePrice">Price</span>
                            </div>
                            <div class="meta-item">
                                <i class="bi bi-eye"></i>
                                <span id="courseStatus">Status</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Basic Information Form -->
            <div class="form-section">
                <h3 class="section-title">
                    <i class="bi bi-info-circle me-2"></i>Basic Information
                </h3>
                <form id="basicInfoForm">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="title">Course Title *</label>
                                <input type="text" id="title" name="title" required>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="category">Category *</label>
                                <select id="category" name="category" required>
                                    <option value="">Select Category</option>
                                    <option value="technology">Technology</option>
                                    <option value="business">Business</option>
                                    <option value="design">Design</option>
                                    <option value="marketing">Marketing</option>
                                    <option value="personal-development">Personal Development</option>
                                    <option value="health">Health & Wellness</option>
                                    <option value="language">Language</option>
                                    <option value="other">Other</option>
                                </select>
                            </div>
                        </div>
                    </div>

                    <div class="form-group">
                        <label for="description">Description *</label>
                        <textarea id="description" name="description" rows="4" required></textarea>
                    </div>

                    <div class="row">
                        <div class="col-md-4">
                            <div class="form-group">
                                <label for="price">Price ($) *</label>
                                <input type="number" id="price" name="price" min="0" step="0.01" required>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="form-group">
                                <label for="duration">Duration (hours)</label>
                                <input type="number" id="duration" name="duration" min="0" step="0.5">
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="form-group">
                                <label for="status">Status *</label>
                                <select id="status" name="status" required>
                                    <option value="draft">Draft</option>
                                    <option value="published">Published</option>
                                    <option value="archived">Archived</option>
                                </select>
                            </div>
                        </div>
                    </div>

                    <div class="form-group">
                        <label for="image_url">Course Image URL</label>
                        <input type="url" id="image_url" name="image_url" placeholder="https://example.com/image.jpg">
                    </div>

                    <div class="d-flex gap-2">
                        <button type="submit" class="btn btn-primary">
                            <i class="bi bi-check-circle me-1"></i>Save Basic Info
                        </button>
                        <button type="button" class="btn btn-secondary" onclick="resetForm()">
                            <i class="bi bi-arrow-clockwise me-1"></i>Reset
                        </button>
                    </div>
                </form>
            </div>

            <!-- Modules Section -->
            <div class="form-section">
                <h3 class="section-title">
                    <i class="bi bi-collection me-2"></i>Course Modules
                </h3>

                <div class="d-flex justify-content-between align-items-center mb-3">
                    <p class="text-muted mb-0">Manage your course modules and their order</p>
                    <button class="btn btn-success" onclick="addNewModule()">
                        <i class="bi bi-plus-circle me-1"></i>Add Module
                    </button>
                </div>

                <div id="modulesContainer" class="modules-container">
                    <!-- Modules will be loaded here -->
                </div>
            </div>

            <!-- Action Buttons -->
            <div class="form-section">
                <div class="d-flex gap-2 justify-content-between">
                    <div>
                        <button class="btn btn-success" onclick="saveAllChanges()">
                            <i class="bi bi-check-circle me-1"></i>Save All Changes
                        </button>
                        <button class="btn btn-secondary" onclick="previewCourse()">
                            <i class="bi bi-eye me-1"></i>Preview Course
                        </button>
                    </div>
                    <div>
                        <button class="btn btn-danger" onclick="deleteCourse()">
                            <i class="bi bi-trash me-1"></i>Delete Course
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.3/dist/js/bootstrap.bundle.min.js"></script>

    <script>
        /**
         * ========================================
         * SISTEMA DE EDICIÓN DE CURSOS - JAVASCRIPT
         * ========================================
         *
         * Funcionalidades principales:
         * - Carga de datos del curso desde la API
         * - Edición de información básica
         * - Gestión de módulos
         * - Guardado de cambios
         * - Navegación y UI
         */

        // Variables globales
        let currentCourse = null;
        let courseModules = [];
        let hasUnsavedChanges = false;

        /**
         * Inicialización de la aplicación
         */
        document.addEventListener('DOMContentLoaded', function() {
            console.log('🚀 Iniciando sistema de edición de cursos...');

            // Configurar sidebar
            setupSidebar();

            // Obtener ID del curso desde URL
            const urlParams = new URLSearchParams(window.location.search);
            const courseId = urlParams.get('id');

            if (courseId) {
                console.log('📚 ID de curso detectado:', courseId);
                loadCourseData(courseId);
            } else {
                console.error('❌ No se proporcionó ID de curso');
                showError('No course ID provided. Please select a course to edit.');
                setTimeout(() => {
                    window.location.href = 'https://abilityseminarsgroup.com/all-courses/';
                }, 3000);
            }

            // Configurar formularios
            setupForms();

            // Advertencia de cambios no guardados
            window.addEventListener('beforeunload', function(e) {
                if (hasUnsavedChanges) {
                    e.preventDefault();
                    e.returnValue = '';
                }
            });
        });

        /**
         * Configurar sidebar y navegación
         */
        function setupSidebar() {
            const sidebarToggle = document.getElementById('sidebarToggle');
            const sidebar = document.getElementById('sidebar');
            const sidebarOverlay = document.getElementById('sidebarOverlay');

            if (sidebarToggle) {
                sidebarToggle.addEventListener('click', function() {
                    sidebar.classList.toggle('active');
                    sidebarOverlay.classList.toggle('active');
                });
            }

            if (sidebarOverlay) {
                sidebarOverlay.addEventListener('click', function() {
                    sidebar.classList.remove('active');
                    sidebarOverlay.classList.remove('active');
                });
            }
        }

        /**
         * Cargar datos del curso desde la API
         */
        async function loadCourseData(courseId) {
            try {
                console.log('📡 Cargando datos del curso:', courseId);

                showLoading();

                const response = await fetch(`https://abilityseminarsgroup.com/wp-json/asg/v1/courses/${courseId}`);
                const result = await response.json();

                console.log('📊 Respuesta de la API:', result);

                if (result.success && result.data) {
                    currentCourse = result.data;
                    courseModules = result.data.modules || [];

                    console.log('✅ Curso cargado exitosamente:', currentCourse);

                    populateCourseData(currentCourse);
                    hideLoading();

                } else {
                    throw new Error(result.message || 'Failed to load course data');
                }

            } catch (error) {
                console.error('❌ Error cargando curso:', error);
                showError(`Error loading course: ${error.message}`);
            }
        }

        /**
         * Poblar los datos del curso en la interfaz
         */
        function populateCourseData(course) {
            console.log('🎨 Poblando datos en la interfaz:', course);

            // Información del header
            document.getElementById('courseTitle').textContent = course.title || 'Untitled Course';
            document.getElementById('courseDescription').textContent = course.description || 'No description available';
            document.getElementById('courseDate').textContent = formatDate(course.created_at);
            document.getElementById('courseCategory').textContent = course.category || 'Uncategorized';
            document.getElementById('coursePrice').textContent = `$${course.price || '0.00'}`;
            document.getElementById('courseStatus').textContent = course.status || 'draft';

            // Imagen del curso
            const courseImage = document.getElementById('courseImage');
            if (course.image_url) {
                courseImage.src = course.image_url;
                courseImage.style.display = 'block';
            } else {
                courseImage.style.display = 'none';
            }

            // Formulario básico
            document.getElementById('title').value = course.title || '';
            document.getElementById('description').value = course.description || '';
            document.getElementById('category').value = course.category || '';
            document.getElementById('price').value = course.price || '';
            document.getElementById('duration').value = course.duration || '';
            document.getElementById('status').value = course.status || 'draft';
            document.getElementById('image_url').value = course.image_url || '';

            // Cargar módulos
            loadModules();
        }

        /**
         * Cargar módulos del curso
         */
        function loadModules() {
            console.log('📚 Cargando módulos:', courseModules);

            const container = document.getElementById('modulesContainer');

            if (!courseModules || courseModules.length === 0) {
                container.innerHTML = `
                    <div class="text-center py-4">
                        <i class="bi bi-collection text-muted" style="font-size: 3rem;"></i>
                        <h5 class="text-muted mt-2">No modules yet</h5>
                        <p class="text-muted">Add your first module to get started</p>
                        <button class="btn btn-primary" onclick="addNewModule()">
                            <i class="bi bi-plus-circle me-1"></i>Add First Module
                        </button>
                    </div>
                `;
                return;
            }

            container.innerHTML = '';

            courseModules.forEach((module, index) => {
                const moduleElement = createModuleElement(module, index);
                container.appendChild(moduleElement);
            });
        }

        /**
         * Crear elemento de módulo
         */
        function createModuleElement(module, index) {
            const moduleDiv = document.createElement('div');
            moduleDiv.className = 'module-item';
            moduleDiv.style.cssText = `
                display: flex;
                align-items: center;
                gap: 1rem;
                padding: 1rem;
                border: 1px solid #e9ecef;
                border-radius: 8px;
                margin-bottom: 1rem;
                background: #f8f9fa;
            `;

            moduleDiv.innerHTML = `
                <div style="width: 80px; height: 60px; border-radius: 6px; overflow: hidden; background: #e9ecef; display: flex; align-items: center; justify-content: center;">
                    ${module.image_url ?
                        `<img src="${module.image_url}" alt="Module Image" style="width: 100%; height: 100%; object-fit: cover;">` :
                        '<i class="bi bi-image text-muted" style="font-size: 1.5rem;"></i>'
                    }
                </div>
                <div style="flex: 1;">
                    <div style="font-weight: 600; color: #1e3a5f; margin-bottom: 0.25rem;">${module.title || 'Untitled Module'}</div>
                    <div style="color: #6c757d; font-size: 0.9rem;">${module.description || 'No description'}</div>
                </div>
                <div style="display: flex; gap: 0.5rem;">
                    <button onclick="editModule(${index})" style="padding: 0.25rem 0.5rem; border: none; border-radius: 4px; background-color: #007bff; color: white; cursor: pointer;" title="Edit Module">
                        <i class="bi bi-pencil"></i>
                    </button>
                    <button onclick="moveModuleUp(${index})" ${index === 0 ? 'disabled' : ''} style="padding: 0.25rem 0.5rem; border: none; border-radius: 4px; background-color: #6c757d; color: white; cursor: pointer;" title="Move Up">
                        <i class="bi bi-arrow-up"></i>
                    </button>
                    <button onclick="moveModuleDown(${index})" ${index === courseModules.length - 1 ? 'disabled' : ''} style="padding: 0.25rem 0.5rem; border: none; border-radius: 4px; background-color: #6c757d; color: white; cursor: pointer;" title="Move Down">
                        <i class="bi bi-arrow-down"></i>
                    </button>
                    <button onclick="deleteModule(${index})" style="padding: 0.25rem 0.5rem; border: none; border-radius: 4px; background-color: #dc3545; color: white; cursor: pointer;" title="Delete Module">
                        <i class="bi bi-trash"></i>
                    </button>
                </div>
            `;
            return moduleDiv;
        }

        /**
         * Configurar formularios
         */
        function setupForms() {
            const basicInfoForm = document.getElementById('basicInfoForm');

            if (basicInfoForm) {
                basicInfoForm.addEventListener('submit', async function(e) {
                    e.preventDefault();
                    await saveBasicInfo();
                });

                // Detectar cambios
                const inputs = basicInfoForm.querySelectorAll('input, textarea, select');
                inputs.forEach(input => {
                    input.addEventListener('change', () => {
                        hasUnsavedChanges = true;
                    });
                });
            }
        }

        /**
         * Guardar información básica
         */
        async function saveBasicInfo() {
            try {
                console.log('💾 Guardando información básica...');

                const formData = new FormData(document.getElementById('basicInfoForm'));
                const courseData = {
                    title: formData.get('title'),
                    description: formData.get('description'),
                    category: formData.get('category'),
                    price: parseFloat(formData.get('price')) || 0,
                    duration: parseFloat(formData.get('duration')) || 0,
                    status: formData.get('status'),
                    image_url: formData.get('image_url') || null
                };

                console.log('📊 Datos a enviar:', courseData);

                const response = await fetch(`https://abilityseminarsgroup.com/wp-json/asg/v1/courses/${currentCourse.course_id}`, {
                    method: 'PUT',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify(courseData)
                });

                const result = await response.json();
                console.log('📡 Respuesta del servidor:', result);

                if (result.success) {
                    // Actualizar datos locales
                    Object.assign(currentCourse, courseData);
                    populateCourseData(currentCourse);

                    hasUnsavedChanges = false;
                    showAlert('success', 'Course information updated successfully!');

                } else {
                    throw new Error(result.message || 'Failed to update course');
                }

            } catch (error) {
                console.error('❌ Error guardando información básica:', error);
                showAlert('danger', `Error updating course: ${error.message}`);
            }
        }

        /**
         * Agregar nuevo módulo
         */
        function addNewModule() {
            const title = prompt('Enter module title:');
            if (!title) return;

            const description = prompt('Enter module description (optional):') || '';

            const newModule = {
                title: title,
                description: description,
                image_url: null,
                order_index: courseModules.length
            };

            courseModules.push(newModule);
            loadModules();
            hasUnsavedChanges = true;

            console.log('➕ Módulo agregado:', newModule);
        }

        /**
         * Editar módulo
         */
        function editModule(index) {
            const module = courseModules[index];
            if (!module) return;

            const newTitle = prompt('Edit module title:', module.title);
            if (newTitle === null) return;

            const newDescription = prompt('Edit module description:', module.description || '');
            if (newDescription === null) return;

            const newImageUrl = prompt('Edit module image URL (optional):', module.image_url || '');

            module.title = newTitle;
            module.description = newDescription;
            module.image_url = newImageUrl || null;

            loadModules();
            hasUnsavedChanges = true;

            console.log('✏️ Módulo editado:', module);
        }

        /**
         * Eliminar módulo
         */
        function deleteModule(index) {
            const module = courseModules[index];
            if (!module) return;

            if (!confirm(`Are you sure you want to delete the module "${module.title}"?`)) {
                return;
            }

            courseModules.splice(index, 1);

            // Reordenar índices
            courseModules.forEach((mod, idx) => {
                mod.order_index = idx;
            });

            loadModules();
            hasUnsavedChanges = true;

            console.log('🗑️ Módulo eliminado:', module);
        }

        /**
         * Mover módulo hacia arriba
         */
        function moveModuleUp(index) {
            if (index <= 0) return;

            const temp = courseModules[index];
            courseModules[index] = courseModules[index - 1];
            courseModules[index - 1] = temp;

            // Actualizar índices
            courseModules[index].order_index = index;
            courseModules[index - 1].order_index = index - 1;

            loadModules();
            hasUnsavedChanges = true;

            console.log('⬆️ Módulo movido hacia arriba');
        }

        /**
         * Mover módulo hacia abajo
         */
        function moveModuleDown(index) {
            if (index >= courseModules.length - 1) return;

            const temp = courseModules[index];
            courseModules[index] = courseModules[index + 1];
            courseModules[index + 1] = temp;

            // Actualizar índices
            courseModules[index].order_index = index;
            courseModules[index + 1].order_index = index + 1;

            loadModules();
            hasUnsavedChanges = true;

            console.log('⬇️ Módulo movido hacia abajo');
        }

        /**
         * Guardar todos los cambios
         */
        async function saveAllChanges() {
            try {
                console.log('💾 Guardando todos los cambios...');

                // Guardar información básica
                await saveBasicInfo();

                // Guardar módulos si hay cambios
                if (courseModules.length > 0) {
                    await saveModules();
                }

                showAlert('success', 'All changes saved successfully!');
                hasUnsavedChanges = false;

            } catch (error) {
                console.error('❌ Error guardando cambios:', error);
                showAlert('danger', `Error saving changes: ${error.message}`);
            }
        }

        /**
         * Guardar módulos
         */
        async function saveModules() {
            try {
                console.log('📚 Guardando módulos...');

                const response = await fetch(`https://abilityseminarsgroup.com/wp-json/asg/v1/courses/${currentCourse.course_id}/modules`, {
                    method: 'PUT',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({ modules: courseModules })
                });

                const result = await response.json();

                if (!result.success) {
                    throw new Error(result.message || 'Failed to save modules');
                }

                console.log('✅ Módulos guardados exitosamente');

            } catch (error) {
                console.error('❌ Error guardando módulos:', error);
                throw error;
            }
        }

        /**
         * Vista previa del curso
         */
        function previewCourse() {
            if (hasUnsavedChanges) {
                if (!confirm('You have unsaved changes. Do you want to continue without saving?')) {
                    return;
                }
            }

            // Abrir en nueva ventana/pestaña
            window.open(`https://abilityseminarsgroup.com/course-preview/?id=${currentCourse.course_id}`, '_blank');
        }

        /**
         * Eliminar curso
         */
        async function deleteCourse() {
            if (!confirm('Are you sure you want to delete this course? This action cannot be undone.')) {
                return;
            }

            if (!confirm('This will permanently delete the course and all its modules. Are you absolutely sure?')) {
                return;
            }

            try {
                console.log('🗑️ Eliminando curso...');

                const response = await fetch(`https://abilityseminarsgroup.com/wp-json/asg/v1/courses/${currentCourse.course_id}`, {
                    method: 'DELETE'
                });

                const result = await response.json();

                if (result.success) {
                    showAlert('success', 'Course deleted successfully!');
                    setTimeout(() => {
                        window.location.href = 'https://abilityseminarsgroup.com/all-courses/';
                    }, 2000);
                } else {
                    throw new Error(result.message || 'Failed to delete course');
                }

            } catch (error) {
                console.error('❌ Error eliminando curso:', error);
                showAlert('danger', `Error deleting course: ${error.message}`);
            }
        }

        /**
         * Resetear formulario
         */
        function resetForm() {
            if (hasUnsavedChanges) {
                if (!confirm('This will reset all unsaved changes. Are you sure?')) {
                    return;
                }
            }

            if (currentCourse) {
                populateCourseData(currentCourse);
                hasUnsavedChanges = false;
                showAlert('info', 'Form reset to last saved state');
            }
        }

        /**
         * Mostrar estado de carga
         */
        function showLoading() {
            document.getElementById('loadingState').style.display = 'block';
            document.getElementById('errorState').style.display = 'none';
            document.getElementById('courseContent').style.display = 'none';
        }

        /**
         * Ocultar estado de carga
         */
        function hideLoading() {
            document.getElementById('loadingState').style.display = 'none';
            document.getElementById('courseContent').style.display = 'block';
        }

        /**
         * Mostrar error
         */
        function showError(message) {
            document.getElementById('loadingState').style.display = 'none';
            document.getElementById('courseContent').style.display = 'none';
            document.getElementById('errorState').style.display = 'block';
            document.getElementById('errorMessage').textContent = message;
        }

        /**
         * Mostrar alerta
         */
        function showAlert(type, message) {
            // Crear elemento de alerta
            const alertDiv = document.createElement('div');
            alertDiv.className = `alert alert-${type} alert-dismissible fade show position-fixed`;
            alertDiv.style.cssText = 'top: 90px; right: 20px; z-index: 9999; min-width: 300px;';
            alertDiv.innerHTML = `
                ${message}
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            `;

            document.body.appendChild(alertDiv);

            // Auto-remover después de 5 segundos
            setTimeout(() => {
                if (alertDiv.parentNode) {
                    alertDiv.remove();
                }
            }, 5000);
        }

        /**
         * Formatear fecha
         */
        function formatDate(dateString) {
            if (!dateString) return 'N/A';

            try {
                const date = new Date(dateString);
                return date.toLocaleDateString('en-US', {
                    year: 'numeric',
                    month: 'short',
                    day: 'numeric'
                });
            } catch (error) {
                return 'Invalid Date';
            }
        }

        // Funciones de utilidad adicionales
        console.log('✅ Sistema de edición de cursos cargado correctamente');
    </script>
</body>
</html>
