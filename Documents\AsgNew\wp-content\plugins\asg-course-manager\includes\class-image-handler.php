<?php
/**
 * Manejador de imágenes para cursos ASG
 * 
 * Gestiona la subida y procesamiento de imágenes
 */

if (!defined('ABSPATH')) {
    exit;
}

class ASG_Image_Handler {
    
    private $allowed_types;
    private $max_file_size;
    private $upload_dir;
    
    public function __construct() {
        // Tipos de archivo permitidos
        $this->allowed_types = [
            'image/jpeg',
            'image/jpg', 
            'image/png',
            'image/webp'
        ];
        
        // Tamaño máximo: 5MB
        $this->max_file_size = 5 * 1024 * 1024;
        
        // Directorio de subida
        $upload_dir = wp_upload_dir();
        $this->upload_dir = $upload_dir['basedir'] . '/asg-courses/';
        
        // Crear directorio si no existe
        if (!file_exists($this->upload_dir)) {
            wp_mkdir_p($this->upload_dir);
        }
    }
    
    /**
     * Subir imagen de curso
     * 
     * @param array $file Archivo $_FILES
     * @param string $type Tipo de imagen (course, module)
     * @return array|WP_Error Resultado de la subida
     */
    public function upload_image($file, $type = 'course') {
        try {
            // Validar archivo
            $validation = $this->validate_file($file);
            if (is_wp_error($validation)) {
                return $validation;
            }
            
            // Generar nombre único
            $file_extension = pathinfo($file['name'], PATHINFO_EXTENSION);
            $unique_filename = $this->generate_unique_filename($type, $file_extension);
            
            // Ruta completa del archivo
            $file_path = $this->upload_dir . $unique_filename;
            
            // Mover archivo subido
            if (!move_uploaded_file($file['tmp_name'], $file_path)) {
                throw new Exception('Error al mover el archivo subido');
            }
            
            // Crear diferentes tamaños de imagen
            $image_sizes = $this->create_image_sizes($file_path, $unique_filename);
            
            // Obtener URL base
            $upload_dir = wp_upload_dir();
            $base_url = $upload_dir['baseurl'] . '/asg-courses/';
            
            return [
                'success' => true,
                'data' => [
                    'filename' => $unique_filename,
                    'original' => $base_url . $unique_filename,
                    'sizes' => [
                        'thumbnail' => $base_url . $image_sizes['thumbnail'],
                        'medium' => $base_url . $image_sizes['medium'],
                        'large' => $base_url . $image_sizes['large']
                    ],
                    'file_size' => filesize($file_path),
                    'mime_type' => $file['type']
                ],
                'message' => 'Imagen subida exitosamente'
            ];
            
        } catch (Exception $e) {
            error_log('❌ ASG Image Handler - Error subiendo imagen: ' . $e->getMessage());
            
            return new WP_Error(
                'image_upload_failed',
                'Error al subir la imagen: ' . $e->getMessage(),
                ['status' => 500]
            );
        }
    }
    
    /**
     * Validar archivo subido
     * 
     * @param array $file Archivo $_FILES
     * @return bool|WP_Error True si es válido, WP_Error si no
     */
    private function validate_file($file) {
        // Verificar errores de subida
        if ($file['error'] !== UPLOAD_ERR_OK) {
            return new WP_Error(
                'upload_error',
                'Error en la subida del archivo: ' . $this->get_upload_error_message($file['error']),
                ['status' => 400]
            );
        }
        
        // Verificar tipo de archivo
        if (!in_array($file['type'], $this->allowed_types)) {
            return new WP_Error(
                'invalid_file_type',
                'Tipo de archivo no permitido. Solo se permiten: JPG, PNG, WebP',
                ['status' => 400]
            );
        }
        
        // Verificar tamaño
        if ($file['size'] > $this->max_file_size) {
            return new WP_Error(
                'file_too_large',
                'El archivo es demasiado grande. Máximo permitido: 5MB',
                ['status' => 400]
            );
        }
        
        // Verificar que es realmente una imagen
        $image_info = getimagesize($file['tmp_name']);
        if ($image_info === false) {
            return new WP_Error(
                'invalid_image',
                'El archivo no es una imagen válida',
                ['status' => 400]
            );
        }
        
        return true;
    }
    
    /**
     * Generar nombre único para el archivo
     * 
     * @param string $type Tipo de imagen
     * @param string $extension Extensión del archivo
     * @return string Nombre único
     */
    private function generate_unique_filename($type, $extension) {
        $timestamp = time();
        $random = wp_generate_password(8, false);
        return "asg-{$type}-{$timestamp}-{$random}.{$extension}";
    }
    
    /**
     * Crear diferentes tamaños de imagen
     * 
     * @param string $file_path Ruta del archivo original
     * @param string $filename Nombre del archivo
     * @return array Array con nombres de archivos de diferentes tamaños
     */
    private function create_image_sizes($file_path, $filename) {
        $sizes = [
            'thumbnail' => ['width' => 150, 'height' => 150],
            'medium' => ['width' => 400, 'height' => 300],
            'large' => ['width' => 800, 'height' => 600]
        ];
        
        $created_sizes = [];
        $file_info = pathinfo($filename);
        
        foreach ($sizes as $size_name => $dimensions) {
            $new_filename = $file_info['filename'] . '-' . $size_name . '.' . $file_info['extension'];
            $new_file_path = $this->upload_dir . $new_filename;
            
            // Redimensionar imagen
            $resized = $this->resize_image($file_path, $new_file_path, $dimensions['width'], $dimensions['height']);
            
            if ($resized) {
                $created_sizes[$size_name] = $new_filename;
            } else {
                // Si falla el redimensionado, usar la original
                $created_sizes[$size_name] = $filename;
            }
        }
        
        return $created_sizes;
    }
    
    /**
     * Redimensionar imagen
     * 
     * @param string $source_path Ruta de la imagen original
     * @param string $dest_path Ruta de destino
     * @param int $max_width Ancho máximo
     * @param int $max_height Alto máximo
     * @return bool True si se redimensionó correctamente
     */
    private function resize_image($source_path, $dest_path, $max_width, $max_height) {
        $image_info = getimagesize($source_path);
        if (!$image_info) {
            return false;
        }
        
        list($orig_width, $orig_height, $image_type) = $image_info;
        
        // Calcular nuevas dimensiones manteniendo proporción
        $ratio = min($max_width / $orig_width, $max_height / $orig_height);
        $new_width = intval($orig_width * $ratio);
        $new_height = intval($orig_height * $ratio);
        
        // Crear imagen desde el archivo original
        switch ($image_type) {
            case IMAGETYPE_JPEG:
                $source_image = imagecreatefromjpeg($source_path);
                break;
            case IMAGETYPE_PNG:
                $source_image = imagecreatefrompng($source_path);
                break;
            case IMAGETYPE_WEBP:
                $source_image = imagecreatefromwebp($source_path);
                break;
            default:
                return false;
        }
        
        if (!$source_image) {
            return false;
        }
        
        // Crear nueva imagen redimensionada
        $new_image = imagecreatetruecolor($new_width, $new_height);
        
        // Preservar transparencia para PNG
        if ($image_type == IMAGETYPE_PNG) {
            imagealphablending($new_image, false);
            imagesavealpha($new_image, true);
        }
        
        // Redimensionar
        imagecopyresampled(
            $new_image, $source_image,
            0, 0, 0, 0,
            $new_width, $new_height,
            $orig_width, $orig_height
        );
        
        // Guardar imagen redimensionada
        $result = false;
        switch ($image_type) {
            case IMAGETYPE_JPEG:
                $result = imagejpeg($new_image, $dest_path, 85);
                break;
            case IMAGETYPE_PNG:
                $result = imagepng($new_image, $dest_path, 8);
                break;
            case IMAGETYPE_WEBP:
                $result = imagewebp($new_image, $dest_path, 85);
                break;
        }
        
        // Limpiar memoria
        imagedestroy($source_image);
        imagedestroy($new_image);
        
        return $result;
    }
    
    /**
     * Obtener mensaje de error de subida
     * 
     * @param int $error_code Código de error
     * @return string Mensaje de error
     */
    private function get_upload_error_message($error_code) {
        $messages = [
            UPLOAD_ERR_INI_SIZE => 'El archivo excede el tamaño máximo permitido',
            UPLOAD_ERR_FORM_SIZE => 'El archivo excede el tamaño máximo del formulario',
            UPLOAD_ERR_PARTIAL => 'El archivo se subió parcialmente',
            UPLOAD_ERR_NO_FILE => 'No se subió ningún archivo',
            UPLOAD_ERR_NO_TMP_DIR => 'Falta el directorio temporal',
            UPLOAD_ERR_CANT_WRITE => 'Error al escribir el archivo en disco',
            UPLOAD_ERR_EXTENSION => 'Subida detenida por extensión'
        ];
        
        return $messages[$error_code] ?? 'Error desconocido en la subida';
    }
}
